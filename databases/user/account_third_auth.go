package user

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	df "gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
)

// AccountThirdAuth 用户第三方账号认证关联表
type AccountThirdAuth struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// UID
	UID int64 `xorm:"not null bigint(20) 'uid'"`
	// 登录方式
	LoginType int `xorm:"not null tinyint(4) 'login_type'"`
	// 第三方用户昵称
	Nickname string `xorm:"not null varchar(30) 'nickname'"`
	// 第三方账号唯一识别码
	ThirdUniqID string `xorm:"not null varchar(128) 'third_uniq_id'"`
	// 是否绑定
	IsBind int `xorm:"not null tinyint(1) 'is_bind'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (AccountThirdAuth) TableName() string {
	return "account_third_auth"
}

// Save 保存数据
func (a *AccountThirdAuth) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := df.GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *AccountThirdAuth) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := df.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

// UpdateByTran 事务更新数据
func (a *AccountThirdAuth) UpdateByTran(session *xorm.Session) error {
	a.UpdateTime = time.Now().Unix()
	_, err := session.ID(a.ID).Update(a)
	return err
}

type thirdAuth struct{}

// TbAccountThirdAuth 外部引用对象
var TbAccountThirdAuth thirdAuth

// GetThirdBindInfoByUID 通过UID获取第三方绑定信息
func (a *thirdAuth) GetThirdBindInfoByUID(uid int64) []*AccountThirdAuth {
	var tables []*AccountThirdAuth
	err := df.GetEngine().Where("uid = ? and is_bind = ?", uid, library.Yes).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetBindThirdUniqID 获取第三方账号的绑定信息
func (a *thirdAuth) GetBindThirdUniqID(thirdID string, loginType int) *AccountThirdAuth {
	var table AccountThirdAuth
	ok, err := df.GetEngine().Where("third_uniq_id = ? and login_type = ? and is_bind = ?",
		thirdID, loginType, library.Yes).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetBindThirdInfoByUID 通过UID获取第三方绑定信息
func (a *thirdAuth) GetBindThirdInfoByUID(uid int64, loginType int) *AccountThirdAuth {
	var table AccountThirdAuth
	ok, err := df.GetEngine().Where("uid = ? and login_type = ? and is_bind = ?", uid, loginType, library.Yes).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
