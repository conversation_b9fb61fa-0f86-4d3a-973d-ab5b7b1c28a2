package user

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
)

type GetVercode struct {
	ID         int64  `xorm:"int(11) notnull pk autoincr 'id'" json:"id"`
	Username   string `xorm:"varchar(128) notnull 'username'" json:"username"`
	Type       int    `xorm:"tinyint(3) notnull 'type'" json:"type"`
	Code       int    `xorm:"int(11) notnull 'code'" json:"code"`
	IsValid    int    `xorm:"tinyint(1) notnull 'is_valid'" json:"is_valid"`
	CreateTime int64  `xorm:"int(11) notnull 'create_time'" json:"create_time"`
	UpdateTime int64  `xorm:"int(11) notnull 'update_time'" json:"update_time"`
}

func (t *GetVercode) TableName() string {
	return "get_vercode"
}

// Save 保存数据
func (t *GetVercode) Save() error {
	t.CreateTime = time.Now().Unix()
	t.UpdateTime = t.CreateTime
	_, err := databases.GetEngineMaster().Insert(t)
	return err
}

// Update 更新数据
func (t *GetVercode) Update() error {
	t.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(t.ID).Update(t)
	return err
}

// IsCodeValid 检验验证码是否正确
func (t *GetVercode) IsCodeValid(inCode int) bool {
	if inCode == 0 {
		return false
	}
	if inCode != t.Code {
		return false
	}
	// 验证码有效期两小时
	currUnixTime := time.Now().Unix()
	return int(currUnixTime)-int(t.CreateTime) <= 7200 // nolint
}

type vercode struct{}

var TbVercode vercode

// GetVerCode 获取验证码
func (v *vercode) GetVerCodeTable(mobile string, codeType int) *GetVercode {
	var table GetVercode
	ok, err := databases.GetEngine().
		Where("username=? and type=? and is_valid = ?", mobile, codeType, library.Yes).Desc("create_time").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetTodayVerCodeList 获取当天获取验证码记录
func (v *vercode) GetTodayVerCodeList(mobile string, codeType int, start int64) []*GetVercode {
	var tables []*GetVercode
	err := databases.GetEngine().
		Where("username=? and type=? and create_time >= ?", mobile, codeType, start).Desc("create_time").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
