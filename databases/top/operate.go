package top

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	libp "gitlab.dailyyoga.com.cn/server/children-admin-api/library/operate"
)

type Operate struct {
	ID             int64  `xorm:"pk autoincr not null comment('主键') int(10) 'id'"`
	Title          string `xorm:"not null varchar(64) 'title'"`
	UserGroupType  int32  `xorm:"not null default 0 comment('用户分群类型') tinyint(2) 'user_group_type'"`
	UserGroupID    int64  `xorm:"not null default 0 comment('用户分群ID') int(10) 'user_group_id'"`
	LinkType       int32  `xorm:"not null comment('跳转类型') tinyint(3) 'link_type'"`
	LinkContent    string `xorm:"not null default '' comment('跳转内容') varchar(1024) 'link_content'"`
	LinkContentIOS string `xorm:"not null default '' comment('跳转内容') varchar(1024) 'link_content_ios'"`
	Sort           int32  `xorm:"not null comment('排序') int(10) 'sort'"`
	StartTime      int64  `xorm:"not null comment('开始时间') int(10) 'start_time'"`
	EndTime        int64  `xorm:"not null comment('结束时间') int(10) 'end_time'"`
	InfoImg        string `xorm:"not null default '' comment('图片地址') varchar(255) 'info_img'"`
	IsDel          int32  `xorm:"not null default 2 comment('是否删除 1是 2否') tinyint(1) 'is_del'"`
	IsOnline       int32  `xorm:"not null default 1 comment('是否在线 1是 2否') tinyint(1) 'is_online'"`
	IsIndexOpen    int32  `xorm:"not null default 2 comment('是否直接打开付费方案页 1是 2否') tinyint(1) 'is_index_open'"`
	CreateTime     int64  `xorm:"not null comment('创建时间') int(10) 'create_time'"`
	UpdateTime     int64  `xorm:"not null comment('更新时间') int(10) 'update_time'"`
}

func (t *Operate) TableName() string {
	return "top_operate"
}

// Save 保存数据
func (t *Operate) Save() error {
	t.CreateTime = time.Now().Unix()
	t.UpdateTime = t.CreateTime
	_, err := databases.GetEngineMaster().Insert(t)
	return err
}

// Update 更新数据
func (t *Operate) Update() error {
	t.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(t.ID).Update(t)
	return err
}

// Update 更新数据
func (t *Operate) UpdateMustCols() error {
	t.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(t.ID).MustCols("user_group_id",
		"link_type", "link_content", "info_img").Update(t)
	return err
}

type topOperate struct{}

// TbTopOperate 外部引用对象
var TbTopOperate topOperate

// GetItemByID 通过ID获取详情
func (a *topOperate) GetItemByID(id int64) *Operate {
	var table Operate
	ok, err := databases.GetEngine().Where("id = ?", id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (a *topOperate) GetListByPageTStatus(timeStatus, page, pageSize, userGroupID int) []*Operate {
	var tables []*Operate
	offset := (page - 1) * pageSize
	table := databases.GetEngine().Where("is_del = ?", library.No)
	// 1 未开始 2 是进行中 3已结束
	now := time.Now().Unix()
	if timeStatus == libp.NotStarted {
		table.Where("start_time > ?", now)
	} else if timeStatus == libp.HaveInHand {
		table.Where("start_time < ? and  end_time >= ?", now, now)
	} else if timeStatus == libp.Closed {
		table.Where("end_time < ?", now)
	}
	if userGroupID != -1 {
		table.Where("user_group_id = ?", userGroupID)
	}
	err := table.Desc("id").Limit(pageSize, offset).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (a *topOperate) GetAll() []*Operate {
	var tables []*Operate
	session := databases.GetEngine().NewSession()
	defer session.Close()
	err := session.Where("is_del = ? AND is_online = ?", library.No, library.Yes).
		Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
