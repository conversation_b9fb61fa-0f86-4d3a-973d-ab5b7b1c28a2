package order

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
)

type AlipayContractCharge struct {
	ID      int64  `xorm:"not null pk autoincr INT(11) 'id'"`
	OrderID string `xorm:"not null varchar(128) 'order_id'"`
	// 商品ID
	ProductID int64 `xorm:"not null int(10) 'product_id'"`
	// 用户UID
	UID int64 `xorm:"not null bigint(20) 'uid'"`
	// 商户签约号
	ContractCode string `xorm:"not null varchar(50) 'contract_code'"`
	// 用户签约成功后协议号
	ContractID string `xorm:"not null varchar(100) 'contract_id'"`
	// 扣款时间
	ChargeDate string `xorm:"not null varchar(10) 'charge_date'"`
	// 状态，0, 未支付，1：支付成功
	Status int `xorm:"not null tinyint(3) 'status'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (AlipayContractCharge) TableName() string {
	return "web_order_alipay_contract_charge"
}

// Save 保存数据
func (a *AlipayContractCharge) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *AlipayContractCharge) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

type alipayCharge struct{}

var TbAlipayCharge alipayCharge

func (*alipayCharge) GetItemByOrderID(orderID string) *AlipayContractCharge {
	var table AlipayContractCharge
	ok, err := databases.GetEngine().Where("order_id = ?", orderID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetChargeListByRange 获取用户扣款记录
func (*alipayCharge) GetChargeListByRange(uid, start, end int64, contractCode string) []*AlipayContractCharge {
	tables := make([]*AlipayContractCharge, 0)
	err := databases.GetEngine().Where("uid = ? and contract_code = ?", uid, contractCode).
		And("create_time >= ? and create_time <= ?", start, end).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetValidChargeByCode 根据签约号获取所有的扣款记录
func (*alipayCharge) GetValidChargeByCode(uid int64, contractCode string) []*AlipayContractCharge {
	tables := make([]*AlipayContractCharge, 0)
	err := databases.GetEngine().Where("uid = ? and contract_code = ? and status = ?",
		uid, contractCode, library.Yes).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*alipayCharge) GetLastSuccessCharge(contractCode string, maxID int64) *AlipayContractCharge {
	var table AlipayContractCharge
	ok, err := databases.GetEngine().Where("contract_code = ? and id < ?",
		contractCode, maxID).And("status = ?", library.Yes).OrderBy("id desc").Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetListByIDRange 根据id区间获取扣款记录
func (*alipayCharge) GetListByIDRange(contractCode string, minID, maxID int64) []*AlipayContractCharge {
	tables := make([]*AlipayContractCharge, 0)
	err := databases.GetEngine().Where("contract_code = ?", contractCode).
		And("id > ? and id < ?", minID, maxID).OrderBy("id asc").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
