package order

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/pay"
)

type AlipayContract struct {
	ID      int64  `xorm:"not null pk autoincr INT(11) 'id'"`
	OrderID string `xorm:"not null varchar(128) 'order_id'"`
	// 商品ID
	ProductID int64 `xorm:"not null int(10) 'product_id'"`
	// 用户UID
	UID int64 `xorm:"not null bigint(20) 'uid'"`
	// 变更类型：0-等待签约 1 签约 2解约
	ChangeType pay.ContractChangeType `xorm:"not null tinyint(3) 'change_type'"`
	// 商户签约号
	ContractCode string `xorm:"not null varchar(50) 'contract_code'"`
	// 用户签约成功后协议号
	ContractID string `xorm:"not null varchar(100) 'contract_id'"`
	// 支付宝商户app_id
	AppID string `xorm:"not null varchar(100) 'app_id'"`
	// 周期类型
	PeriodType string `xorm:"not null varchar(10) 'period_type'"`
	// 周期数
	Period int `xorm:"not null tinyint(3) 'period'"`
	// 首次扣款时间
	ExecuteTime string `xorm:"not null varchar(50) 'execute_time'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (AlipayContract) TableName() string {
	return "web_order_alipay_contract"
}

// Save 保存数据
func (a *AlipayContract) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *AlipayContract) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

type alipaucontract struct{}

var TbAlipayContract alipaucontract

func (*alipaucontract) GetItemByContractCode(code string) *AlipayContract {
	var table AlipayContract
	ok, err := databases.GetEngine().Where("contract_code = ?", code).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*alipaucontract) GetItemByOrderID(orderID string) *AlipayContract {
	var table AlipayContract
	ok, err := databases.GetEngine().Where("order_id = ?", orderID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetValidItemByTimeRange 获取时间段内是否有已签约的订单
func (*alipaucontract) GetValidItemByTimeRange(min, max int64) *AlipayContract {
	var table AlipayContract
	ok, err := databases.GetEngine().Where("create_time >= ? and create_time < ? ", min, max).
		And("change_type> ?", 0).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
