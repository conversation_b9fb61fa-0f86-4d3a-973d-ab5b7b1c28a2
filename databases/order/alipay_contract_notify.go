package order

import (
	"time"

	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
)

// AlipayContractNotify 支付宝签约回调记录
type AlipayContractNotify struct {
	ID int64 `xorm:"not null pk autoincr INT(11) 'id'"`
	// 商户签约号
	ContractCode string `xorm:"not null varchar(50) 'contract_code'"`
	// 用户签约成功后协议号
	ContractID string `xorm:"not null varchar(100) 'contract_id'"`
	// 变更类型:SIGN-签约,UNSIGN-解约
	ChangeType string `xorm:"not null varchar(20) 'change_type'"`
	// 签约时间
	SignTime string `xorm:"not null varchar(50) 'sign_time'"`
	// 回调时间
	NotifyTime string `xorm:"not null varchar(50) 'notify_time'"`
	// 支付宝唯一用户号
	AlipayUserID string `xorm:"not null varchar(50) 'alipay_user_id'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (AlipayContractNotify) TableName() string {
	return "web_order_alipay_contract_notify"
}

// Save 保存数据
func (a *AlipayContractNotify) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}
