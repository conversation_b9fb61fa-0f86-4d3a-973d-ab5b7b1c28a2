package order

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
)

// MiddleOrder 支付中台订单
type CompleteNotify struct {
	ID          int64  `xorm:"not null pk autoincr INT(11) 'id'"`
	OrderID     string `xorm:"not null varchar(128) 'order_id'"`
	PayType     int    `xorm:"not null tinyint(4) 'pay_type'"`
	Notify      string `xorm:"'request_data'"`
	IsProcessed int    `xorm:"not null tinyint(1) 'is_processed'"`
	CreateTime  int64  `xorm:"not null int(11) 'create_time'"`
	UpdateTime  int64  `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (CompleteNotify) TableName() string {
	return "order_complete_notify"
}

// Save 保存数据
func (d *CompleteNotify) Save() error {
	d.CreateTime = time.Now().Unix()
	d.UpdateTime = d.CreateTime

	_, err := databases.GetEngineMaster().Insert(d)
	return err
}

func (d *CompleteNotify) Update() error {
	d.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(d.ID).Update(d)
	return err
}

func (d *CompleteNotify) UpdateByTran(session *xorm.Session) error {
	d.UpdateTime = time.Now().Unix()
	_, err := session.ID(d.ID).Update(d)
	return err
}

type notify struct{}

var TbCompleteNofify notify

func (notify) GetItemByOrderID(orderID string) *CompleteNotify {
	var table CompleteNotify
	ok, err := databases.GetEngine().Where("order_id = ?", orderID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
