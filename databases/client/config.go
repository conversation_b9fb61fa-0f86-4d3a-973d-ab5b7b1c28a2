package client

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
)

type Config struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'" json:"id"`
	// 配置项
	Key string `xorm:"not null varchar(255) 'key'" json:"key"`
	// 配置项中文名
	KeyName string `xorm:"not null varchar(255) 'key_name'" json:"key_name"`
	// 配置项中文名
	Value string `xorm:"not null text 'value'" json:"value"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'" json:"create_time"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'" json:"update_time"`
}

// TableName 获取表名
func (Config) TableName() string {
	return "base_client_config"
}

// Save 保存数据
func (a *Config) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *Config) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

type config struct{}

var TbConfig config

// GetItemByKey 通过key获取详情
func (a *config) GetItemByKey(key string) *Config {
	var table Config

	ok, err := databases.GetEngine().Where("`key` = ?", key).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
