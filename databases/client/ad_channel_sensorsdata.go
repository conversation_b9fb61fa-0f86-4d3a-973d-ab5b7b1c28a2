package client

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
)

type AdChannelSensorsData struct {
	ID          int64  `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	DeviceID    string `xorm:"not null default '' VARCHAR(128) 'device_id'" json:"device_id"`
	OSType      int    `xorm:"not null default 2 tinyint(1) 'os_type'" json:"os_type"`
	AnonymousID string `xorm:"not null default '' VARCHAR(128) 'anonymous_id'" json:"anonymous_id"`
	UtmSource   string `xorm:"not null default '' VARCHAR(128) 'utm_source'" json:"utm_source"`
	CreateTime  int64  `xorm:"not null INT(11) 'create_time'" json:"create_time"`
	UpdateTime  int64  `xorm:"not null INT(11) 'update_time'" json:"update_time"`
}

// TableName 获取表名
func (a *AdChannelSensorsData) TableName() string {
	return "ad_channel_sensorsdata"
}

// Save 保存数据
func (a *AdChannelSensorsData) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

// Update 修改
func (a *AdChannelSensorsData) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(a.ID).MustCols("utm_source").Update(a)
	return err
}

type adSensorsData struct{}

// TbAdSensorsData 外部调用对象
var TbAdSensorsData adSensorsData

func (p *adSensorsData) GetItemByDeviceID(deviceID string) *AdChannelSensorsData {
	var t AdChannelSensorsData
	ok, err := databases.GetEngine().Where("device_id = ?", deviceID).Get(&t)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &t
}
