package client

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
)

type AdUIDChannel struct {
	ID         int64  `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	UID        int64  `xorm:"not null default 0 BIGINT(20) 'uid'" json:"uid"`
	DeviceID   string `xorm:"not null default '' VARCHAR(128) 'device_id'" json:"device_id"`
	UtmSource  string `xorm:"not null default '' VARCHAR(128) 'utm_source'" json:"utm_source"`
	CreateTime int64  `xorm:"not null INT(11) 'create_time'" json:"create_time"`
	UpdateTime int64  `xorm:"not null INT(11) 'update_time'" json:"update_time"`
}

// TableName 获取表名
func (a *AdUIDChannel) TableName() string {
	return "ad_uid_channel"
}

// Save 保存数据
func (a *AdUIDChannel) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

// Update 修改
func (a *AdUIDChannel) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(a.ID).MustCols("utm_source").Update(a)
	return err
}

type adUIDChannel struct{}

// TbAdUIDChannel 外部调用对象
var TbAdUIDChannel adUIDChannel

func (a *adUIDChannel) GetItemByDeviceID(deviceID string) *AdUIDChannel {
	var t AdUIDChannel
	ok, err := databases.GetEngine().Where("device_id = ?", deviceID).Get(&t)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &t
}

func (a *adUIDChannel) GetItemByUID(uid int64) *AdUIDChannel {
	var t AdUIDChannel
	ok, err := databases.GetEngine().Where("uid = ?", uid).Get(&t)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &t
}
