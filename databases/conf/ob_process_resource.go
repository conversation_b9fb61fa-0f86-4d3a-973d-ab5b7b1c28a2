package conf

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
)

type ObProcessResource struct {
	ID               int64  `xorm:"not null autoincr pk int(11) unsigned 'id'" json:"id"`       // 主键
	Name             string `xorm:"not null varchar(64) 'name'" json:"name"`                    // 流程名称
	PageList         string `xorm:"not null text 'page_list'" json:"page_list"`                 // 图片编号顺序json
	TransitionVideos string `xorm:"not null text 'transition_videos'" json:"transition_videos"` // 加载页视频json
	IsDel            int32  `xorm:"not null tinyint(1) 'is_del'" json:"is_del"`                 // 是否删除 1是 2否
	CreateTime       int64  `xorm:"not null int(11) 'create_time'" json:"create_time"`          // 创建时间
	UpdateTime       int64  `xorm:"not null int(11) 'update_time'" json:"update_time"`          // 更新时间
	PlanPageID       int64  `xorm:"not null default 0 int(11) 'plan_page_id'"`                  // 计划生成页
	ObType           int    `xorm:"not null default 1 int(11) 'ob_type'"`                       // ob 类型
}

func (o *ObProcessResource) TableName() string {
	return "ob_process_resource"
}

func (o *ObProcessResource) Save() error {
	o.CreateTime = time.Now().Unix()
	o.UpdateTime = o.CreateTime
	_, err := databases.GetEngineMaster().Insert(o)
	return err
}

func (o *ObProcessResource) Update() error {
	o.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(o.ID).MustCols("name", "plan_page_id").Update(o)
	return err
}

type obconf struct {
}

var TbObProcessResource obconf

func (*obconf) GetList(page, pageSize, obType int) []*ObProcessResource {
	var tables []*ObProcessResource
	offset := (page - 1) * pageSize
	session := databases.GetEngine().Select("id, name, create_time, update_time, plan_page_id,ob_type").
		Where("is_del = ?", library.No)
	if obType != 0 {
		session.Where("ob_type = ?", obType)
	}
	err := session.Limit(pageSize, offset).Desc("id").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*obconf) GetItemByID(id int64) *ObProcessResource {
	var table ObProcessResource
	ok, err := databases.GetEngine().ID(id).Where("is_del = ?", library.No).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*obconf) GetAll() []*ObProcessResource {
	var tables []*ObProcessResource
	err := databases.GetEngine().Where("is_del = ? and ob_type = ?", library.No, library.Yes).
		Desc("id").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (a *obconf) GetListMap() map[int64]*ObProcessResource {
	resp := make(map[int64]*ObProcessResource)
	var tables []*ObProcessResource
	err := databases.GetEngine().Where("is_del= ?", library.No).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	for _, v := range tables {
		resp[v.ID] = v
	}
	return resp
}

func (*obconf) GetAllByObType(obType int) []*ObProcessResource {
	var tables []*ObProcessResource
	err := databases.GetEngine().Where("is_del = ? and ob_type = ?", library.No, obType).
		Desc("id").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
