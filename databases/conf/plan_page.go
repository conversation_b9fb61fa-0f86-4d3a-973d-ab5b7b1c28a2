package conf

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
)

type PlanPage struct {
	ID           int64  `xorm:"pk autoincr not null comment('自增ID') int(11) 'id'"`
	PlanPageName string `xorm:"not null default '' comment('名称') varchar(64) 'plan_page_name'"`
	BgColor      string `xorm:"not null default '' comment('名称') varchar(64) 'bg_color'"`
	Content      string `xorm:"not null comment('计划生成页内容') text 'content'"`
	Status       int64  `xorm:"'status'"`
	CreateTime   int64  `xorm:"not null default 0 comment('创建时间') int(11) 'create_time'"`
	UpdateTime   int64  `xorm:"not null default 0 comment('更新时间') int(11) 'update_time'"`
}

func (a *PlanPage) TableName() string {
	return "plan_page"
}

// Save 保存数据
func (a *PlanPage) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

type planPage struct{}

var TbPlanPage planPage

// UpdateMustCol 更新数据
func (a *PlanPage) UpdateMustCol() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(a.ID).MustCols("content", "status").Update(a)
	return err
}

func (c *planPage) GetItemByID(id int64) *PlanPage {
	var table PlanPage
	ok, err := databases.GetEngine().Where("id = ? and status = ?", id, library.No).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (c *planPage) GetList(page, pageSize int, name string) []*PlanPage {
	tables := make([]*PlanPage, 0)
	sess := databases.GetEngine().NewSession()
	defer sess.Close()
	offset := (page - 1) * pageSize
	if page > 0 {
		sess.Limit(pageSize, offset)
	}
	if name != "" {
		sess = sess.Where("plan_page_name LIKE ?", "%"+name+"%")
	}
	sess = sess.Where("status = ?", library.No)
	err := sess.Desc("id").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
