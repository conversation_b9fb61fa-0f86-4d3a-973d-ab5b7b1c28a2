package conf

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
)

type MchAccountConfig struct {
	ID          int64  `xorm:"not null autoincr pk int(11) unsigned 'id'"` // 主键
	ProductType int32  `xorm:"not null tinyint(1) 'product_type'"`         // 产品类型 1会员产品
	ProductID   int64  `xorm:"not null int(11) 'product_id'"`              // 产品ID
	MchID       string `xorm:"not null varchar(64) 'mchid'"`               // 商户号
	IsDel       int32  `xorm:"not null tinyint(1) 'is_del'"`               // 是否删除 1是 2否
	CreateTime  int64  `xorm:"not null int(11) 'create_time'"`             // 创建时间
	UpdateTime  int64  `xorm:"not null int(11) 'update_time'"`             // 更新时间
}

func (m *MchAccountConfig) TableName() string {
	return "mchid_account_config"
}

func (m *MchAccountConfig) Save() error {
	m.CreateTime = time.Now().Unix()
	m.UpdateTime = m.CreateTime
	_, err := databases.GetEngineMaster().Insert(m)
	return err
}

func (m *MchAccountConfig) Update() error {
	m.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(m.ID).Update(m)
	return err
}

type maconf struct {
}

var TbMchAccountConf maconf

func (*maconf) GetList(page, pageSize int) []*MchAccountConfig {
	var tables []*MchAccountConfig
	offset := (page - 1) * pageSize
	err := databases.GetEngine().Where("is_del = ?", library.No).Limit(pageSize, offset).Desc("id").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (*maconf) GetItem(id int64) *MchAccountConfig {
	var table MchAccountConfig
	ok, err := databases.GetEngine().ID(id).Where("is_del = ?", library.No).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (*maconf) GetValid(id int64, productType int32, productID int64) *MchAccountConfig {
	var table MchAccountConfig
	sess := databases.GetEngine().Where("product_type = ? and product_id  = ? and is_del = ?",
		productType, productID, library.No)
	if id > 0 {
		sess.And("id != ?", id)
	}
	ok, err := sess.Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
