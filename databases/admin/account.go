package admin

import (
	"errors"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	df "gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/service/util"
)

// Account 用户账号表
type Account struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 用户昵称
	AdminName string `xorm:"not null varchar(255) 'admin_name'"`
	// md5密码
	PasswordMd5 string `xorm:"not null varchar(64) 'passwd_md5'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (Account) TableName() string {
	return "admin_account"
}

// Save 保存数据
func (a *Account) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := df.GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *Account) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := df.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

type account struct{}

// TbAccount 外部引用对象
var TbAccount account

// GetItemByID 通过ID获取用户详情
func (a *account) GetItemByID(uid int64) *Account {
	var table Account

	ok, err := df.GetEngine().Where("id = ?", uid).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (a *account) CheckUser(username, password string) (*Account, error) {
	if username == "" || password == "" {
		return nil, errors.New("admin_name or password is empty")
	}

	var table Account

	ok, err := df.GetEngine().Where("admin_name = ?", username).
		Limit(1).Get(&table)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, errors.New("not found")
	}

	if table.PasswordMd5 != util.Byte2Md5([]byte(password)) {
		return nil, errors.New("wrong password")
	}

	return &table, nil
}
