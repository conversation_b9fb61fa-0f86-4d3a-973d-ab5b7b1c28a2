package obpay

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
)

type PageSku struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 返回挽留弹窗商品id
	PageID int64 `xorm:"not null int(11) 'page_id'"`
	// 返回挽留弹窗商品id
	ProductID int64 `xorm:"not null int(11) 'product_id'"`
	// 头图
	Img string `xorm:"not null varchar(256) 'img'"`
	// 头图男性
	SelectImg string `xorm:"not null varchar(256) 'select_img'"`
	// 是否选中
	IsSelected int32 `xorm:"not null tinyint(1) 'is_selected'"`
	// 是否删除
	IsDel int32 `xorm:"not null tinyint(1) 'is_del'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
	// 优惠图片地址
	OfferSelectImage string `xorm:"not null default '' VARCHAR(255) 'offer_select_image'" json:"offer_select_image"`
	// 优惠图片地址
	OfferImage string `xorm:"not null default '' VARCHAR(255) 'offer_image'" json:"offer_image"`
	// 取消支付挽留配置
	RetainPopupID  int64  `xorm:"not null int(11) 'retain_popup_id'"`
	RetainImg      string `xorm:"not null varchar(256) 'retain_img'"`
	RetainOfferImg string `xorm:"not null varchar(256) 'retain_offer_img'"`
	// 新SKU替换配置
	ReplaceType             int32  `xorm:"not null int(11) 'replace_type'"`
	ReplaceProductID        int64  `xorm:"not null int(11) 'replace_product_id'"`
	ReplaceImg              string `xorm:"not null varchar(256) 'replace_img'"`
	ReplaceSelectImg        string `xorm:"not null varchar(256) 'replace_select_img'"`
	ReplaceOfferImage       string `xorm:"not null varchar(256) 'replace_offer_image'"`
	ReplaceOfferSelectImage string `xorm:"not null varchar(256) 'replace_offer_select_image'"`
}

// TableName 获取表名
func (PageSku) TableName() string {
	return "ob_pay_page_sku"
}

// Save 保存数据
func (a *PageSku) Save() error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := databases.GetEngineMaster().Insert(a)
	return err
}

// Update 更新数据
func (a *PageSku) Update() error {
	a.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(a.ID).Update(a)
	return err
}

// Update 更新数据
func (a *PageSku) DeleteByPageIDByTran(session *xorm.Session) error {
	a.IsDel = library.Yes
	a.UpdateTime = time.Now().Unix()
	_, err := session.Where("page_id = ? and is_del = ?", a.PageID, library.No).Update(a)
	return err
}

// Save 保存数据
func (a *PageSku) SaveByTran(session *xorm.Session) error {
	a.CreateTime = time.Now().Unix()
	a.UpdateTime = a.CreateTime
	_, err := session.Insert(a)
	return err
}

type pagesku struct {
}

var TbPageSku pagesku

func (*pagesku) GetListByPageID(pageID int64) []*PageSku {
	var tables []*PageSku
	err := databases.GetEngine().Where("page_id = ? and is_del = ?", pageID, library.No).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
