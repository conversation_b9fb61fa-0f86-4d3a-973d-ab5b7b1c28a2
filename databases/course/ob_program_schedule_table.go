package course

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
)

type ObProgramScheduleTable struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 课表id
	ScheduleID int64 `xorm:"int(10) unsigned notnull 'schedule_id'"`
	// 课表名称
	Name string `xorm:"not null varchar(255) 'name'"`
	// 循环顺序
	CycleSort int64 `xorm:"int(10) unsigned notnull 'cycle_sort'"`
	// 资源列表
	SessionList string `xorm:"text notnull 'session_list'"`
	// 是否删除 1 删除 2 否
	IsDel int32 `xorm:"not null tinyint(1) 'is_del'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (ObProgramScheduleTable) TableName() string {
	return "ob_program_schedule_table"
}

// Save 保存数据
func (o *ObProgramScheduleTable) Save() error {
	o.CreateTime = time.Now().Unix()
	o.UpdateTime = o.CreateTime

	_, err := databases.GetEngineMaster().Insert(o)
	return err
}

// Update 更新数据
func (o *ObProgramScheduleTable) Update() error {
	o.UpdateTime = time.Now().Unix()

	_, err := databases.GetEngineMaster().ID(o.ID).Update(o)
	return err
}

func (o *ObProgramScheduleTable) SaveByTran(session *xorm.Session) error {
	o.CreateTime = time.Now().Unix()
	o.UpdateTime = o.CreateTime
	_, err := session.Insert(o)
	return err
}

func (o *ObProgramScheduleTable) UpdateByTran(session *xorm.Session) error {
	o.UpdateTime = time.Now().Unix()
	_, err := session.ID(o.ID).Update(o)
	return err
}

type obProSchTable struct{}

var TbObProSchTable obProSchTable

func (o *obProSchTable) GetListByScheduleID(scheduleID int64) []*ObProgramScheduleTable {
	var tables []*ObProgramScheduleTable
	err := databases.GetEngine().Where("schedule_id = ? and is_del = ?", scheduleID, library.No).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (o *obProSchTable) GetItemByID(id int64) *ObProgramScheduleTable {
	var table ObProgramScheduleTable
	ok, err := databases.GetEngine().Where("id = ?", id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}
