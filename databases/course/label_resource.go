package course

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	lc "gitlab.dailyyoga.com.cn/server/children-admin-api/library/cache"
	libc "gitlab.dailyyoga.com.cn/server/children-admin-api/library/course"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/service/cache"
)

// LabelResource 标签资源列表
type LabelResource struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 资源ID
	ResourceType int64 `xorm:"not null tinyint(4) 'resource_type'"`
	// 资源ID
	ResourceID int64 `xorm:"not null int(11) 'resource_id'"`
	// 标签ID
	LabelID int64 `xorm:"not null int(11) 'label_id'"`
	// 是否删除 1 删除 2 否
	IsDel int32 `xorm:"not null tinyint(1) 'is_del'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
}

// TableName 获取表名
func (LabelResource) TableName() string {
	return "label_resource"
}

// Save 保存数据
func (l *LabelResource) Save() error {
	l.CreateTime = time.Now().Unix()
	l.UpdateTime = l.CreateTime

	_, err := databases.GetEngineMaster().Insert(l)
	return err
}

// Update 更新数据
func (l *LabelResource) Update() error {
	l.UpdateTime = time.Now().Unix()

	_, err := databases.GetEngineMaster().ID(l.ID).Update(l)
	return err
}

// SaveByTran 保存数据
func (l *LabelResource) SaveByTran(session *xorm.Session) error {
	l.CreateTime = time.Now().Unix()
	l.UpdateTime = l.CreateTime

	_, err := session.Insert(l)
	return err
}

func (l *LabelResource) UpdateTran(session *xorm.Session) error {
	l.UpdateTime = time.Now().Unix()

	_, err := session.ID(l.ID).Update(l)
	return err
}

func (l *LabelResource) DelByTran(resourceID int64, labelCourseArr []string, session *xorm.Session, resourceType int) error {
	if len(labelCourseArr) > 0 {
		sql := "update label_resource set is_del = ?,update_time=? where  resource_id=? and label_id in(?) and resource_type = ?"
		_, err := session.Exec(sql, library.Yes, time.Now().Unix(), resourceID, strings.Join(labelCourseArr, ","), resourceType)
		l.UpdateCacheAll()
		return err
	}
	sql := "update label_resource set is_del = ?,update_time=? where  resource_id=? and resource_type = ?"
	_, err := session.Exec(sql, library.Yes, time.Now().Unix(), resourceID, resourceType)
	l.UpdateCache(resourceID)
	return err
}

func (l *LabelResource) DelByIDsTran(ids []string, session *xorm.Session, resourceID int64) error {
	sql := fmt.Sprintf("update label_resource set is_del = %d,update_time=%d where  id in(%s)",
		library.Yes, time.Now().Unix(), strings.Join(ids, ","))
	_, err := session.Exec(sql)
	l.UpdateCache(resourceID)
	return err
}

// UpdateCache 更新缓存
func (l *LabelResource) UpdateCacheAll() {
	rdc := cache.GetCRedis().GetClient()
	ctx := context.Background()
	rdc.Del(ctx, lc.CourseResourceID)
}

func (l *LabelResource) UpdateCache(id int64) {
	rdc := cache.GetCRedis().GetClient()
	ctx := context.Background()
	cKey := strconv.FormatInt(id, 10)
	rdc.HDel(ctx, lc.CourseResourceID, cKey)
}

type labelResource struct{}

// TbLabelResource 外部引用对象
var TbLabelResource labelResource

// GetListByResourceID 获取资源ID对应的标签ID
func (l *labelResource) GetListByResourceID(resourceID int64, resourceType libc.ResourceTypeInt) []LabelResource {
	var tables []LabelResource

	err := databases.GetEngine().Where("resource_id = ? and resource_type = ? and is_del = ?",
		resourceID, resourceType, library.No).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (l *labelResource) GetByResourceID(resourceID, labelID int64, resourceType libc.ResourceTypeInt) *LabelResource {
	var t LabelResource
	ok, err := databases.GetEngine().Where("resource_id = ? and label_id = ? and resource_type = ? and is_del = ?",
		resourceID, labelID, resourceType, library.No).Get(&t)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &t
}
