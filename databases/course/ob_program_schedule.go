package course

import (
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
)

type ObProgramSchedule struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 课表名称
	Name string `xorm:"not null varchar(255) 'name'"`
	// 用户分群
	UserGroupID int64 `xorm:"int(10) unsigned notnull 'user_group_id'"`
	// 优先级
	Priority int64 `xorm:"int(10) unsigned notnull 'priority'"`
	// 是否删除 1 删除 2 否
	IsDel int32 `xorm:"not null tinyint(1) 'is_del'"`
	// 自动类型
	ChangeType int32 `xorm:"not null tinyint(1) 'change_type'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
}

// TableName 获取表名
func (ObProgramSchedule) TableName() string {
	return "ob_program_schedule"
}

// Save 保存数据
func (o *ObProgramSchedule) Save() error {
	o.CreateTime = time.Now().Unix()
	o.UpdateTime = o.CreateTime
	_, err := databases.GetEngineMaster().Insert(o)
	return err
}

// Update 更新数据
func (o *ObProgramSchedule) Update() error {
	o.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(o.ID).Update(o)
	return err
}

func (o *ObProgramSchedule) SaveByTran(session *xorm.Session) error {
	o.CreateTime = time.Now().Unix()
	o.UpdateTime = o.CreateTime
	_, err := session.Insert(o)
	return err
}

func (o *ObProgramSchedule) UpdateByTran(session *xorm.Session) error {
	o.UpdateTime = time.Now().Unix()
	_, err := session.ID(o.ID).MustCols("user_group_id", "priority").Update(o)
	return err
}

type obProSch struct{}

var TbObProSch obProSch

func (o *obProSch) GetItemByID(id int64) *ObProgramSchedule {
	var table ObProgramSchedule
	ok, err := databases.GetEngine().Where("id = ?", id).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (o *obProSch) GetItemByPriority(priority int64) *ObProgramSchedule {
	var table ObProgramSchedule
	ok, err := databases.GetEngine().Where("is_del = ? and priority = ?", library.No, priority).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (o *obProSch) GetList(name string, userGroupID int64) []*ObProgramSchedule {
	var tables []*ObProgramSchedule
	sess := databases.GetEngineMaster().NewSession()
	defer sess.Close()
	sess.Where("is_del = ?", library.No)
	if name != "" {
		sess.And("name LIKE ?", "%"+name+"%")
	}
	if userGroupID > 0 {
		sess.And("user_group_id = ?", userGroupID)
	}
	err := sess.OrderBy("id desc").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
