package course

import (
	"context"
	"strconv"
	"time"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	lc "gitlab.dailyyoga.com.cn/server/children-admin-api/library/cache"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/service/cache"
)

// DBCourseLibrary 课程库
type DBCourseLibrary struct {
	// 主键
	ID int64 `xorm:"not null autoincr pk int(11) 'id'"`
	// 课程标题
	Title string `xorm:"not null varchar(255) 'title'"`
	// 课程描述
	Desc string `xorm:"not null varchar(255) 'desc'"`
	// 难度等级
	Level int32 `xorm:"not null tinyint(4) 'level'"`
	// 课程视频地址
	VideoURL string `xorm:"not null varchar(255) 'video_url'"`
	// 课程封面
	CoverURL string `xorm:"not null varchar(255) 'cover_url'"`
	// 课程横版封面
	HorizontalCoverURL string `xorm:"not null varchar(255) 'horizontal_cover_url'"`
	// 课程时长
	Duration float64 `xorm:"not null decimal(10,3) 'duration'"`
	// 是否VIP
	IsVIP int32 `xorm:"not null tinyint(1) 'is_vip'"`
	// 是否在线
	IsOnline int32 `xorm:"not null tinyint(1) 'is_online'"`
	// 创建时间
	CreateTime int64 `xorm:"not null int(11) 'create_time'"`
	// 更新时间
	UpdateTime int64 `xorm:"not null int(11) 'update_time'"`
	// 练习次数
	PracticeNum int64 `xorm:"not null int(11) 'practice_num'"`
	// 1 横屏 2 竖屏
	IsHorizontal int32 `xorm:"not null tinyint(1) 'is_horizontal'"`
	// 自定义标签
	CustomLabels string `xorm:"not null varchar(256) 'custom_labels'"`
	// 教练ID
	Coach int64 `xorm:"not null int(11) 'coach'"`
	// 注意事项
	Precautions string `xorm:"not null varchar(256) 'precautions'"`
	// 是否新课程
	IsNewCourse int32 `xorm:"not null tinyint(1) 'is_new_course'"`
	// 课程类型
	CourseType int32 `xorm:"not null int(11) 'course_type'"`
	// 新课程时间
	NewCourseTime int64 `xorm:"not null int(11) 'new_course_time'"`
}

// TableName 获取表名
func (DBCourseLibrary) TableName() string {
	return "course_library"
}

// Save 保存数据
func (c *DBCourseLibrary) Save() error {
	c.CreateTime = time.Now().Unix()
	c.UpdateTime = c.CreateTime

	_, err := databases.GetEngineMaster().Insert(c)
	return err
}

// SaveByTran 保存数据
func (c *DBCourseLibrary) SaveByTran(session *xorm.Session) error {
	c.CreateTime = time.Now().Unix()
	c.UpdateTime = c.CreateTime

	_, err := session.Insert(c)
	return err
}

// Update 更新数据
func (c *DBCourseLibrary) Update() error {
	c.UpdateTime = time.Now().Unix()
	c.UpdateCache(c.ID)
	_, err := databases.GetEngineMaster().ID(c.ID).Update(c)
	return err
}

// UpdateByTran 更新数据
func (c *DBCourseLibrary) UpdateByTran(session *xorm.Session) error {
	c.UpdateTime = time.Now().Unix()
	c.UpdateCache(c.ID)
	_, err := session.ID(c.ID).MustCols("need_warmup_before", "need_relax_after", "trial_action_1",
		"trial_action_2").Update(c)
	return err
}

// UpdateCache 更新缓存
func (c *DBCourseLibrary) UpdateCache(id int64) {
	rdc := cache.GetCRedis().GetClient()
	ctx := context.Background()
	cKey := strconv.FormatInt(id, 10)
	rdc.HDel(ctx, lc.CourseLibraryHash, cKey)
}

type clibrary struct {
}

var TbCLibrary clibrary

// GetItem 获取课程详情
func (s *clibrary) GetItem(courseID int64) *DBCourseLibrary {
	var table DBCourseLibrary

	ok, err := databases.GetEngine().Where("id = ?", courseID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetItemByUniqID 获取课程详情
func (s *clibrary) GetItemByUniqID(uniqID string) *DBCourseLibrary {
	var table DBCourseLibrary

	ok, err := databases.GetEngine().Where("unique_id = ?", uniqID).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

// GetItemByTitle 获取课程详情
func (s *clibrary) GetItemByTitle(title string) *DBCourseLibrary {
	var table DBCourseLibrary

	ok, err := databases.GetEngine().Where("title = ?", title).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

type ListQuery struct {
	ID            int64  `json:"id"`
	Title         string `json:"title"`
	Level         int32  `json:"level"`
	IsOnline      int32  `json:"is_online"`
	IsVIP         int32  `json:"is_vip"`
	Page          int32  `json:"page"`
	PageSize      int32  `json:"page_size"`
	DurationStart int64  `json:"duration_start"`
	DurationEnd   int64  `json:"duration_end"`
	CourseType    int    `json:"course_type"`
}

// GetListByAdminQuery 后台课程查询
func (s *clibrary) GetListByAdminQuery(query *ListQuery) []*DBCourseLibrary {
	var tables []*DBCourseLibrary
	sess := databases.GetEngineMaster().NewSession()
	defer sess.Close()
	sess.Table("course_library").Alias("s").Select("s.*").Where("1=1")
	if query.ID > 0 {
		sess.And("s.id = ?", query.ID)
	}
	if query.Title != "" {
		sess.And("s.title LIKE ?", "%"+query.Title+"%")
	}
	if query.IsVIP > 0 {
		sess.And("s.is_vip = ?", query.IsVIP)
	}
	if query.IsOnline > 0 {
		sess.And("s.is_online = ?", query.IsOnline)
	}
	if query.Level > 0 {
		sess.And("s.level = ?", query.Level)
	}
	if query.DurationStart > 0 {
		sess.And("s.duration >= ?", query.DurationStart)
	}
	if query.DurationEnd > 0 {
		sess.And("s.duration <= ?", query.DurationEnd)
	}
	if query.CourseType != 0 {
		sess.And("s.course_type = ?", query.CourseType)
	}
	offset := (query.Page - 1) * query.PageSize
	sess.OrderBy("s.id desc").Limit(int(query.PageSize), int(offset))
	err := sess.Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

// GetItemGenerateDone 获取课程详情
func (s *clibrary) GetItemGenerateDone() *DBCourseLibrary {
	var table DBCourseLibrary
	ok, err := databases.GetEngine().Where("is_generate_done = ?", library.No).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (s *clibrary) GetListMap(courseArr []int64) map[int64]*DBCourseLibrary {
	tableMap := make(map[int64]*DBCourseLibrary)
	var table []*DBCourseLibrary
	err := databases.GetEngine().In("id", courseArr).Find(&table)
	if err != nil {
		logger.Error(err)
		return tableMap
	}
	for _, v := range table {
		tableMap[v.ID] = v
	}
	return tableMap
}

func (s *clibrary) GetCourseByIDs(sessionIDArr []int64) map[int64]*DBCourseLibrary {
	result := make(map[int64]*DBCourseLibrary)
	var tables []*DBCourseLibrary
	err := databases.GetEngine().In("id", sessionIDArr).Find(&tables)
	if err != nil {
		logger.Error(err)
		return result
	}
	for _, v := range tables {
		result[v.ID] = v
	}
	return result
}

func (s *clibrary) GetCourseByCourseKind(courseKind []int64) []*DBCourseLibrary {
	var tables []*DBCourseLibrary
	err := databases.GetEngine().In("course_kind", courseKind).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (s *clibrary) GetTotalList() []*DBCourseLibrary {
	var tables []*DBCourseLibrary
	err := databases.GetEngine().Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
