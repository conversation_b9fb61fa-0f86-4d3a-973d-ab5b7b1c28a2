package group

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	ug "gitlab.dailyyoga.com.cn/protogen/fitness-user-group-go/fusergroup"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
)

type UserGroupLabelConfig struct {
	ID           int64  `xorm:"not null pk autoincr INT(11) 'id'" json:"id"`
	CategoryType int32  `xorm:"not null default 0 TINYINT(2) 'category_type'" json:"category_type"`
	LabelTitle   string `xorm:"not null default '' VARCHAR(128) 'label_title'" json:"label_title"`
	LabelName    string `xorm:"not null default '' VARCHAR(32) 'label_name'" json:"label_name"`
	LabelDesc    string `xorm:"not null default '' VARCHAR(256) 'label_desc'" json:"label_desc"`
	DeleteStatus int32  `xorm:"not null default 2 TINYINT(1) 'delete_status'" json:"delete_status"`
	CreateTime   int64  `xorm:"not null INT(11) 'create_time'" json:"create_time"`
	UpdateTime   int64  `xorm:"not null INT(11) 'update_time'" json:"update_time"`
}

type userGroupLabelConfig struct{}

var TbUserGroupLabelConfig userGroupLabelConfig

func (g *userGroupLabelConfig) GetGroupLabelList() []*UserGroupLabelConfig {
	var tables []*UserGroupLabelConfig
	err := databases.GetEngine().Where("delete_status = ?",
		ug.DeleteStatus_No).Asc("category_type").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
