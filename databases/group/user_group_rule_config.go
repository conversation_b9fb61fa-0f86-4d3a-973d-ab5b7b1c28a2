package group

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	ug "gitlab.dailyyoga.com.cn/protogen/fitness-user-group-go/fusergroup"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
)

// user_group_rule_config 账号信息
type RuleConfig struct {
	ID             int64  `xorm:"int(11) not null pk 'id'"`
	DeleteStatus   int32  `xorm:"tinyint(1) 'delete_status'"`
	CreateTime     int64  `xorm:"int(10) 'create_time'"`
	UpdateTime     int64  `xorm:"int(10) 'update_time'"`
	GroupName      string `xorm:"varchar(64) 'group_name'"`
	GroupRule      string `xorm:"varchar(2048) 'group_rule'"`
	GroupRuleLabel string `xorm:"varchar(2048) 'group_rule_label'"`
}

func (RuleConfig) TableName() string {
	return "user_group_rule_config"
}

func (u *RuleConfig) Update() bool {
	u.UpdateTime = time.Now().Unix()
	_, err := databases.GetEngineMaster().ID(u.ID).Update(u)
	if err != nil {
		logger.Error(err)
		return false
	}
	return true
}

func (u *RuleConfig) Insert() bool {
	u.CreateTime = time.Now().Unix()
	u.UpdateTime = u.CreateTime
	u.DeleteStatus = int32(ug.DeleteStatus_No)
	_, err := databases.GetEngineMaster().Insert(u)
	if err != nil {
		logger.Error(err)
		return false
	}
	return true
}

type userGroupRuleConfig struct{}

var TbUserGroupRuleConfig userGroupRuleConfig

func (g *userGroupRuleConfig) GetRecords(page, pageSize int32, groupName string) []RuleConfig {
	var tables []RuleConfig
	session := databases.GetEngine().NewSession()
	defer session.Close()
	if groupName != "" {
		session = session.Where("group_name LIKE ?", "%"+groupName+"%")
	}
	session = session.Where("delete_status = ?", ug.DeleteStatus_No).Desc("id")
	start := (page - 1) * pageSize
	err := session.Limit(int(pageSize), int(start)).Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}

func (g *userGroupRuleConfig) GetDetail(id int64) *RuleConfig {
	var table RuleConfig
	ok, err := databases.GetEngine().Where("id = ?", id).
		Where("delete_status = ?", ug.DeleteStatus_No).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (g *userGroupRuleConfig) GetGroupRuleByName(name string) *RuleConfig {
	var table RuleConfig
	ok, err := databases.GetEngine().Where("group_name = ?", name).Where("delete_status = ?",
		ug.DeleteStatus_No).Get(&table)
	if err != nil {
		logger.Error(err)
		return nil
	}
	if !ok {
		return nil
	}
	return &table
}

func (g *userGroupRuleConfig) GetRecordsList(groupID []int64) []RuleConfig {
	var tables []RuleConfig
	err := databases.GetEngine().In("id", groupID).Cols("id", "group_rule_label").Find(&tables)
	if err != nil {
		logger.Error(err)
		return nil
	}
	return tables
}
