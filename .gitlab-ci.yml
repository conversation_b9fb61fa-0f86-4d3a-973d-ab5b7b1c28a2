stages:
  - linter-compile
  - docker
  - deploy

variables:
  GO_VERSION: "go1.20.3"
  GO_LINT: "/usr/local/bin/golangci-lint-153"
  GOPRIVATE: "*.dailyyoga.com.cn"
  GOPROXY: "https://goproxy.io,direct"
  ACR_URL: "registry-vpc.cn-hangzhou.aliyuncs.com/dailyyoga"
  ACR_REPO: "children-admin-api"
  PROJECT: "children"
  TYPE: "admin-api-service"

# ======================== Compile Template ========================
.compile-app: &compile-app
  stage: linter-compile
  tags:
    - server-runner
  cache:
    policy: pull-push
  script:
    - gvm use ${GO_VERSION}
    - go env -w GOPRIVATE=${GOPRIVATE}
    - go env -w GO111MODULE=on
    - go env -w GOPROXY=${GOPROXY}
    - ${GO_LINT} run
    - go build -o app
  artifacts:
    name: "app"
    expire_in: 5 min
    paths:
      - app

compile-app:
  extends: .compile-app
  only:
    refs:
      - release/dev
      - release/mirror
      - master

# ======================== Docker Build Template ========================
.docker-build: &docker-build
  stage: docker
  tags:
    - docker-builder-runner
  script:
    - >
      /kaniko/executor
      --context $CI_PROJECT_DIR
      --dockerfile $CI_PROJECT_DIR/Dockerfile
      --destination ${ACR_URL}/${ACR_REPO}:${IMAGE_TAG}
      --insecure
      --skip-tls-verify

build-push-image:
  extends: .docker-build
  needs:
    - job: compile-app
      artifacts: true
  rules:
    - if: '$CI_COMMIT_REF_NAME == "release/dev"'
      variables:
        IMAGE_TAG: "qa-${CI_COMMIT_SHORT_SHA}"
    - if: '$CI_COMMIT_REF_NAME == "release/mirror"'
      variables:
        IMAGE_TAG: "mirror-${CI_COMMIT_SHORT_SHA}"
    - if: '$CI_COMMIT_REF_NAME == "master"'
      variables:
        IMAGE_TAG: "prod-${CI_COMMIT_SHORT_SHA}"

# ======================== Deploy Template ========================
.deploy: &deploy
  stage: deploy
  tags:
    - helm-writer-runner
  before_script:
    - 'git clone --single-branch --branch master https://gitlab.dailyyoga.com.cn/rdc/helm/server.git'
    - 'cd server/${TYPE}/'
  script:
    - 'yq eval -i ".deploy.imageTag = \"${IMAGE_TAG}\"" ${VALUES_FILE}'
    - 'git commit -am "Update image: ${IMAGE_TAG}"'
    - 'git push origin master'

deploy-image:
  extends: .deploy
  needs: [build-push-image]
  rules:
    - if: '$CI_COMMIT_REF_NAME == "release/dev"'
      variables:
        IMAGE_TAG: "qa-${CI_COMMIT_SHORT_SHA}"
        VALUES_FILE: "${PROJECT}-values-qa.yaml"
    - if: '$CI_COMMIT_REF_NAME == "release/mirror"'
      variables:
        IMAGE_TAG: "mirror-${CI_COMMIT_SHORT_SHA}"
        VALUES_FILE: "${PROJECT}-values-mirror.yaml"
    - if: '$CI_COMMIT_REF_NAME == "master"'
      variables:
        IMAGE_TAG: "prod-${CI_COMMIT_SHORT_SHA}"
        VALUES_FILE: "${PROJECT}-values-prod.yaml"