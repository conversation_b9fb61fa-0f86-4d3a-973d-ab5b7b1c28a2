package main

import (
	"log"

	"gitlab.dailyyoga.com.cn/gokit/conf"
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/middlewares/auth"
	srvconf "gitlab.dailyyoga.com.cn/server/children-admin-api/config"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/grpc"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
	srvadmin "gitlab.dailyyoga.com.cn/server/children-admin-api/service/admin"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/service/cache"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/service/sensor"
)

// nolint
func httpStarter(ms *microservice.Microservice) {
	opts := []http.Option{
		http.WithCORS(
			http.WithCORSAllowAll(),
			http.WithCORSAllowOrigin("*"),
			http.WithCORSAllowHeaders("Operatorname", "accept", "Content-Length",
				"Accept-Encoding", "X-CSRF-Token", "origin", "Cache-Control", "X-Requested-With"),
			http.WithCORSAllowMethods("GET", "POST", "PUT", "DELETE", "OPTIONS"),
		),
		http.WithResponseCodes(errorcode.ErrorCodeMessage),
		http.WithRouter(routerAdmin),
		http.WithMiddlewares(auth.MiddlewareAdmin(auth.WithAdminConfig(srvadmin.GetAuthConfig()))),
	}
	http.NewServer(ms, opts...)
}

func main() {
	config := conf.Read("children-admin-api", "common/redis", "common/etcd", "db/children")
	opts := []microservice.Option{
		microservice.WithConfig(config),
		microservice.WithStarter(srvconf.InitConf, databases.Starter, httpStarter, grpc.Starter, cache.Starter,
			CronStarter, sensor.Starter),
	}
	if err := microservice.Run(opts...); err != nil {
		log.Fatal(err)
	}
}
