package config

import (
	"encoding/json"

	"gitlab.dailyyoga.com.cn/gokit/conf"
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/gokit/middlewares/auth"
)

var gConfig *Conf

func Get() *Conf {
	return gConfig
}

type Conf struct {
	Service          Service           `json:"service"`
	Databasechildren Database          `yaml:"db_children" json:"db_children"`
	QiniuCDN         QiniuCDN          `yaml:"qiniu_cdn" json:"qiniu_cdn"`
	AdminAuth        *auth.AdminConfig `yaml:"admin_auth" json:"admin_auth"`
	AppVersionURL    string            `yaml:"app_version" json:"app_version"`
	QiYuCs           QiYuCs            `yaml:"qi_yu_cs" json:"qi_yu_cs"`
}

type QiniuCDN struct {
	AccessKey    string `yaml:"access_key" json:"access_key"`
	AccessSecret string `yaml:"access_secret" json:"access_secret"`
}

type QiYuCs struct {
	Md5Key string `yaml:"md5key" json:"md5key"`
	Target string `yaml:"target" json:"target"`
}

type Database struct {
	Master DatabaseConf   `toml:"master" json:"master" yaml:"master"`
	Slaves []DatabaseConf `toml:"slaves" json:"slaves" yaml:"slaves"`
}

type DatabaseConf struct {
	Address   string `toml:"address" json:"address" yaml:"address"`
	User      string `toml:"user" json:"user" yaml:"user"`
	Password  string `toml:"password" json:"password" yaml:"password"`
	Databases string `toml:"databases" json:"databases" yaml:"databases"`
	Charset   string `toml:"charset" json:"charset" yaml:"charset"`
}

type Service struct {
	Env             microservice.Env `json:"env"`
	Name            string           `json:"name"`
	SensorsDataPath string           `json:"sensors_data_path"`
}

func InitConf(ms *microservice.Microservice) {
	confInfo := ms.GetConf().GetViperConf()
	var err error
	gConfig, err = conf.Parse[Conf](confInfo)
	if err != nil {
		panic(err)
	}
	aa, _ := json.Marshal(gConfig)
	logger.Info("InitConf", string(aa))
}
