# children-admin-api

根据动作刷新缓存

```go
package main

import (
	"fmt"
	"io/ioutil"
	"net/http"
	"strings"

	"github.com/huaweicloud/huaweicloud-sdk-go-v3/core/auth/global"
	cdn1 "github.com/huaweicloud/huaweicloud-sdk-go-v3/services/cdn/v1"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/cdn/v1/model"
	"github.com/huaweicloud/huaweicloud-sdk-go-v3/services/cdn/v1/region"
)

func main() {
	result := make([]string, 0)
	for _, action := range []string{
		"bb21c9bcaa00d9babf8a54239efa2151",
	} {
		for _, suffix := range []string{"1920pvh/index.m3u8", "1920pl/index.m3u8", "1080p/index.m3u8", "720p/index.m3u8"} {
			resultData := GetM3U8(action, suffix)
			if len(resultData) != 0 {
				result = append(result, resultData...)
			}
		}
	}
}

func GetM3U8(action, suffix string) []string {
	result := make([]string, 0)
	url := "/videos/actionv2/" + action + "/915cf282949814f04776f226fc082d63/withexplanations/"
	method := "GET"

	client := &http.Client{}
	req, err := http.NewRequest(method, "http://qnchildren.dailyworkout.cn"+url+suffix, nil)

	if err != nil {
		fmt.Println(err)
		return result
	}
	res, err := client.Do(req)
	if err != nil {
		fmt.Println(err)
		return result
	}
	defer res.Body.Close()

	body, err := ioutil.ReadAll(res.Body)
	if err != nil {
		fmt.Println(err)
		return result
	}
	RefreshUrls("https://upftimage.dailyworkout.cn" + url + suffix)
	RefreshUrls("https://childrenimg.dailyworkout.cn" + url + suffix)
	result = append(result, url+suffix)
	for _, v := range strings.Split(string(body), "\n") {
		if strings.Contains(v, ".ts") {
			RefreshUrls("https://upftimage.dailyworkout.cn" + url + strings.Split(suffix, "index.m3u8")[0] + v)
			RefreshUrls("https://childrenimg.dailyworkout.cn" + url + strings.Split(suffix, "index.m3u8")[0] + v)
			result = append(result, url+strings.Split(suffix, "index.m3u8")[0]+v)
		}
	}
	return result
}

func RefreshUrls(url string) {
	request := &model.CreateRefreshTasksRequest{}
	var listUrlsRefreshTask = []string{
		url,
	}
	fmt.Println(url)
	typeRefreshTask := model.GetRefreshTaskRequestBodyTypeEnum().FILE
	refreshTaskBody := &model.RefreshTaskRequestBody{
		Type: &typeRefreshTask,
		Urls: listUrlsRefreshTask,
	}
	request.Body = &model.RefreshTaskRequest{
		RefreshTask: refreshTaskBody,
	}
	result, err := GetHwCDNManager().CreateRefreshTasks(request)
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(result.String())
}
func GetHwCDNManager() *cdn1.CdnClient {
	auths := global.NewCredentialsBuilder().
		WithAk("39XMKOXKPFC0Y2B7OFXB").
		WithSk("xWSRJcfwzc9B1P5Wnqk0F4FSglfZ1hPFtXF9GBSc").
		Build()

	return cdn1.NewCdnClient(
		cdn1.CdnClientBuilder().
			WithRegion(region.ValueOf("cn-north-1")).
			WithCredential(auths).
			Build())
}
```