package sensor

import (
	"strings"

	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	sc "gitlab.dailyyoga.com.cn/gokit/sensorsdata"

	srvconf "gitlab.dailyyoga.com.cn/server/children-admin-api/config"
)

type EvaluateResults struct {
	IsPass   bool   `json:"is_pass"`
	GiftName string `json:"gift_name"`
	Channel  string `json:"channel"`
}

// EventName 注册事件上报
// nolint
func (c EvaluateResults) EventName() string {
	cfgEnv := srvconf.Get().Service.Env
	if cfgEnv != microservice.Product && cfgEnv != microservice.Mirror {
		return "h2_evaluate_result_cs"
	}
	return "evaluate_result_cs"
}

func (EvaluateResults) Prefix() string {
	return ""
}
func (c *EvaluateResults) Track(uid string) {
	go func() {
		if err := sc.Track(uid, *c, true); err != nil {
			logger.Error(err)
		}
	}()
}

func Starter(ms *microservice.Microservice) {
	if err := sc.Initial(srvconf.Get().Service.SensorsDataPath, false,
		strings.ReplaceAll(srvconf.Get().Service.Name, "-", "")); err != nil {
		logger.Errorf("sensors data init failed. %s", err)
	}
}
