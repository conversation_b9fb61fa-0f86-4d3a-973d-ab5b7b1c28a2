package util

import (
	"regexp"
	"strconv"
	"strings"
)

func IsDayStart(row string) bool {
	if row == "" {
		return false
	}
	re := regexp.MustCompile(`第\d+天`)
	return re.MatchString(row)
}

// ParseFloat64 string 转 float64
func ParseFloat64(str string) float64 {
	if str == "" {
		return 0
	}
	f, err := strconv.ParseFloat(str, 64)
	if err != nil {
		return 0
	}
	return f
}

func ParseInt64(str string) int64 {
	if str == "" {
		return 0
	}
	f, err := strconv.ParseInt(str, 10, 64)
	if err != nil {
		return 0
	}
	return f
}

// IsXLSX 检查文件是否是 XLSX 格式
func IsXLSX(filename string) bool {
	// 将文件名转为小写，以确保对比时不区分大小写
	lowerFilename := strings.ToLower(filename)
	// 检查文件后缀是否为 ".xlsx"
	return strings.HasSuffix(lowerFilename, ".xlsx")
}
