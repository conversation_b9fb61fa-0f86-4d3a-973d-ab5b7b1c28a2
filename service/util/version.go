package util

import (
	"strconv"
	"strings"
)

type version struct{}

var UVersion version

type VRes struct {
	Success bool
	ResMsg  string
	Version int64
}

const VersionEmptyErr = "版本号不能为空"
const VersionFormatErr = "输入数据格式错误"
const VersionInputErr = "版本号填写有问题"

type Place int64

func IntToEnumType(e int) Place {
	return Place(e)
}

// PlaceEnum 位数类型列表
var PlaceEnum = struct {
	First Place
	Two   Place
	Three Place
	Last  Place
}{

	First: 0,
	Two:   1,
	Three: 2,
	Last:  3,
}

var PlaceEnumValueFormat8 = map[Place]int64{
	PlaceEnum.First: 1000000,
	PlaceEnum.Two:   10000,
	PlaceEnum.Three: 100,
	PlaceEnum.Last:  1,
}

const PlaceVersion = 4

func (v *version) Format(i string) *VRes {
	res := &VRes{
		Success: true,
	}
	i = strings.Replace(i, "df-", "", 1)
	if i == "" {
		res.Success = false
		res.ResMsg = VersionEmptyErr
		return res
	}
	if !strings.Contains(i, ".") {
		res.Success = false
		res.ResMsg = VersionFormatErr
		return res
	}
	arr := strings.Split(i, ".")
	if len(arr) > PlaceVersion {
		res.Success = false
		res.ResMsg = "传入版本号为8位请检查"
		return res
	}
	res.Version = v.FormatPlace(arr)
	return res
}

// 处理版本号
func (v *version) FormatPlace(arr []string) int64 {
	var vs int64
	for k, v := range arr {
		tmp, _ := strconv.ParseInt(v, 10, 64)
		vs += tmp * PlaceEnumValueFormat8[IntToEnumType(k)]
	}
	return vs
}
