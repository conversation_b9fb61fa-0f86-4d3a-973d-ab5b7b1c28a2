package util

import "gitlab.dailyyoga.com.cn/server/children-admin-api/databases/client"

func SaveConfig(key, keyName, value string) error {
	schedule := &client.Config{
		Key:     key,
		KeyName: keyName,
		Value:   value,
	}
	info := client.TbConfig.GetItemByKey(key)
	if info == nil {
		if err := schedule.Save(); err != nil {
			return err
		}
	} else {
		schedule.ID = info.ID
		if err := schedule.Update(); err != nil {
			return err
		}
	}
	return nil
}
