package util

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	message "gitlab.dailyyoga.com.cn/protogen/srv-message-go"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/grpc"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
)

type vercode struct{}

var SrvVercode vercode

// GenerateCode 生成随机数code
func (*vercode) GenerateCode() int {
	return int(rand.New(rand.NewSource(time.Now().UnixNano())).Int31n(899999) + 100000) //nolint
}

// SendMsgCode 发送短信验证码
func (*vercode) SendMsgCode(mobile string, code int) error {
	serv := grpc.GetMessageClient()
	content := fmt.Sprintf("您的验证码是%d。有效期为2小时，请您尽快验证。感谢使用小树苗运动！", code)
	resp, err := serv.SendSMS(context.Background(), &message.SMSRequest{
		Phone:   mobile,
		Content: content,
		Type:    message.SMSType_CODE,
		MsgSign: "【小树苗运动】",
	})
	if err != nil {
		logger.Error("发送验证码失败", err)
		return err
	}
	if resp.GetResultCode() != errorcode.ServiceResultSuccess {
		logger.Error("发送验证码失败", resp)
		return errors.New("发送验证码失败")
	}
	return nil
}

func (*vercode) PraiseMsg(mobile string) error {
	serv := grpc.GetMessageClient()
	resp, err := serv.SendSMS(context.Background(), &message.SMSRequest{
		Phone:   mobile,
		Content: "恭喜你！好评审核已通过，快打开小树苗运动APP查看奖励吧！",
		Type:    message.SMSType_MARKETING,
		MsgSign: "【小树苗运动】",
	})
	if err != nil {
		logger.Error("发送好评审核已通过失败", err)
		return err
	}
	if resp.GetResultCode() != errorcode.ServiceResultSuccess {
		logger.Error("发送好评审核已通过失败", resp)
		return errors.New("发送好评审核已通过失败")
	}
	return nil
}
