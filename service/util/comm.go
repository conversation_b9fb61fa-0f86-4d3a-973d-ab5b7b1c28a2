package util

import (
	"context"
	"fmt"
	"io"
	nhttp "net/http"
	"strings"
)

// HTTPRequest HTTP 请求
func HTTPRequest(url, method string, header nhttp.Header, body io.Reader) ([]byte, error) {
	// validate
	if url == "" || method == "" || header == nil {
		return nil, fmt.Errorf("请检查请求参数")
	}
	method = strings.ToUpper(method)
	client := new(nhttp.Client)
	tr := &nhttp.Transport{
		DisableKeepAlives: true,
	}
	client.Transport = tr
	r, err := nhttp.NewRequestWithContext(context.Background(), method, url, body)
	if err != nil {
		return nil, err
	}
	r.Header = header
	resp, err := client.Do(r)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()
	return io.ReadAll(resp.Body)
}
