package util

import (
	"encoding/json"
	"net/http"
	"sync"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
)

// 格式化图片信息
func FormatVideoInfo(url string) *library.VideoInfo {
	if url == "" {
		return nil
	}
	data := GetVideoSizeByQiNiuURL(url)
	if data == nil || len(data.Streams) < 1 {
		logger.Error("获取视频信息错误，请排查", url)
		return nil
	}
	res := &library.VideoInfo{
		URL:    url,
		Width:  data.Streams[0].Width,
		Height: data.Streams[0].Height,
	}
	return res
}

func FormatVideoInfoStr(url string) string {
	if url == "" {
		return ""
	}
	res := FormatVideoInfo(url)
	resByte, err := json.Marshal(res)
	if err != nil {
		return ""
	}
	return string(resByte)
}

func UnmarshalVideoStr(videoStr string) *library.VideoInfo {
	res := &library.VideoInfo{}
	if videoStr == "" {
		return res
	}
	if err := json.Unmarshal([]byte(videoStr), res); err != nil {
		logger.Warn(err)
	}
	return res
}

type VideoSize struct {
	Streams []Streams `json:"streams,omitempty"`
	Format  Format    `json:"format,omitempty"`
}
type Format struct {
	Duration string `json:"duration,omitempty"`
	Size     string `json:"size,omitempty"`
	BitRate  string `json:"bit_rate,omitempty"`
}
type Streams struct {
	Width  int `json:"width,omitempty"`
	Height int `json:"height,omitempty"`
}

var videoSizeMap map[string]*VideoSize
var videoSizeMapOnce sync.Once

// GetVideoSizeByQiNiuURL 通过七牛地址获取视频大小
func GetVideoSizeByQiNiuURL(rURL string) *VideoSize {
	videoSizeMapOnce.Do(func() {
		videoSizeMap = make(map[string]*VideoSize)
	})
	if _, ok := videoSizeMap[rURL]; !ok {
		header := http.Header{}
		header.Add("Content-Type", "application/json")
		address := rURL + "?avinfo"
		body, err := HTTPRequest(address, "GET", header, nil)
		if err != nil {
			logger.Warnf("获取视频尺寸出错 地址:%s 错误:%s", address, err)
			return nil
		}
		var video VideoSize
		if err := json.Unmarshal(body, &video); err != nil {
			logger.Warn(err)
			return nil
		}
		videoSizeMap[rURL] = &video
		return videoSizeMap[rURL]
	}
	return videoSizeMap[rURL]
}
