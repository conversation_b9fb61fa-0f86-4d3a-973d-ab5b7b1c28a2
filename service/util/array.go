package util

import (
	"reflect"
)

// 判断整数是否在数组中
func InArray(n int, arr []int) bool {
	for _, a := range arr {
		if a == n {
			return true
		}
	}
	return false
}

// ContainsElement 使用反射检查数组中是否包含目标元素
func ContainsElement(arr, target interface{}) bool {
	// 使用反射获取数组的值
	arrValue := reflect.ValueOf(arr)
	// 确保 arr 是一个数组
	if arrValue.Kind() != reflect.Array && arrValue.Kind() != reflect.Slice {
		return false
	}
	// 遍历数组，逐一检查元素
	for i := 0; i < arrValue.Len(); i++ {
		// 使用反射获取数组中的元素
		element := arrValue.Index(i).Interface()
		// 使用反射比较元素是否等于目标值
		if reflect.DeepEqual(element, target) {
			return true
		}
	}
	// 没有找到匹配的元素
	return false
}

// ContainsArray 检查数组 arr 是否包含子数组 sub
func ContainsArray(arr, sub []int64) bool {
	// 将 arr1 转换为 map，键为元素值，值为元素出现次数
	countMap := make(map[int64]int)
	for _, v := range arr {
		countMap[v]++
	}
	// 检查 arr2 中的元素是否在 countMap 中存在，并且次数大于等于 arr1 中的次数
	for _, v := range sub {
		count, ok := countMap[v]
		if !ok || count <= 0 {
			return false
		}
		countMap[v]--
	}

	return true
}

// IsEqual 判断两个数组是否相等
func IsEqual(arr1, arr2 []int64) bool {
	if len(arr1) != len(arr2) {
		return false
	}
	for i := range arr1 {
		if arr1[i] != arr2[i] {
			return false
		}
	}
	return true
}

func RemoveDuplicatesInt(nums []int64) []int64 {
	result := make([]int64, 0)
	seen := make(map[int64]bool)

	for _, num := range nums {
		if !seen[num] {
			result = append(result, num)
			seen[num] = true
		}
	}
	return result
}

func RemoveDuplicatesString(nums []string) []string {
	result := make([]string, 0)
	seen := make(map[string]bool)

	for _, num := range nums {
		if !seen[num] {
			result = append(result, num)
			seen[num] = true
		}
	}
	return result
}
