package util

import (
	"encoding/base64"

	mrand "math/rand"
	"time"

	"golang.org/x/crypto/bcrypt"
)

// B64Encode base64加密
func B64Encode(s string) string {
	return base64.StdEncoding.EncodeToString([]byte(s))
}

// B64Encode base64解密
func B64Decode(s string) string {
	code, err := base64.StdEncoding.DecodeString(s)
	if err != nil {
		return ""
	}
	return string(code)
}

// BCryptAndSalt 加密密码
func BCryptAndSalt(pwd string) string {
	bytePwd := []byte(pwd)
	hash, err := bcrypt.GenerateFromPassword(bytePwd, bcrypt.MinCost)
	if err != nil {
		return ""
	}
	return string(hash)
}

// BcryptValidatePwd 验证密码
func BcryptValidatePwd(hashedPwd, plainPwd string) bool {
	byteHash := []byte(hashedPwd)
	bytePlainPwd := []byte(plainPwd)
	err := bcrypt.CompareHashAndPassword(byteHash, bytePlainPwd)
	return err == nil
}

// GetRandomString 生成随机字符串
func GetRandomString(length int) string {
	str := "0123456789AaBbCcDdEeFfGgHhIiJjKkLlMmNnOoPpQqRrSsTtUuVvWwXxYyZz"
	bytes := []byte(str)
	result := make([]byte, 0)
	r := mrand.New(mrand.NewSource(time.Now().UnixNano())) //nolint
	for i := 0; i < length; i++ {
		result = append(result, bytes[r.Intn(len(bytes))])
	}
	return string(result)
}
