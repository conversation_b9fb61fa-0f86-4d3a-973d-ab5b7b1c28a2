package util

import (
	"crypto/md5" // #nosec
	"encoding/hex"
	"fmt"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
)

func ToBool(n int) bool {
	return n == library.Yes
}

func ToFloat64(v interface{}) float64 {
	str := ""
	if i, ok := v.([]byte); ok {
		str = string(i)
	} else {
		str = fmt.Sprint(v)
	}

	i, _ := strconv.ParseFloat(str, 64)
	return i
}

func ToFloat32(i interface{}) float32 {
	return float32(ToFloat64(i))
}

func ToInt(i interface{}) int {
	return int(ToFloat64(i))
}

func ToInt64(i interface{}) int64 {
	return int64(ToInt(i))
}

func ToString(i interface{}) string {
	return fmt.Sprint(i)
}

func Byte2Md5(b []byte) string {
	ctx := md5.New() // #nosec
	_, _ = ctx.Write(b)
	cipher := ctx.Sum(nil)

	return hex.EncodeToString(cipher)
}

func Division(dividend, divisor interface{}) float64 {
	df := ToFloat64(divisor)
	if df == 0 {
		return 0
	}
	return ToFloat64(dividend) / df
}

func GetIntervalDays(start, end int64) int {
	if start == 0 && end == 0 {
		return 0
	}
	if end < start {
		return 0
	}
	t1, _ := time.ParseInLocation("2006-01-02", time.Unix(start, 0).Format("2006-01-02"), time.Local)
	t2, _ := time.ParseInLocation("2006-01-02", time.Unix(end, 0).Format("2006-01-02"), time.Local)
	return int((t2.Unix()-t1.Unix())/86400 + 1)
}
