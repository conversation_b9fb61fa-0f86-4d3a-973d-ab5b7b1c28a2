package cache

import (
	"context"
	"strconv"
	"time"

	"github.com/go-redis/redis/v8"
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
)

// Client redis client
type Client struct {
	*redis.Client
}

type Redis struct {
	c          *Client
	expiration time.Duration
}

var (
	fRedis    *Redis
	yogaRdis  *Redis
	lockRedis *Redis
)

func InitRedis(address, password string) error {
	c, err := NewClient(address, password)
	if err != nil {
		return err
	}
	fRedis = &Redis{
		c:          c,
		expiration: 10 * time.Second,
	}
	return nil
}

func InitYogaRedis(address, password string) error {
	c, err := NewClient(address, password)
	if err != nil {
		return err
	}
	yogaRdis = &Redis{
		c:          c,
		expiration: 10 * time.Second,
	}
	return nil
}

func InitLockRedis(address, password string) error {
	c, err := NewClient(address, password)
	if err != nil {
		return err
	}
	lockRedis = &Redis{
		c:          c,
		expiration: 10 * time.Second,
	}
	return nil
}

// NewClient Connecting to Redis Server
func NewClient(host, password string) (*Client, error) {
	c := &Client{}
	c.Client = redis.NewClient(&redis.Options{
		Addr:     host,
		Password: password,
		// 设置连接池大小
		PoolSize: 10,
		// 设置最小空闲连接数
		MinIdleConns: 5,
	})
	// test connect using ping command
	err := c.Client.Ping(context.Background()).Err()

	return c, err
}

func Starter(ms *microservice.Microservice) {
	conf := ms.GetConf().GetViperConf()
	address := conf.GetString("fitness_redis.address")
	password := conf.GetString("fitness_redis.password")
	if err := InitRedis(address, password); err != nil {
		logger.Error(err)
	}
	addressYoga := conf.GetString("redis.address")
	passwordYoga := conf.GetString("redis.password")
	if err := InitYogaRedis(addressYoga, passwordYoga); err != nil {
		logger.Error(err)
	}
	lockAddress := conf.GetString("lock_redis.address")
	lockPassword := conf.GetString("lock_redis.password")
	if err := InitLockRedis(lockAddress, lockPassword); err != nil {
		logger.Error(err)
	}
}

func GetCRedis() *Redis {
	return fRedis
}

func (r *Redis) GetClient() *Client {
	return r.c
}

func GetYogaRedis() *Redis {
	return yogaRdis
}

func GetLockRedis() *Redis {
	return lockRedis
}

type LockOption func(*Redis)

func LockExpiration(t time.Duration) LockOption {
	return func(r *Redis) {
		r.expiration = t
	}
}

func (r *Redis) Lock(k string, opts ...LockOption) bool {
	v := strconv.FormatInt(time.Now().Unix(), 10)
	ctx := context.Background()

	for _, opt := range opts {
		opt(r)
	}
	if r.expiration == 0 {
		r.expiration = time.Second * 10
	}

	ret, err := r.c.SetNX(ctx, k, v, r.expiration).Result()
	if err != nil {
		return false
	}
	return ret
}

func (r *Redis) Unlock(k string) error {
	ctx := context.Background()
	if err := r.c.Del(ctx, k).Err(); err != nil && err != redis.Nil {
		return err
	}
	return nil
}
