package order

import (
	"encoding/json"

	dbo "gitlab.dailyyoga.com.cn/server/children-admin-api/databases/order"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/pay"
)

type AlipayCompleteRes struct {
	AppID string `json:"app_id"`
}
type WechatCompleteRes struct {
	MchID string `json:"mch_id"`
}

func GetMchIDByOrder(order *dbo.WebOrder) string {
	if order.MchID != "" {
		return order.MchID
	}
	completeOrder := dbo.TbCompleteNofify.GetItemByOrderID(order.OrderID)
	if completeOrder == nil {
		return ""
	}
	switch completeOrder.PayType {
	case pay.PayTypeAlipay:
		alires := &AlipayCompleteRes{}
		err := json.Unmarshal([]byte(completeOrder.Notify), alires)
		if err == nil {
			return alires.AppID
		}
	case pay.PayTypeWechat:
		wechatres := &WechatCompleteRes{}
		err := json.Unmarshal([]byte(completeOrder.Notify), wechatres)
		if err == nil {
			return wechatres.MchID
		}
	}
	return ""
}

func GetRetryTimeByOrderID(orderID string) int {
	charge := dbo.TbAlipayCharge.GetItemByOrderID(orderID)
	// 没有扣款记录，则重试次数为0
	if charge == nil {
		return 0
	}
	lastSuccess := dbo.TbAlipayCharge.GetLastSuccessCharge(charge.ContractCode, charge.ID)
	var minID int64 = 0
	if lastSuccess != nil {
		minID = lastSuccess.ID
	}
	chargeList := dbo.TbAlipayCharge.GetListByIDRange(charge.ContractCode, minID, charge.ID)
	return len(chargeList)
}
