package rop

import (
	"context"
	"encoding/binary"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	pb "gitlab.dailyyoga.com.cn/protogen/children-rop-go/children-rop"
	gdb "gitlab.dailyyoga.com.cn/server/children-admin-api/databases/group"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases/obpay"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases/plan"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases/top"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/grpc"
	libc "gitlab.dailyyoga.com.cn/server/children-admin-api/library/client"
	lib "gitlab.dailyyoga.com.cn/server/children-admin-api/library/rop"
)

type SceneBiz struct {
	ID          int64          `json:"id"`
	Name        string         `json:"name"`
	Description string         `json:"description"`
	Scene       *Scene         `json:"scene"`
	BizID       string         `json:"biz_id"`
	BizPrefix   string         `json:"biz_prefix"`
	ShuntType   int32          `json:"shunt_type"`
	UserGroupID int64          `json:"user_group_id"`
	Status      int32          `json:"status"`
	BizParams   []*ConfigParam `json:"biz_params"`
	CreateTime  int64          `json:"create_time"`
	StartTime   int64          `json:"start_time"`
	EndTime     int64          `json:"end_time"`
}

type Scene struct {
	ID           int64         `json:"id"`
	Name         string        `json:"name"`
	Description  string        `json:"description"`
	OwningModule string        `json:"owning_module"`
	IdentifyType int32         `json:"identify_type"`
	Params       []*SceneParam `json:"params"`
}
type SceneParam struct {
	CateType int32  `json:"cateType"`
	Type     string `json:"type"`
	Name     string `json:"name"`
	Key      string `json:"key"`
}

type ConfigParam struct {
	Type  string
	Name  string
	Key   string
	Value string
}

type BizDetailResp struct {
	ID            int64          `json:"id"`
	SceneID       int64          `json:"scene_id"`
	Name          string         `json:"name"`
	Description   string         `json:"description"`
	Status        int32          `json:"status"`
	StartTime     int64          `json:"start_time"`
	EndTime       int64          `json:"end_time"`
	ModuleTitle   string         `json:"module_title"`
	ShuntType     int32          `json:"shunt_type"`
	UserGroupID   int64          `json:"user_group_id"`
	GoalID        string         `json:"goal_id"`
	GoalName      string         `json:"goal_name"`
	CreateTime    int64          `json:"create_time"`
	BizID         string         `json:"biz_id"`
	OwningModule  string         `json:"owning_module"`
	Experiments   []*Experiments `json:"experiments"`
	UserGroupName string         `json:"user_group_name"`
	GoalParam     string         `json:"goal_param"`
	Versions      []Versions     `json:"versions,omitempty"`
}

type Experiments struct {
	ID          int64  `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Percent     int32  `json:"percent"`
	Status      int32  `json:"status"`
	CreateTime  int64  `json:"create_time"`
	UpdateTime  int64  `json:"update_time"`
	StartTime   int64  `json:"start_time"`
	EndTime     int64  `json:"end_time"`
}

func GetBizDetail(params *pb.FetchSceneBizsRequest) *BizDetailResp {
	data := fetchSceneBizs(params)
	if len(data) < 1 {
		return &BizDetailResp{}
	}
	temp := &SceneBiz{}
	for _, v := range data {
		if v.ID == params.ID {
			temp = v
		}
	}
	if temp.ID < 1 {
		return &BizDetailResp{}
	}
	resp := formatBizCeil(temp)
	if resp.ShuntType == lib.ShuntTypeUserGroup && resp.UserGroupID > 0 {
		group := gdb.TbUserGroupRuleConfig.GetDetail(resp.UserGroupID)
		if group != nil {
			resp.UserGroupName = group.GroupName
		}
	}
	sourceList := GetSourceList(resp.OwningModule)
	for _, v := range sourceList {
		if strconv.Itoa(v.GoalID) == resp.GoalID {
			resp.GoalParam = fmt.Sprintf("%s:%s", resp.GoalID, v.Title)
			resp.GoalName = v.Title
			break
		}
	}
	return resp
}

func GetSourceList(owningModule string) []*lib.SourceItem {
	resp := make([]*lib.SourceItem, 0)
	switch owningModule {
	case lib.OnBoardingPayment, lib.PlanGenerationPage, lib.TallerOb, lib.IndexFullScreenPopup:
		resp = append(resp, lib.SourceDevice...)
	default:
		resp = append(resp, &lib.SourceItem{
			GoalID: 1,
			Title:  "通用目标",
		})
	}
	return resp
}

func fetchSceneBizs(params interface{}) []*SceneBiz {
	scenes := make([]*SceneBiz, 0)
	p, ok := (params).(*pb.FetchSceneBizsRequest)
	if !ok {
		logger.Error("FetchSceneBizsRequest 结构体转化失败")
		return scenes
	}
	client := grpc.GetchildrenRopClient()
	resp, err := client.FetchSceneBizs(context.Background(), p)
	if err != nil {
		logger.Error("请求失败", err)
		return scenes
	}
	if !resp.GetResult().GetOK() || len(resp.GetScenesBizs()) < 1 {
		return scenes
	}
	for _, v := range resp.GetScenesBizs() {
		param := make([]*SceneParam, 0)
		for _, p := range v.GetScene().GetParams() {
			sp := &SceneParam{
				CateType: p.GetCateType(),
				Type:     p.GetType(),
				Name:     p.GetName(),
				Key:      p.GetKey(),
			}
			param = append(param, sp)
		}
		bp := make([]*ConfigParam, 0)
		for _, cp := range v.GetBizParams() {
			bp = append(bp, &ConfigParam{
				Type:  cp.GetType(),
				Name:  cp.GetName(),
				Key:   cp.GetKey(),
				Value: cp.GetValue(),
			})
		}

		s := &SceneBiz{
			ID:          v.GetID(),
			Name:        v.GetName(),
			Description: v.GetDescription(),
			Scene: &Scene{
				ID:           v.GetScene().GetID(),
				Name:         v.GetScene().GetName(),
				Description:  v.GetScene().GetDescription(),
				OwningModule: v.GetScene().GetOwningModule(),
				IdentifyType: v.GetScene().GetIdentifyType(),
				Params:       param,
			},
			BizID:       v.GetBizID(),
			BizPrefix:   v.GetBizPrefix(),
			ShuntType:   int32(v.GetShuntType()),
			UserGroupID: v.GetUserGroupID(),
			Status:      v.GetStatus(),
			CreateTime:  v.GetCreateTime(),
			StartTime:   v.GetStartTime(),
			EndTime:     v.GetEndTime(),
			BizParams:   bp,
		}
		scenes = append(scenes, s)
	}
	return scenes
}

func getBizExperimentsList(bizID string, id int64) []*Experiments {
	list := make([]*Experiments, 0)
	client := grpc.GetchildrenRopClient()
	resp, err := client.FetchExperiments(context.Background(), &pb.FetchExperimentsRequest{
		BizID: bizID,
		ID:    id,
	})
	if err != nil {
		logger.Error("请求失败", err)
		return list
	}
	if !resp.GetResult().GetOK() || len(resp.GetExperiments()) < 1 {
		return list
	}
	for _, v := range resp.GetExperiments() {
		if v.ID < 1 {
			continue
		}
		status := lib.StatusDefault
		if v.GetStartTime() != 0 {
			status = lib.StatusDelete
			if v.GetEndTime() == 0 {
				status = lib.StatusNormal
			}
		}
		temp := &Experiments{
			ID:          v.GetID(),
			Name:        v.GetName(),
			Description: v.GetDescription(),
			Percent:     v.GetPercent(),
			Status:      int32(status),
			StartTime:   v.GetStartTime(),
			EndTime:     v.GetEndTime(),
			CreateTime:  v.GetCreateTime(),
			UpdateTime:  v.GetUpdateTime(),
		}
		list = append(list, temp)
	}
	return list
}

func formatBizCeil(data *SceneBiz) *BizDetailResp {
	experiments := getBizExperimentsList(data.BizID, 0)
	status := lib.StatusDefault
	if len(experiments) > 0 {
		for _, v := range experiments {
			if v.Status == lib.StatusNormal {
				status = lib.StatusNormal
				break
			}
		}
	}
	var groupName string
	if data.UserGroupID > 0 {
		group := gdb.TbUserGroupRuleConfig.GetDetail(data.UserGroupID)
		if group != nil {
			groupName = group.GroupName
		}
	}
	return &BizDetailResp{
		ID:            data.ID,
		SceneID:       data.Scene.ID,
		Name:          data.Name,
		Description:   data.Description,
		Status:        int32(status),
		StartTime:     data.StartTime,
		EndTime:       data.EndTime,
		ModuleTitle:   data.Scene.Name,
		ShuntType:     data.ShuntType,
		UserGroupID:   data.UserGroupID,
		GoalID:        data.BizParams[0].Value,
		CreateTime:    data.CreateTime,
		BizID:         data.BizID,
		OwningModule:  data.Scene.OwningModule,
		Experiments:   experiments,
		UserGroupName: groupName,
	}
}

// GetSceneList 获取场景列表
func GetSceneList(params *pb.FetchSceneBizsRequest) []*BizDetailResp {
	resp := make([]*BizDetailResp, 0)
	data := fetchSceneBizs(params)
	if len(data) < 1 {
		return resp
	}
	for _, v := range data {
		if v.Status == lib.StatusDelete {
			continue
		}
		bizCeil := formatBizCeil(v)
		sourceList := GetSourceList(bizCeil.OwningModule)
		for _, v := range sourceList {
			if strconv.Itoa(v.GoalID) == bizCeil.GoalID {
				bizCeil.GoalName = v.Title
				break
			}
		}
		if v.Scene.ID == lib.ChannelPaymentPageScenesID {
			bizCeil.Versions = getVersionList(v.ID)
		}
		resp = append(resp, bizCeil)
	}
	return resp
}

type InfoResp struct {
	ID            int64           `json:"id"`
	Name          string          `json:"name"`
	Description   string          `json:"description"`
	Percent       int32           `json:"percent"`
	Status        int32           `json:"status"`
	CreateTime    int64           `json:"create_time"`
	UpdateTime    int64           `json:"update_time"`
	StartTime     int64           `json:"start_time"`
	EndTime       int64           `json:"end_time"`
	RunTime       int64           `json:"run_time"`
	Versions      []Versions      `json:"versions"`
	VersionColumn []VersionColumn `json:"version_column"`
}

type Versions struct {
	ID           string `json:"id"`
	Name         string `json:"name"`
	Description  string `json:"description"`
	Percent      int32  `json:"percent"`
	BizID        int64  `json:"biz_id"`
	ExperimentID int64  `json:"experiment_id"`
	Config       string `json:"config"`
}

type VersionColumn struct {
	CateType  int32             `json:"cate_type,omitempty"`
	Type      string            `json:"type,omitempty"`
	Name      string            `json:"name,omitempty"`
	Key       string            `json:"key,omitempty"`
	ValueList map[string]string `json:"value_list,omitempty"`
}

func GetExperimentDetail(bizID, id int64) *InfoResp {
	resp := &InfoResp{}
	experiments := getBizExperimentsList("", id)
	if len(experiments) < 1 {
		return resp
	}
	for _, v := range experiments {
		if v.ID == id {
			resp = &InfoResp{
				ID:          v.ID,
				Name:        v.Name,
				Description: v.Description,
				Percent:     v.Percent,
				Status:      v.Status,
				StartTime:   v.StartTime,
				EndTime:     v.EndTime,
				CreateTime:  v.CreateTime,
				UpdateTime:  v.UpdateTime,
			}
			break
		}
	}
	resp.RunTime = 0
	if resp.Status == lib.StatusNormal {
		resp.RunTime = time.Now().Unix() - resp.StartTime
	}
	resp.Versions = getVersionList(bizID)
	resp.VersionColumn = getVersionColumns(bizID)
	return resp
}

func getVersionList(bizID int64) []Versions {
	data := make([]Versions, 0)
	client := grpc.GetchildrenRopClient()
	resp, err := client.FetchVersions(context.Background(), &pb.FetchVersionsRequest{
		BizID: bizID,
	})
	if err != nil {
		logger.Error("请求失败", err)
		return data
	}
	for _, v := range resp.GetVersions() {
		configSet := make(map[string]string)
		config := v.GetConfig()
		var configSetStr []byte
		if len(config) > 0 {
			for _, c := range config {
				configSet[c.GetKey()] = c.GetValue()
			}
			configSetStr, _ = json.Marshal(configSet)
		}

		data = append(data, Versions{
			ID:           v.GetID(),
			Name:         v.GetName(),
			Description:  v.GetDescription(),
			Percent:      v.GetPercent(),
			ExperimentID: v.GetExperimentID(),
			Config:       string(configSetStr),
			BizID:        v.BizID,
		})
	}
	return data
}

func getVersionColumns(bizID int64) []VersionColumn {
	data := make([]VersionColumn, 0)
	bizData := fetchSceneBizs(&pb.FetchSceneBizsRequest{ID: bizID})
	if len(bizData) == 0 {
		return data
	}
	if bizData[0].Scene == nil {
		return data
	}
	bizDetail := GetBizDetail(&pb.FetchSceneBizsRequest{
		ID: bizID,
	})
	terminalType := libc.DeviceTypeEnum.All
	if bizDetail != nil && bizDetail.GoalID != "" {
		goalID, _ := strconv.Atoi(bizDetail.GoalID)
		switch goalID {
		case lib.DeviceTypeAll:
			terminalType = libc.DeviceTypeEnum.All
		case lib.DeviceTypeIos:
			terminalType = libc.DeviceTypeEnum.IOS
		case lib.DeviceTypeAndroid,
			lib.DeviceTypeAndroidNOHUAWEI,
			lib.DeviceTypeAndroidHUAWEI:
			terminalType = libc.DeviceTypeEnum.Android
		}
	}
	owningModule := bizData[0].Scene.OwningModule
	params := bizData[0].Scene.Params
	for _, v := range params {
		if v.CateType == lib.SceneCateTypeGoal {
			continue
		}
		valueList := getVersionCateTypeConfig(owningModule+v.Key, bizID, terminalType)
		data = append(data, VersionColumn{
			CateType:  v.CateType,
			Type:      v.Type,
			Name:      v.Name,
			Key:       v.Key,
			ValueList: valueList,
		})
	}
	return data
}

// nolint
func getVersionCateTypeConfig(owningModule string, bizID int64, terminalType libc.DeviceTypeInt) map[string]string {
	data := make(map[string]string)
	switch owningModule {
	case lib.OnBoardingPayment + lib.PaymentPageID:
		list := obpay.TbPage.GetAll(0, "")
		for _, v := range list {
			textID := strconv.FormatInt(v.ID, 10)
			data[textID] = v.Title
		}
	case lib.PlanGenerationPage + lib.PlanGenerationPageID:
		list := plan.TbPlanPageGeneration.GetList()
		for _, v := range list {
			textID := strconv.FormatInt(v.ID, 10)
			data[textID] = v.Title
		}
	case lib.TallerOb + lib.TallerObSwitch:
		data = map[string]string{
			"1": "开",
			"2": "关",
		}
	case lib.IndexFullScreenPopup + lib.ScreenPopupID:
		list := top.TbTopOperate.GetAll()
		for _, v := range list {
			textID := strconv.FormatInt(v.ID, 10)
			data[textID] = v.Title
		}
	}
	return data
}

type StrongPayment struct {
	SelectStrongPayment1 string `json:"select_strong_payment1"`
	SelectStrongPayment2 string `json:"select_strong_payment2"`
	SelectStrongPayment3 string `json:"select_strong_payment3"`
	SelectStrongPayment4 string `json:"select_strong_payment4"`
	SelectStrongPayment5 string `json:"select_strong_payment5"`
}

// nolint
func int64SliceToBytes(slice []int64) []byte {
	// Create a byte slice to hold the binary representation
	result := make([]byte, len(slice)*8)

	// Convert each int64 to binary representation
	for i, v := range slice {
		binary.LittleEndian.PutUint64(result[i*8:], uint64(v))
	}

	return result
}
