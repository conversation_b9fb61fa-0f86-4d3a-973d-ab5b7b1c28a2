package admin

import (
	"crypto/md5" // nolint
	"encoding/hex"
	"net/url"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	conf "gitlab.dailyyoga.com.cn/server/children-admin-api/config"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
)

func GetAdminName(t *http.Context) string {
	adminNameInterface := t.GetHeader(library.HeaderOperatorName)
	var adminName string
	if adminNameInterface != "" {
		adminName, _ = url.QueryUnescape(adminNameInterface)
	}
	return adminName
}

// nolint
func CheckQiYuCs(md5 string, timestamp int64) bool {
	cfg := conf.Get().QiYuCs
	timeFormat := time.Unix(timestamp/1000, 0).Format("2006-01-02 15:04:05")
	hashInput := cfg.Md5Key + timeFormat + cfg.Target
	if md5Hash(hashInput) != md5 {
		return false
	}
	return true
}

// nolint
func md5Hash(str string) string {
	hash := md5.Sum([]byte(str))
	return hex.EncodeToString(hash[:])
}
