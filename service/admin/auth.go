package admin

import (
	"gitlab.dailyyoga.com.cn/gokit/middlewares/auth"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/config"
)

func GetAuthConfig() *auth.AdminConfig {
	return &auth.AdminConfig{
		Project:    config.Get().AdminAuth.Project,
		Enable:     config.Get().AdminAuth.Enable,
		VerifyURL:  config.Get().AdminAuth.VerifyURL,
		WhiteLists: config.Get().AdminAuth.WhiteLists,
		APIMap:     MouduleAuthAPI,
	}
}

var AdminModuleTypeEnum = struct {
	CommonModule       auth.AdminModuleType // 通用模块，如果一个接口在多个地方使用，例如产品列表 课程列表
	ProductInfo        auth.AdminModuleType // 产品信息
	PayPage            auth.AdminModuleType // 付费方案页配置
	HomeBanner         auth.AdminModuleType // 付费方案页配置
	UserInfoQuery      auth.AdminModuleType // 用户信息查询
	PayOrderInfo       auth.AdminModuleType // 支付订单查询
	CourseLibrary      auth.AdminModuleType // 课程列表
	UserGroupConfig    auth.AdminModuleType // 用户分群配置
	UserMemberChange   auth.AdminModuleType // 会员时间变动
	ObResourcePool     auth.AdminModuleType // OB流程资源池
	IosAuditSwitch     auth.AdminModuleType // iOS审核开关
	AndroidAuditSwitch auth.AdminModuleType // 安卓审核开关
	ComplianceConfig   auth.AdminModuleType // 合规
	ContainerList      auth.AdminModuleType // 容器列表
	IndexGroupContent  auth.AdminModuleType // 首页分群内容配置
	ABRopConfig        auth.AdminModuleType // AB实验配置
	ChangeChannel      auth.AdminModuleType // 更改渠道
	HomePageRecommend  auth.AdminModuleType // 首页课程推荐
	IosBgGroupConfig   auth.AdminModuleType // 苹果后台分组配置
	APPGrayIOS         auth.AdminModuleType // 灰度配置 ios
	APPGrayAndroid     auth.AdminModuleType // 灰度配置 android
	JuLiangSwitch      auth.AdminModuleType // 巨量开关
	UploadVideo        auth.AdminModuleType // 上传视频
	IntelligentConfig  auth.AdminModuleType // 智能课表配置模块
	ObProgram          auth.AdminModuleType // 智能课表配置
	PraiseResource     auth.AdminModuleType // 应用商店好评配置
	StorePraise        auth.AdminModuleType // 好评审核列表
	PrivacyProtSwitch  auth.AdminModuleType // 隐私协议开关
	SubscribeMode      auth.AdminModuleType // 支付宝签约方式
	AllScreenPopup     auth.AdminModuleType // 全屏弹窗
}{
	CommonModule:       "common_module",
	ProductInfo:        "product_info",
	PayPage:            "pay_page",
	HomeBanner:         "home_banner",
	UserInfoQuery:      "user_info_query",
	PayOrderInfo:       "pay_order_info",
	CourseLibrary:      "course_library",
	UserGroupConfig:    "user_group_config",
	UserMemberChange:   "user_member_change",
	ObResourcePool:     "ob_resource_pool",
	IosAuditSwitch:     "ios_audit_switch",
	AndroidAuditSwitch: "android_audit_switch",
	ComplianceConfig:   "compliance_config",
	ContainerList:      "container_list",
	IndexGroupContent:  "index_group_content",
	ABRopConfig:        "ab_rop_config",
	ChangeChannel:      "change_channel",
	HomePageRecommend:  "home_page_recommend",
	IosBgGroupConfig:   "ios_bggroup_Config",
	APPGrayIOS:         "app_gray_ios",
	APPGrayAndroid:     "app_gray_android",
	JuLiangSwitch:      "ju_liang_switch",
	UploadVideo:        "upload_video",
	IntelligentConfig:  "intelligent_config",
	ObProgram:          "ob_program",
	PraiseResource:     "praise_resource",
	StorePraise:        "store_praise",
	PrivacyProtSwitch:  "privacy_protocol_switch",
	SubscribeMode:      "subscribe_mode",
	AllScreenPopup:     "all_screen_popup",
}

var AdminModuleTypeDesc = map[auth.AdminModuleType]string{
	AdminModuleTypeEnum.CommonModule:       "通用模块",
	AdminModuleTypeEnum.ProductInfo:        "产品信息",
	AdminModuleTypeEnum.PayPage:            "付费方案页",
	AdminModuleTypeEnum.HomeBanner:         "首页资源位",
	AdminModuleTypeEnum.UserInfoQuery:      "用户信息查询",
	AdminModuleTypeEnum.PayOrderInfo:       "支付订单查询",
	AdminModuleTypeEnum.CourseLibrary:      "课程列表",
	AdminModuleTypeEnum.UserGroupConfig:    "用户分群配置",
	AdminModuleTypeEnum.UserMemberChange:   "会员时间变动",
	AdminModuleTypeEnum.ObResourcePool:     "OB流程资源池",
	AdminModuleTypeEnum.IosAuditSwitch:     "iOS审核开关",
	AdminModuleTypeEnum.AndroidAuditSwitch: "安卓审核开关",
	AdminModuleTypeEnum.ComplianceConfig:   "合规",
	AdminModuleTypeEnum.ContainerList:      "容器列表",
	AdminModuleTypeEnum.IndexGroupContent:  "首页分群内容配置",
	AdminModuleTypeEnum.ABRopConfig:        "AB实验配置",
	AdminModuleTypeEnum.ChangeChannel:      "更改渠道",
	AdminModuleTypeEnum.HomePageRecommend:  "首页课程推荐",
	AdminModuleTypeEnum.IosBgGroupConfig:   "苹果后台分组配置",
	AdminModuleTypeEnum.APPGrayIOS:         "ios灰度配置",
	AdminModuleTypeEnum.APPGrayAndroid:     "android灰度配置",
	AdminModuleTypeEnum.JuLiangSwitch:      "巨量开关",
	AdminModuleTypeEnum.UploadVideo:        "上传视频",
	AdminModuleTypeEnum.IntelligentConfig:  "智能课表配置",
	AdminModuleTypeEnum.ObProgram:          "智能课表内容管理",
	AdminModuleTypeEnum.PraiseResource:     "好评资源池",
	AdminModuleTypeEnum.StorePraise:        "好评审核列表",
	AdminModuleTypeEnum.PrivacyProtSwitch:  "隐私协议开关",
	AdminModuleTypeEnum.SubscribeMode:      "支付宝文案配置",
	AdminModuleTypeEnum.AllScreenPopup:     "全屏弹窗",
}

var OperateTypeEnum = struct {
	View      auth.AdminOperateType
	Edit      auth.AdminOperateType
	Delete    auth.AdminOperateType
	Examine   auth.AdminOperateType
	OnOffLine auth.AdminOperateType
}{
	View:    "view",    // 查看
	Edit:    "edit",    // 编辑
	Examine: "examine", // 审核
}

var MouduleAuthAPI = map[string]*auth.APIInfo{
	// 通用模块
	"/api/file/upload":          {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.CommonModule},
	"/api/file/batch/upload":    {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.CommonModule},
	"/api/client/qiniu/uptoken": {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.CommonModule},
	// 产品信息
	"/api/member/product/config": {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.CommonModule},
	"/api/member/product/info":   {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.ProductInfo},
	"/api/member/product/new":    {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ProductInfo},
	"/api/member/product/list":   {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.CommonModule},
	"/api/member/product/update": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ProductInfo},

	// 付费方案页
	"/api/pay/page/info":                    {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.PayPage},
	"/api/pay/page/update":                  {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.PayPage},
	"/api/pay/page/new":                     {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.PayPage},
	"/api/pay/page/list":                    {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.CommonModule},
	"/api/pay/page/delete":                  {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.PayPage},
	"/api/pay/page/copy":                    {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.PayPage},
	"/api/pay/page/header/Image/group/save": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.PayPage},
	"/api/pay/page/header/Image/group/copy": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.PayPage},
	"/api/pay/page/header/Image/group/del":  {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.PayPage},
	"/api/pay/page/header/Image/group/info": {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.CommonModule},
	"/api/pay/page/header/Image/group/list": {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.CommonModule},
	// 用户信息查询
	"/api/user/info":           {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.UserInfoQuery},
	"/api/user/order/list":     {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.UserInfoQuery},
	"/api/logoff/user/info":    {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.UserInfoQuery},
	"/api/user/subscribe_list": {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.UserInfoQuery},
	"/api/user/logoff/mobile":  {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.UserInfoQuery},
	"/api/user/logoff":         {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.UserInfoQuery},
	// 修改手机号
	"/api/user/update/mobile": {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.UserInfoQuery},
	// 支付订单查询
	"/api/order/search":        {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.PayOrderInfo},
	"/api/order/search/config": {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.PayOrderInfo},
	// 课程列表
	"/api/course/config":           {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.CourseLibrary},
	"/api/course/info":             {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.CourseLibrary},
	"/api/course/list":             {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.CommonModule},
	"/api/course/save":             {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.CourseLibrary},
	"/api/course/generation":       {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.CourseLibrary},
	"/api/course/export":           {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.CommonModule},
	"/api/course/gen/voice/yi/hao": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.CourseLibrary},
	"/api/course/import":           {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.CourseLibrary},

	// 全部配置
	"/api/course/label/info": {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.CourseLibrary},
	"/api/course/label/save": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.CourseLibrary},
	"/api/course/script":     {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.CourseLibrary},

	// 用户分群配置
	"/api/group_rule/new":             {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.UserGroupConfig},
	"/api/group_rule/update":          {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.UserGroupConfig},
	"/api/group_rule/info":            {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.UserGroupConfig},
	"/api/group_rule/list":            {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.UserGroupConfig},
	"/api/group_label/default_config": {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.UserGroupConfig},
	"/api/group_label/list":           {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.UserGroupConfig},
	"/api/group_rule/config_list":     {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.UserGroupConfig},
	"/api/group_tool/saveUserGroup":   {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.UserGroupConfig},
	"/api/group_tool/saveUserRop":     {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.UserGroupConfig},
	"/api/group_tool/delUserBinding":  {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.UserGroupConfig},
	// 会员时间变动
	"/api/user/vip/config":     {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.UserMemberChange},
	"/api/user/vip/change":     {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.UserMemberChange},
	"/api/user/vip/admin/list": {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.UserMemberChange},
	"/api/uid/csv/info":        {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.UserMemberChange},

	// 首页资源位
	"/api/home/<USER>/banner/config": {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.HomeBanner},
	"/api/home/<USER>/banner/info":   {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.HomeBanner},
	"/api/home/<USER>/banner/save":   {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.HomeBanner},
	"/api/home/<USER>/banner/online": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.HomeBanner},
	"/api/home/<USER>/banner/list":   {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.HomeBanner},
	"/api/home/<USER>/banner/del":    {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.HomeBanner},

	// iOS审核开关
	"/api/conf/audit/info":   {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.IosAuditSwitch},
	"/api/conf/audit/update": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.IosAuditSwitch},
	// 安卓审核开关
	"/api/android_audit/update":         {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.AndroidAuditSwitch},
	"/api/android_audit/info":           {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.AndroidAuditSwitch},
	"/api/android_audit/common_channel": {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.AndroidAuditSwitch},
	// 合规
	"/api/compliance_switch/holiday": {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.ComplianceConfig},
	"/api/compliance_switch/info":    {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.ComplianceConfig},
	"/api/compliance_switch/update":  {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ComplianceConfig},
	// 容器列表
	"/api/container/config": {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.ContainerList},
	"/api/container/create": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ContainerList},
	"/api/container/update": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ContainerList},
	"/api/container/info":   {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.ContainerList},
	"/api/container/list":   {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.ContainerList},
	"/api/container/del":    {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ContainerList},
	// 容器配置获取
	"/api/container/session_label/list": {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.ContainerList},
	// 首页分群内容配置
	"/api/container_group/create": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.IndexGroupContent},
	"/api/container_group/update": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.IndexGroupContent},
	"/api/container_group/info":   {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.IndexGroupContent},
	"/api/container_group/list":   {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.IndexGroupContent},
	"/api/container_group/del":    {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.IndexGroupContent},
	"/api/group_resource/add":     {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.IndexGroupContent},
	"/api/group_resource/list":    {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.IndexGroupContent},
	"/api/group_resource/update":  {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.IndexGroupContent},
	"/api/group_resource/del":     {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.IndexGroupContent},
	// 分累配置
	"/api/area/classify/conf":   {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.IndexGroupContent},
	"/api/area/classify/save":   {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.IndexGroupContent},
	"/api/area/classify/del":    {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.IndexGroupContent},
	"/api/area/classify/online": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.IndexGroupContent},
	"/api/area/classify/list":   {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.IndexGroupContent},
	"/api/area/classify/sort":   {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.IndexGroupContent},

	// AB实验配置
	"/api/rop/scene/config":              {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.ABRopConfig},
	"/api/rop/scene/create":              {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ABRopConfig},
	"/api/rop/scene/biz_list":            {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.ABRopConfig},
	"/api/rop/scene/biz_del":             {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ABRopConfig},
	"/api/rop/scene/biz_info":            {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.ABRopConfig},
	"/api/rop/scene/biz_time_update":     {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ABRopConfig},
	"/api/rop/experiment/create":         {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ABRopConfig},
	"/api/rop/experiment/percent_update": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ABRopConfig},
	"/api/rop/experiment/change_status":  {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ABRopConfig},
	"/api/rop/experiment/del":            {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ABRopConfig},
	"/api/rop/experiment/info":           {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.ABRopConfig},
	"/api/rop/version/create":            {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ABRopConfig},
	"/api/rop/version/del":               {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ABRopConfig},
	"/api/rop/version/update":            {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ABRopConfig},
	"/api/rop/version/percent_update":    {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ABRopConfig},

	// 更改渠道
	"/api/pay/page/conf":           {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.ChangeChannel},
	"/api/pay/page/change/channel": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ChangeChannel},
	"/api/pay/page/get/channel":    {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.ChangeChannel},

	// 巨量开关
	"/api/juliang/switch/info":   {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.JuLiangSwitch},
	"/api/juliang/switch/update": {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.JuLiangSwitch},

	// 智能课表配置
	"/api/intelligent_config/list":   {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.IntelligentConfig},
	"/api/intelligent_config/update": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.IntelligentConfig},
	"/api/intelligent_config/delete": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.IntelligentConfig},
	"/api/first_practice/info":       {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.IntelligentConfig},
	"/api/first_practice/update":     {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.IntelligentConfig},
	"/api/intelligent_config/create": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.IntelligentConfig},
	"/api/intelligent_config/info":   {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.IntelligentConfig},

	// 计划生成页
	"/api/ob/plan_page/save":   {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ObResourcePool},
	"/api/ob/plan_page/edit":   {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.ObResourcePool},
	"/api/ob/plan_page/delete": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ObResourcePool},
	"/api/ob/plan_page/list":   {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.ObResourcePool},
	"/api/ob/plan_page/copy":   {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ObResourcePool},

	// 智能课表内容管理
	"/api/obprogram/save":           {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ObProgram},
	"/api/obprogram/info":           {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.ObProgram},
	"/api/obprogram/del":            {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ObProgram},
	"/api/obprogram/list":           {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.ObProgram},
	"/api/obprogram/change":         {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ObProgram},
	"/api/ob/program/backward/days": {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.ObProgram},

	// 计划生成页
	"/api/plan/generation/list": {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.ObResourcePool},
	"/api/plan/generation/save": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ObResourcePool},
	"/api/plan/generation/info": {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.ObResourcePool},
	"/api/plan/generation/del":  {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.ObResourcePool},

	// 好评资源池
	"/api/store/praise/resource/config": {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.PraiseResource},
	"/api/store/praise/resource/save":   {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.PraiseResource},
	"/api/store/praise/resource/info":   {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.PraiseResource},
	"/api/store/praise/resource/list":   {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.PraiseResource},
	"/api/store/praise/resource/del":    {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.PraiseResource},

	// 应用商店好评列表
	"/api/store/praise/list":            {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.StorePraise},
	"/api/store/praise/export":          {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.StorePraise},
	"/api/store/praise/approve":         {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.StorePraise},
	"/api/conf/android_positive/info":   {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.StorePraise},
	"/api/conf/android_positive/update": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.StorePraise},
	// 隐私协议开关
	"/api/protocol/switch/update": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.PrivacyProtSwitch},
	"/api/protocol/switch/info":   {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.PrivacyProtSwitch},

	// 支付宝签约方式
	"/api/conf/offer/rop/save": {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.SubscribeMode},
	"/api/conf/offer/rop/info": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.SubscribeMode},

	// 全屏弹窗
	"/api/operate/index/config": {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.AllScreenPopup},
	"/api/operate/index/new":    {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.AllScreenPopup},
	"/api/operate/index/update": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.AllScreenPopup},
	"/api/operate/index/list":   {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.AllScreenPopup},
	"/api/operate/index/delete": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.AllScreenPopup},
	"/api/operate/index/online": {Operate: OperateTypeEnum.Edit, Module: AdminModuleTypeEnum.AllScreenPopup},
	"/api/operate/index/info":   {Operate: OperateTypeEnum.View, Module: AdminModuleTypeEnum.AllScreenPopup},
}
