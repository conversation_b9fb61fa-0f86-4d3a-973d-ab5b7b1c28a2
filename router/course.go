package routers

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/controllers/course"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/controllers/obprogram"
)

func courseRouters(s *http.Engine) {
	// 后台课程枚举接口
	s.Handle("GET", "/api/course/config", course.ConfigCourse)
	// 后台课程列表接口
	s.Handle("GET", "/api/course/list", course.ListCourse)
	// 保存课程
	s.Handle("POST", "/api/course/save", course.NewCourse)
	// 后台课程信息接口
	s.Handle("GET", "/api/course/info", course.InfoCourse)

	// 智能课表
	s.Handle("POST", "/api/obprogram/save", obprogram.ProgramSchedule.Save)
	// 智能课表详情
	s.Handle("GET", "/api/obprogram/info", obprogram.ProgramSchedule.Info)
	// 智能课表删除
	s.Handle("POST", "/api/obprogram/del", obprogram.ProgramSchedule.Del)
	// 智能课表删除
	s.Handle("GET", "/api/obprogram/list", obprogram.ProgramSchedule.List)
	// 倒退智能课表
	s.Handle("POST", "/api/ob/program/backward/days", course.ObProgramBackwardDays)
	// 课程导入
	s.Handle("POST", "/api/course/import", course.CourseImport)

	// 获取全局标签
	s.Handle("GET", "/api/course/label/info", course.InfoLabel)
	// 保存全局标签
	s.Handle("POST", "/api/course/label/save", course.SaveLabel)
	// 临时数据转换
	s.Handle("GET", "/api/course/script", course.ScriptCourse)
}
