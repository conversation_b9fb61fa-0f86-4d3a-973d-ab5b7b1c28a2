package routers

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/controllers/rop"
)

// ROP相关接口
func ropRouters(s *http.Engine) {
	// 场景
	s.<PERSON>le("GET", "/api/rop/scene/config", rop.RScene.GetConfigList)
	s.<PERSON>("POST", "/api/rop/scene/create", rop.RScene.CreateScene)
	s.<PERSON>le("GET", "/api/rop/scene/biz_list", rop.RScene.GetBizList)
	s.<PERSON>le("POST", "/api/rop/scene/biz_del", rop.RScene.DeleteSceneBiz)
	s.Handle("GET", "/api/rop/scene/biz_info", rop.RScene.GetBizDetail)
	s.Handle("POST", "/api/rop/scene/biz_time_update", rop.RScene.UpdateBizTime)

	// 实验
	s.Handle("POST", "/api/rop/experiment/create", rop.RExperiment.Create)
	s.<PERSON><PERSON>("POST", "/api/rop/experiment/percent_update", rop.RExperiment.PercentUpdate)
	s.Handle("POST", "/api/rop/experiment/change_status", rop.RExperiment.ChangeStatus)
	s.Handle("POST", "/api/rop/experiment/del", rop.RExperiment.Del)
	s.Handle("GET", "/api/rop/experiment/info", rop.RExperiment.Info)

	// 版本
	s.Handle("POST", "/api/rop/version/create", rop.RVersion.Create)
	s.Handle("POST", "/api/rop/version/percent_update", rop.RVersion.PercentUpdate)
	s.Handle("POST", "/api/rop/version/del", rop.RVersion.Del)
	s.Handle("POST", "/api/rop/version/update", rop.RVersion.Update)
}
