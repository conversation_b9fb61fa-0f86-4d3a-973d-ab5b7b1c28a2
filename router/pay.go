package routers

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/controllers/pay"
)

// 后台用户相关接口
func payRouters(s *http.Engine) {
	// 会员产品枚举接口
	s.Handle("GET", "/api/member/product/config", pay.AdminProduct.Config)
	// 会员产品信息更新接口
	s.Handle("POST", "/api/member/product/update", pay.AdminProduct.Update)
	// 会员产品信息接口
	s.Handle("GET", "/api/member/product/info", pay.AdminProduct.Info)
	// 会员产品信息保存接口
	s.Handle("POST", "/api/member/product/new", pay.AdminProduct.New)
	// 会员产品列表接口
	s.Handle("GET", "/api/member/product/list", pay.AdminProduct.List)
	// OB付费页信息查询
	s.Handle("GET", "/api/pay/page/info", pay.AdminPayPage.PageInfo)
	// 复制
	s.Handle("POST", "/api/pay/page/copy", pay.AdminPayPage.Copy)
	// OB付费页信息更新
	s.Handle("POST", "/api/pay/page/update", pay.AdminPayPage.UpdatePage)
	// OB付费页信息创建
	s.Handle("POST", "/api/pay/page/new", pay.AdminPayPage.NewInfo)
	// OB付费页信息列表
	s.Handle("GET", "/api/pay/page/list", pay.AdminPayPage.PageList)
	// OB付费页信息删除
	s.Handle("POST", "/api/pay/page/delete", pay.AdminPayPage.DeletePage)
	// OB跳过逻辑
	s.Handle("POST", "/api/ob/skip/button/num/save", pay.AdminPayPage.ObSkipNumSave)
	// OB跳过逻辑
	s.Handle("GET", "/api/ob/skip/button/num/info", pay.AdminPayPage.ObSkipNumInfo)
	// ios sku 分组保存
	s.Handle("POST", "/api/member/product/ios_group_save", pay.AdminIosOfferGroup.Save)
	// ios sku 分组保存
	s.Handle("POST", "/api/member/product/ios_group_update", pay.AdminIosOfferGroup.Save)
	// ios sku 分组详情
	s.Handle("GET", "/api/member/product/ios_group_edit", pay.AdminIosOfferGroup.Info)
	// 会员产品信息保存接口
	s.Handle("POST", "/api/member/product/ios_group_del", pay.AdminIosOfferGroup.Del)
	// ios sku 分组列表
	s.Handle("GET", "/api/member/product/ios_group_list", pay.AdminIosOfferGroup.List)
}
