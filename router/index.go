package routers

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/controllers/index"
)

// 首页路由
func indexRouters(s *http.Engine) {
	// 容器枚举
	s.Handle("GET", "/api/container/config", index.ContainerConfig)
	// 容器创建
	s.Handle("POST", "/api/container/create", index.ContainerNew)
	// 容器更新
	s.Handle("POST", "/api/container/update", index.ContainerUpdate)
	// 容器详情
	s.Handle("GET", "/api/container/info", index.ContainerInfo)
	// 容器列表
	s.Handle("GET", "/api/container/list", index.ContainerList)
	// 容器列表
	s.Handle("GET", "/api/container/del", index.ContainerDel)
	// 首页分群创建
	s.Handle("POST", "/api/container_group/create", index.NewGroup)
	// 首页分群更新
	s.Handle("POST", "/api/container_group/update", index.UpdateGroup)
	// 首页分群详情
	s.Handle("GET", "/api/container_group/info", index.GroupInfo)
	// 首页分群列表
	s.Handle("GET", "/api/container_group/list", index.ListGroup)
	// 首页删除接口
	s.Handle("GET", "/api/container_group/del", index.GroupDel)
	// 首页分群下添加容器
	s.Handle("POST", "/api/group_resource/add", index.GroupResourceAdd)
	// 首页分群下容器列表
	s.Handle("GET", "/api/group_resource/list", index.GroupCotainerList)
	// 首页分群下容器更新
	s.Handle("POST", "/api/group_resource/update", index.GroupResourceUpdate)
	// 首页分群下容器删除
	s.Handle("GET", "/api/group_resource/del", index.GroupContainerDel)
	// 保存首页容器
	s.Handle("POST", "/api/home_resource/save", index.HomeResourceSave)
	// 首页列表
	s.Handle("GET", "/api/home_resource/list", index.HomeResourceList)
	// info
	s.Handle("GET", "/api/home_resource/info", index.HomeResourceInfo)
	// copy
	s.Handle("POST", "/api/home_resource/copy", index.HomeContainerCop)
	// del
	s.Handle("POST", "/api/home_resource/del", index.HomeContainerDel)
	// 课程筛选配置
	s.Handle("GET", "/api/container/session_label/list", index.SessionLabelList)
}
