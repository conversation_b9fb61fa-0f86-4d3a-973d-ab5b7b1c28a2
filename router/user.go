package routers

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/controllers/admin"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/controllers/user"
)

// 后台用户相关接口
func userRouters(s *http.Engine) {
	// 后台登录接口
	s.Handle("POST", "/api/login", admin.AdminUser.Login)
	// 后台模块列表
	s.Handle("GET", "/api/admin_module", admin.AdminUser.ModuleInfo)
	// 后台功能tab接口
	s.Handle("GET", "/api/user/authority", user.Authority)
	// 文件上传接口
	s.Handle("POST", "/api/file/upload", admin.AdminFile.UploadFile)
	// 文件上传接口
	s.Handle("POST", "/api/qiyu/file/upload", admin.AdminFile.QiYuUploadFile)
	// 文件上传接口
	s.Handle("POST", "/api/file/batch/upload", admin.AdminFile.BatchUploadFile)
	// 用户信息查询接口
	s.Handle("GET", "/api/user/info", user.AccountInfo)
	// 用户订单查询接口
	s.Handle("GET", "/api/user/order/list", user.OrderList)
	// 注销用户信息查询接口
	s.Handle("GET", "/api/logoff/user/info", user.LogoffUserInfo)
	// 用户的签约列表
	s.Handle("GET", "/api/user/subscribe_list", user.SubscribeList)
	// 用户广告渠道修改
	s.Handle("GET", "/api/pay/page/conf", user.Conf)
	s.Handle("POST", "/api/pay/page/change/channel", user.UpdateADChannel)
	s.Handle("POST", "/api/pay/page/get/channel", user.GetChannel)
	// 解绑
	s.Handle("POST", "/api/user/logoff/mobile", user.LogoffMobile)
	// 修改手机号
	s.Handle("POST", "/api/user/update/mobile", user.LogoffMobileUpdate)
	// 注销
	s.Handle("POST", "/api/user/logoff", admin.AdminUser.LogoffAccount)
}
