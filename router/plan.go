package routers

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/controllers/plan"
)

func planRouters(s *http.Engine) {
	// 计划生成页 列表
	s.Handle("GET", "/api/plan/generation/list", plan.AdminPlanGeneration.List)
	// 计划生成页 更新
	s.Handle("POST", "/api/plan/generation/save", plan.AdminPlanGeneration.SaveInfo)
	// 计划生成页 详情
	s.Handle("GET", "/api/plan/generation/info", plan.AdminPlanGeneration.Info)
	// 计划生成页 删除
	s.Handle("POST", "/api/plan/generation/del", plan.AdminPlanGeneration.Del)
}
