package routers

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/controllers/user"
)

// 用户分群相关
func groupRouters(s *http.Engine) {
	// 创建用户分群
	s.Handle("POST", "/api/group_rule/new", user.GroupAdmin.NewInfo)
	// 更新用户分群
	s.Handle("POST", "/api/group_rule/update", user.GroupAdmin.UpdateInfo)
	// 获取用户分群详情
	s.Handle("GET", "/api/group_rule/info", user.GroupAdmin.EditInfo)
	// 分群列表
	s.Handle("GET", "/api/group_rule/list", user.GroupAdmin.ListInfo)
	// 分群规则分组标签
	s.Handle("GET", "/api/group_label/default_config", user.GroupAdmin.DefaultConfig)
	// 分群规则分组规则标签
	s.Handle("GET", "/api/group_label/list", user.GroupAdmin.GetGroupLabelList)
	// 分群规则枚举
	s.Handle("GET", "/api/group_rule/config_list", user.GroupAdmin.GroupRuleConfig)
	// 修改指定用户分群
	s.Handle("GET", "/api/group_tool/saveUserGroup", user.GroupAdmin.SaveUserGroup)
	// 修改指定用户rop
	s.Handle("GET", "/api/group_tool/saveUserRop", user.GroupAdmin.SaveUserRop)
	// 删除指定用户rop分群
	s.Handle("GET", "/api/group_tool/delUserBinding", user.GroupAdmin.DelUserBinding)
}
