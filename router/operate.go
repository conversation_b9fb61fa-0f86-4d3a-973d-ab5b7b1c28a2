package routers

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/controllers/top"
)

func operateRouters(s *http.Engine) {
	// 首页吸底小条配置
	s.Handle("GET", "/api/operate/index/config", top.AOperate.Config)
	// 新建首页吸底小条更新
	s.Handle("POST", "/api/operate/index/new", top.AOperate.NewInfo)
	// 首页吸底小条更新
	s.Handle("POST", "/api/operate/index/update", top.AOperate.UpdateInfo)
	// 首页吸底小条列表
	s.Handle("GET", "/api/operate/index/list", top.AOperate.ListInfo)
	// 首页吸底小条删除
	s.Handle("POST", "/api/operate/index/delete", top.AOperate.DeleteInfo)
	// 首页吸底小条上下线
	s.Handle("POST", "/api/operate/index/online", top.AOperate.OnlineInfo)
	// 首页吸底小条详情
	s.Handle("GET", "/api/operate/index/info", top.AOperate.Info)
}
