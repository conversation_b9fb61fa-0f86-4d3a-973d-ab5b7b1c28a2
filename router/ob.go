package routers

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/controllers/ob"
)

// 后台OB流程相关接口
func obRouters(s *http.Engine) {
	// OB流程图片枚举接口
	s.Handle("GET", "/api/ob/process/config", ob.AdminProcess.Config)
	// OB流程详情
	s.Handle("GET", "/api/ob/process/info", ob.AdminProcess.Detail)
	// OB流程更新
	s.Handle("POST", "/api/ob/process/update", ob.AdminProcess.Update)
	// OB流程创建
	s.Handle("POST", "/api/ob/process/create", ob.AdminProcess.Create)
	// OB流程列表
	s.Handle("GET", "/api/ob/process/list", ob.AdminProcess.List)
	// OB流程删除
	s.Handle("POST", "/api/ob/process/delete", ob.AdminProcess.Delete)
	// OB计划生成页创建
	s.Handle("POST", "/api/ob/plan_page/save", ob.AdminProcess.CreatePlanPage)
	// OB计划生成页详情
	s.Handle("GET", "/api/ob/plan_page/edit", ob.AdminProcess.EditPlanPage)
	// OB计划生成页删除
	s.Handle("POST", "/api/ob/plan_page/delete", ob.AdminProcess.DeletePlanPage)
	// OB计划生成页列表
	s.Handle("GET", "/api/ob/plan_page/list", ob.AdminProcess.PlanPageList)
	// OB计划生成页列表
	s.Handle("POST", "/api/ob/plan_page/copy", ob.AdminProcess.CopyPlanPage)
}
