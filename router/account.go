package routers

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/controllers/admin"
)

// APP用户相关接口
func accountRouters(s *http.Engine) {
	// 会员后台操作枚举值
	s.Handle("GET", "/api/user/vip/config", admin.AdminUser.VipConfig)
	// 会员后台操作加减
	s.Handle("POST", "/api/user/vip/change", admin.AdminUser.VipChange)
	// 会员后台操作加减记录
	s.Handle("GET", "/api/user/vip/admin/list", admin.AdminUser.VipAdminList)
	// 加载uid列表
	s.Handle("POST", "/api/uid/csv/info", admin.AdminUser.LoadUIDList)
}
