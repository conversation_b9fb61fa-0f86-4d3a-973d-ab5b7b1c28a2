package routers

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/controllers/admin"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/controllers/config"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/controllers/user"
)

// 客户端配置相关接口
// nolint
func clientRouters(s *http.Engine) {
	// ob rop config
	s.Handle("GET", "/api/conf/ob/page/rop/config", admin.AdminClient.ObPageRopConfig)
	// ob rop
	s.Handle("GET", "/api/conf/ob/page/rop/info", admin.AdminClient.ObPageRopInfo)
	// ob rop
	s.Handle("POST", "/api/conf/ob/page/rop/save", admin.AdminClient.ObPageRopSave)
	// 审核配置信息获取
	s.Handle("GET", "/api/conf/audit/info", admin.AdminClient.AuditInfo)
	// 审核配置信息更新
	s.Handle("POST", "/api/conf/audit/update", admin.AdminClient.AuditUpdate)
	// 安卓应用商店好评配置
	// 安卓应用商店好评配置更新
	// ip甄别创建/修改
	s.Handle("POST", "/api/conf/updateConfigIdentityIP", admin.AdminClient.UpdateIPIdentityConf)
	// ip甄别详情
	s.Handle("GET", "/api/conf/getConfigIdentityIP", admin.AdminClient.GetIPIdentityConf)
	// 七鱼在线客服开关
	s.Handle("GET", "/api/conf/customer_service/info", admin.AdminClient.CustomerServiceConfInfo)
	// 切换商户号配置枚举
	s.Handle("GET", "/api/alipay/product_shop/config", config.MAConfig.Config)
	// 切换商户号配置列表
	s.Handle("GET", "/api/alipay/product_shop/list", config.MAConfig.List)
	// 切换商户号配置详情
	s.Handle("GET", "/api/alipay/product_shop/info", config.MAConfig.Info)
	// 切换商户号配置创建
	s.Handle("POST", "/api/alipay/product_shop/create", config.MAConfig.Create)
	// 切换商户号配置更新
	s.Handle("POST", "/api/alipay/product_shop/update", config.MAConfig.Update)
	// 切换商户号配置删除
	s.Handle("POST", "/api/alipay/product_shop/delete", config.MAConfig.Delete)
	// 切换商户号配置检查是否有相同配置
	s.Handle("POST", "/api/alipay/product_shop/check", config.MAConfig.Check)
	// 合规开关修改
	s.Handle("POST", "/api/compliance_switch/update", admin.AdminClient.ComplianceConfigUpdate)
	// 合规开关详情
	s.Handle("GET", "/api/compliance_switch/info", admin.AdminClient.ComplianceConfigInfo)
	// 合规特殊时间日期
	s.Handle("GET", "/api/compliance_switch/holiday", admin.AdminClient.ComplianceHoliday)
	// 安卓审核调整
	s.Handle("POST", "/api/android_audit/update", admin.AdminClient.AndroidAuditConfigUpdate)
	// 安卓审核详情
	s.Handle("GET", "/api/android_audit/info", admin.AdminClient.AndroidAuditConfigInfo)
	// 常用渠道
	s.Handle("GET", "/api/android_audit/common_channel", admin.AdminClient.CommonChannel)
	// 巨量开关
	s.Handle("GET", "/api/juliang/switch/info", admin.AdminClient.GetJuLiangSwitchInfo)
	// 巨量开关
	s.Handle("POST", "/api/juliang/switch/update", admin.AdminClient.JuLiangSwitchSave)
	// 七牛云上传token获取
	s.Handle("GET", "/api/client/qiniu/uptoken", admin.AdminClient.GetQiNiuToken)
	// 支付宝签约文案rop
	s.Handle("POST", "/api/conf/offer/rop/save", admin.AdminClient.AliPaySigningCopyWritingSave)
	s.Handle("GET", "/api/conf/offer/rop/info", admin.AdminClient.AliPaySigningCopyWritingInfo)

	// 好评资源池配置信息
	s.Handle("GET", "/api/store/praise/resource/config", user.ResourceConfig)
	// 好评资源池配置保存
	s.Handle("POST", "/api/store/praise/resource/save", user.ResourceSave)
	// 好评资源池配置信息
	s.Handle("GET", "/api/store/praise/resource/info", user.ResourceInfo)
	// 好评资源池配置列表
	s.Handle("GET", "/api/store/praise/resource/list", user.ResourceList)
	// 好评资源池配置删除
	s.Handle("POST", "/api/store/praise/resource/del", user.ResourceDel)
	// 好评配置
	s.Handle("GET", "/api/conf/android_positive/info", user.PraiseConfig)
	// 好评配置更新
	s.Handle("POST", "/api/conf/android_positive/update", user.PraiseUpdate)

	// 应用商店好评列表
	s.Handle("GET", "/api/store/praise/list", user.StorePraiseList)
	// 应用商店好评列表
	s.Handle("GET", "/api/store/praise/export", user.StorePraiseExport)
	// 应用商店好评审核
	s.Handle("POST", "/api/store/praise/approve", user.StorePraiseAudit)

	// 隐私协议
	s.Handle("POST", "/api/protocol/switch/update", admin.AdminClient.PrivacyProtocolSwitchSave)
	s.Handle("GET", "/api/protocol/switch/info", admin.AdminClient.PrivacyProtocolSwitchInfo)
}
