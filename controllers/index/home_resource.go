package index

import (
	"encoding/json"
	"strconv"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	db "gitlab.dailyyoga.com.cn/server/children-admin-api/databases/index"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
)

type ResourceItem struct {
	ContainerType int `json:"container_type"`
	ContainerID   int `json:"container_id"`
}

type HomeResourceItem struct {
	ID           int64           `json:"id"`
	Name         string          `json:"name"`
	ContainerNum int             `json:"container_num"`
	CreateTime   int64           `json:"create_time"`
	ResourceList []*ResourceItem `json:"resource_list"`
}

func HomeResourceSave(t *http.Context) {
	req := HomeResourceItem{}
	body := library.GetBody(t)
	if err := json.Unmarshal(body, &req); err != nil {
		t.Result(errorcode.InvalidParams, err.Error())
		return
	}
	resourceList, _ := json.Marshal(req.ResourceList)
	item := &db.HomeContainer{
		ID:           req.ID,
		Name:         req.Name,
		ResourceList: string(resourceList),
		IsDel:        library.No,
	}
	if item.ID == 0 {
		if err := item.Save(); err != nil {
			t.Result(errorcode.DBError, err.Error())
			return
		}
	} else {
		if err := item.Update(); err != nil {
			t.Result(errorcode.DBError, err.Error())
			return
		}
	}
	t.Result(library.EmptyResponse{})
}

func HomeResourceInfo(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	container := db.TbHomeContainer.GetItemByID(id)
	if container == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	item := formatHomeResource(container)
	if item == nil {
		t.Result(errorcode.DBError)
		return
	}
	t.Result(item)
}

func formatHomeResource(item *db.HomeContainer) *HomeResourceItem {
	res := &HomeResourceItem{
		ID:         item.ID,
		Name:       item.Name,
		CreateTime: item.CreateTime,
	}
	resourceList := make([]*ResourceItem, 0)
	if err := json.Unmarshal([]byte(item.ResourceList), &resourceList); err != nil {
		logger.Error(err)
		return nil
	}
	res.ResourceList = resourceList
	res.ContainerNum = len(res.ResourceList)
	return res
}

type HomeResourceRsp struct {
	List []*HomeResourceItem `json:"list"`
}

func HomeResourceList(t *http.Context) {
	req := &db.HomeContainerListReq{}
	t.ParseRequestStruct(req)
	var err error
	if req.ID, err = strconv.ParseInt(req.Name, 10, 64); err == nil {
		req.Name = ""
	}
	list := db.TbHomeContainer.GetList(req)
	res := &HomeResourceRsp{
		List: make([]*HomeResourceItem, 0),
	}
	if len(list) == 0 {
		t.Result(res)
		return
	}
	for _, v := range list {
		item := formatHomeResource(v)
		if item == nil {
			continue
		}
		res.List = append(res.List, item)
	}
	t.Result(res)
}

func HomeContainerDel(t *http.Context) {
	req := HomeResourceItem{}
	body := library.GetBody(t)
	if err := json.Unmarshal(body, &req); err != nil {
		t.Result(errorcode.InvalidParams, err.Error())
		return
	}
	item := db.TbHomeContainer.GetItemByID(req.ID)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	item.IsDel = library.Yes
	if err := item.Update(); err != nil {
		logger.Error(err)
		t.Result(errorcode.DBError)
		return
	}
	t.Result(library.EmptyResponse{})
}

func HomeContainerCop(t *http.Context) {
	req := HomeResourceItem{}
	body := library.GetBody(t)
	if err := json.Unmarshal(body, &req); err != nil {
		t.Result(errorcode.InvalidParams, err.Error())
		return
	}
	item := db.TbHomeContainer.GetItemByID(req.ID)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	item.ID = 0
	if err := item.Save(); err != nil {
		logger.Error(err)
		t.Result(errorcode.DBError)
		return
	}
	t.Result(library.EmptyResponse{})
}
