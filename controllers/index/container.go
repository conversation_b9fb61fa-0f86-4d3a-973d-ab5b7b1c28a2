package index

import (
	"encoding/json"
	"errors"
	"strings"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	db "gitlab.dailyyoga.com.cn/server/children-admin-api/databases/index"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
	libi "gitlab.dailyyoga.com.cn/server/children-admin-api/library/index"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/service/util"
)

type ContainerConfigConfig struct {
	ContainerType map[libi.ContainerTypeInt]string `json:"container_type"`
	LinkType      map[library.LinkTypeInt]string   `json:"link_type"`
}

func ContainerConfig(t *http.Context) {
	t.Result(&ContainerConfigConfig{
		ContainerType: libi.ContainerTypeDesc,
		LinkType: map[library.LinkTypeInt]string{
			library.LinkTypeEnum.Course:    "课程详情",
			library.LinkTypeEnum.Payment:   "付费方案页",
			library.LinkTypeEnum.InnerLink: "内链",
			library.LinkTypeEnum.Praise:    "好评活动",
		},
	})
}

type ContainerInfoItem struct {
	ID            int64         `json:"id"`
	Name          string        `json:"name"`
	BgName        string        `json:"bg_name"`
	BgImgPhone    string        `json:"bg_img_phone"`
	BgImgPad      string        `json:"bg_img_pad"`
	ContainerType int64         `json:"container_type"`
	BeginTime     int64         `json:"begin_time"`
	EndTime       int64         `json:"end_time"`
	CreateTime    int64         `json:"create_time"`
	UpdateTime    int64         `json:"update_time"`
	ResourceNum   int           `json:"resource_num"`
	ResourceList  []interface{} `json:"resource_list"`
	ConfigParam   Params        `json:"config_param"`
	Title         string        `json:"title,omitempty"`
	QuickTra      int           `json:"quick_training"`
}

type ContainerResourceItem struct {
	ResourceID     int64                  `json:"resource_id,omitempty"`
	ResourceType   int64                  `json:"resource_type"`
	MarketTitle    string                 `json:"market_title,omitempty"`
	MarketSubTitle string                 `json:"market_sub_title,omitempty"`
	Labels         string                 `json:"labels,omitempty"`
	TitleCover     string                 `json:"title_cover,omitempty"`
	BgType         int                    `json:"bg_type,omitempty"`
	BgColorStart   string                 `json:"bg_color_start,omitempty"`
	BgColorEnd     string                 `json:"bg_color_end,omitempty"`
	BgImg          string                 `json:"bg_img,omitempty"`
	AliasTitle     string                 `json:"alias_title,omitempty"`
	CourseList     []*ContainerCourseItem `json:"course_list,omitempty"`
}

type Params struct {
	LinkType       int    `json:"link_type"`
	LinkContent    string `json:"link_content,omitempty"`
	LinkContentIos string `json:"link_content_ios,omitempty"`
	KegelOB        int    `json:"kegel_ob,omitempty"`
	BannerImgPhone string `json:"banner_img_phone"`   // 图片地址
	BannerImgPad   string `json:"banner_img_pad"`     // 图片地址
	PraiseResID    int    `json:"praise_resource_id"` // 资源id
	SopID          int    `json:"sop_id,omitempty"`
}
type VideoStreaming struct {
	Sort               int    `json:"sort,omitempty"`
	VideoType          int    `json:"video_type,omitempty"`
	CoverImgPhone      string `json:"cover_img_phone,omitempty"`
	CoverImgPad        string `json:"cover_img_pad,omitempty"`
	PlanID             int    `json:"plan_id,omitempty"`
	CourseID           int    `json:"course_id,omitempty"`
	StartTimeMinute    int    `json:"start_time_minute"`
	EndTimeMinute      int    `json:"end_time_minute"`
	StartTimeSecond    int    `json:"start_time_second"`
	EndTimeSecond      int    `json:"end_time_second"`
	EffectiveStartTime int    `json:"effective_start_time"`
	EffectiveEndTime   int    `json:"effective_end_time"`
	VideoURL           string `json:"video_url,omitempty"`
}

type SessionFilter struct {
	LabelID      int                `json:"label_id"`
	SubLabelID   int                `json:"sub_label_id"`
	SubLabelName string             `json:"sub_label_name"`
	Image        *library.ImageInfo `json:"image"`
	Sort         int                `json:"sort"`
}
type ContainerCourseItem struct {
	ID           int64   `json:"id"`
	ResourceType int64   `json:"resource_type"`
	Title        string  `json:"title,omitempty"`
	Duration     float64 `json:"duration,omitempty"`
	Calorie      int64   `json:"calorie,omitempty"`
	HoriCover    string  `json:"horizontal_cover,omitempty"`
	SessionNums  int64   `json:"session_nums,omitempty"`
	AliasTitle   string  `json:"alias_title,omitempty"`
}

var NotAllowTitle = []string{
	"猜你喜欢",
	"推荐",
}

const (
	limit10 = 10
)

// nolint
func ContainerNew(t *http.Context) {
	params := &ContainerInfoItem{}
	t.ParseRequestStruct(params)
	if len(params.ResourceList) == 0 &&
		params.ContainerType != int64(libi.ContainerTypeEnum.CustomCard) &&
		params.ContainerType != int64(libi.ContainerTypeEnum.GuessLike) &&
		params.ContainerType != int64(libi.ContainerTypeEnum.RecentPractice) &&
		params.ContainerType != int64(libi.ContainerTypeEnum.CourseClassification) {
		t.Result(errorcode.InvalidParams)
		return
	}
	if params.ConfigParam.LinkType == 0 && params.ContainerType == int64(libi.ContainerTypeEnum.CustomCard) {
		t.Result(errorcode.InvalidParams)
		return
	}

	if params.QuickTra != library.Yes {
		params.QuickTra = library.No
	}
	item := &db.Container{
		Name:          params.Name,
		BgName:        params.BgName,
		ContainerType: params.ContainerType,
		BeginTime:     params.BeginTime,
		EndTime:       params.EndTime,
		BgImgPhone:    params.BgImgPhone,
		BgImgPad:      params.BgImgPad,
		QuickTra:      params.QuickTra,
		IsDel:         library.No,
	}
	rByte, err := json.Marshal(params.ResourceList)
	if err != nil {
		t.Result(errorcode.SystemError, err.Error())
		return
	}
	var resource string
	var jsonByte []byte
	switch libi.ContainerTypeInt(params.ContainerType) {
	case libi.ContainerTypeEnum.VideoStreaming:
		resourceList := make([]*VideoStreaming, 0)
		if err = json.Unmarshal(rByte, &resourceList); err != nil {
			t.Result(errorcode.InvalidParams, err.Error())
			return
		}
		jsonByte, _ = json.Marshal(resourceList)
		resource = string(jsonByte)
	case libi.ContainerTypeEnum.SessionFilter:
		resourceList := make([]*SessionFilter, 0)
		if err = json.Unmarshal(rByte, &resourceList); err != nil {
			t.Result(errorcode.InvalidParams, err.Error())
			return
		}
		if len(resourceList) > limit10 {
			t.Result(errorcode.InvalidParams, "二级分类筛选最多10个")
			return
		}
		jsonByte, _ = json.Marshal(resourceList)
		resource = string(jsonByte)
	default:
		resourceList := make([]*ContainerResourceItem, 0)
		if err = json.Unmarshal(rByte, &resourceList); err != nil {
			t.Result(errorcode.InvalidParams, err.Error())
			return
		}
		if err := checkParams(params, resourceList); err != nil {
			t.Result(errorcode.InvalidParams, err.Error())
			return
		}
		jsonByte, _ = json.Marshal(resourceList)
		resource = string(jsonByte)
	}
	item.ResourceList = resource
	params.ConfigParam.BannerImgPad = util.FormatImageInfoStr(params.ConfigParam.BannerImgPad)
	params.ConfigParam.BannerImgPhone = util.FormatImageInfoStr(params.ConfigParam.BannerImgPhone)
	rBytes, err := json.Marshal(params.ConfigParam)
	if err != nil {
		t.Result(errorcode.SystemError, err.Error())
		return
	}
	item.ConfigParam = string(rBytes)
	if err := item.Save(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

func checkParams(params *ContainerInfoItem, resourceList []*ContainerResourceItem) error {
	for _, v := range NotAllowTitle {
		if strings.Contains(params.Name, v) {
			return errors.New("容器标题不允许出现推荐、猜你喜欢")
		}
		for k := range resourceList {
			if strings.Contains(resourceList[k].MarketTitle, v) {
				return errors.New("营销标题不允许出现推荐、猜你喜欢")
			}
			if strings.Contains(resourceList[k].MarketSubTitle, v) {
				return errors.New("营销副标题不允许出现推荐、猜你喜欢")
			}
			max := 3
			if len(strings.Split(resourceList[k].Labels, ",")) > max {
				return errors.New("标签数量最大3个")
			}
		}
	}
	return nil
}

// nolint
func ContainerUpdate(t *http.Context) {
	params := &ContainerInfoItem{}
	t.ParseRequestStruct(params)
	if len(params.ResourceList) == 0 &&
		params.ContainerType != int64(libi.ContainerTypeEnum.CustomCard) &&
		params.ContainerType != int64(libi.ContainerTypeEnum.GuessLike) &&
		params.ContainerType != int64(libi.ContainerTypeEnum.RecentPractice) {
		t.Result(errorcode.InvalidParams)
		return
	}
	if params.ConfigParam.LinkType == 0 && params.ContainerType == int64(libi.ContainerTypeEnum.CustomCard) {
		t.Result(errorcode.InvalidParams)
		return
	}
	container := db.TbContainer.GetItemByID(params.ID)
	if container == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	if params.QuickTra != library.Yes {
		params.QuickTra = library.No
	}
	container.Name = params.Name
	container.BgName = params.BgName
	container.ContainerType = params.ContainerType
	container.BeginTime = params.BeginTime
	container.EndTime = params.EndTime
	container.BgImgPhone = params.BgImgPhone
	container.BgImgPad = params.BgImgPad
	container.Title = params.Title
	container.QuickTra = params.QuickTra
	rByte, err := json.Marshal(params.ResourceList)
	if err != nil {
		t.Result(errorcode.SystemError, err.Error())
		return
	}

	var resource string
	var jsonByte []byte
	switch libi.ContainerTypeInt(params.ContainerType) {
	case libi.ContainerTypeEnum.VideoStreaming:
		resourceList := make([]*VideoStreaming, 0)
		if err = json.Unmarshal(rByte, &resourceList); err != nil {
			t.Result(errorcode.InvalidParams, err.Error())
			return
		}
		jsonByte, _ = json.Marshal(resourceList)
		resource = string(jsonByte)
	case libi.ContainerTypeEnum.SessionFilter:
		resourceList := make([]*SessionFilter, 0)
		if err = json.Unmarshal(rByte, &resourceList); err != nil {
			t.Result(errorcode.InvalidParams, err.Error())
			return
		}
		if len(resourceList) > limit10 {
			t.Result(errorcode.InvalidParams, "二级分类筛选最多10个")
			return
		}
		jsonByte, _ = json.Marshal(resourceList)
		resource = string(jsonByte)
	default:
		resourceList := make([]*ContainerResourceItem, 0)
		if err = json.Unmarshal(rByte, &resourceList); err != nil {
			t.Result(errorcode.InvalidParams, err.Error())
			return
		}
		if err = checkParams(params, resourceList); err != nil {
			t.Result(errorcode.InvalidParams, err.Error())
			return
		}
		jsonByte, _ = json.Marshal(resourceList)
		resource = string(jsonByte)
	}
	container.ResourceList = resource
	params.ConfigParam.BannerImgPad = util.FormatImageInfoStr(params.ConfigParam.BannerImgPad)
	params.ConfigParam.BannerImgPhone = util.FormatImageInfoStr(params.ConfigParam.BannerImgPhone)
	rBytes, err := json.Marshal(params.ConfigParam)
	if err != nil {
		t.Result(errorcode.SystemError, err.Error())
		return
	}
	container.ConfigParam = string(rBytes)
	if err := container.Update(); err != nil {
		logger.Error(err)
		return
	}
	t.Result(library.EmptyResponse{})
}

func ContainerInfo(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	container := db.TbContainer.GetItemByID(id)
	if container == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	item := formatContainerItem(container)
	if item == nil {
		t.Result(errorcode.DBError)
		return
	}
	t.Result(item)
}

func formatContainerItem(item *db.Container) *ContainerInfoItem {
	res := &ContainerInfoItem{
		ID:            item.ID,
		Name:          item.Name,
		BgName:        item.BgName,
		ContainerType: item.ContainerType,
		BeginTime:     item.BeginTime,
		EndTime:       item.EndTime,
		BgImgPhone:    item.BgImgPhone,
		BgImgPad:      item.BgImgPad,
		CreateTime:    item.CreateTime,
		UpdateTime:    item.UpdateTime,
		Title:         item.Title,
		QuickTra:      item.QuickTra,
	}

	if item.ConfigParam != "" {
		config := Params{}
		err := json.Unmarshal([]byte(item.ConfigParam), &config)
		if err != nil {
			return nil
		}
		res.ConfigParam = config
		res.ConfigParam.BannerImgPad = util.UnmarshalImageStr(res.ConfigParam.BannerImgPad).URL
		res.ConfigParam.BannerImgPhone = util.UnmarshalImageStr(res.ConfigParam.BannerImgPhone).URL
	}
	rList := make([]interface{}, 0)
	if !util.InArray(int(item.ContainerType), []int{int(libi.ContainerTypeEnum.VideoStreaming),
		int(libi.ContainerTypeEnum.SessionFilter)}) {
		list := make([]*ContainerResourceItem, 0)
		err := json.Unmarshal([]byte(item.ResourceList), &list)
		if err != nil {
			return nil
		}
		// 历史数据没有resourceType所以这里需要判断并处理
		for _, v := range list {
			// 外层容器类型为2,3,6用
			if v.ResourceType == 0 {
				// 1课程 2计划
				v.ResourceType = libi.ContainerCourse
			}
			if len(v.CourseList) != 0 {
				// 内层 榜单用
				for _, courseInfo := range v.CourseList {
					if courseInfo.ResourceType == 0 {
						// 1课程 2计划
						courseInfo.ResourceType = libi.ContainerCourse
					}
				}
			}
			rList = append(rList, v)
		}
	} else {
		if err := json.Unmarshal([]byte(item.ResourceList), &rList); err != nil {
			logger.Error(err)
			return nil
		}
	}

	res.ResourceList = rList
	res.ResourceNum = len(rList)
	return res
}

type ContainerListRsp struct {
	List []*ContainerInfoItem `json:"list"`
}

func ContainerList(t *http.Context) {
	req := &db.ContainerListReq{}
	t.ParseRequestStruct(req)
	list := db.TbContainer.GetList(req)
	res := &ContainerListRsp{
		List: make([]*ContainerInfoItem, 0),
	}
	if len(list) == 0 {
		t.Result(res)
		return
	}
	for _, v := range list {
		item := formatContainerItem(v)
		if item == nil {
			continue
		}
		if item.ID == libi.DefaultCourseCollect || item.ID == libi.DefaultGuessLike ||
			item.ID == libi.DefaultRecentPractice {
			continue
		}
		res.List = append(res.List, item)
	}
	t.Result(res)
}

func ContainerDel(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	if id < 1 {
		t.Result(errorcode.InvalidParams)
		return
	}
	item := db.TbContainer.GetItemByID(id)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	item.IsDel = library.Yes
	if err := item.Update(); err != nil {
		logger.Error(err)
		t.Result(errorcode.DBError)
		return
	}
	containerList := db.TbGroupContainer.GetListByContainerID(item.ID)
	for k := range containerList {
		containerList[k].IsDel = library.Yes
		if err := containerList[k].Update(); err != nil {
			t.Result(errorcode.SystemError, err.Error())
			return
		}
	}
	t.Result(library.EmptyResponse{})
}
