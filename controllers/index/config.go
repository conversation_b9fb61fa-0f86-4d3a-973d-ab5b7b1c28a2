package index

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases/course"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
	libc "gitlab.dailyyoga.com.cn/server/children-admin-api/library/index"
)

type SessionLabelItem struct {
	ID       int64           `json:"id"`
	Name     string          `json:"name"`
	SubLabel []*SessionLabel `json:"sub_label"`
}
type SessionLabel struct {
	ID   int64  `json:"id"`
	Name string `json:"name"`
}

func SessionLabelList(t *http.Context) {
	data := course.TbLabelConfig.GetListByIDList(libc.ContainerLabelList)
	if len(data) < 1 {
		t.Result(errorcode.InvalidParams)
		return
	}
	pidList := make([]int, 0)
	for _, pItem := range data {
		pidList = append(pidList, int(pItem.ID))
	}
	pList := course.TbLabelConfig.GetListByPIDList(pidList)
	pListMap := make(map[int64][]*SessionLabel)
	for _, pv := range pList {
		if _, ok := pListMap[pv.Pid]; !ok {
			pListMap[pv.Pid] = make([]*SessionLabel, 0)
		}
		pListMap[pv.Pid] = append(pListMap[pv.Pid], &SessionLabel{
			ID:   pv.ID,
			Name: pv.Title,
		})
	}
	resp := make([]*SessionLabelItem, 0)
	for _, v := range data {
		item := &SessionLabelItem{
			ID:       v.ID,
			Name:     v.Title,
			SubLabel: make([]*SessionLabel, 0),
		}
		pVal, ok := pListMap[v.ID]
		if !ok {
			continue
		}
		item.SubLabel = pVal
		resp = append(resp, item)
	}
	t.Result(resp)
}
