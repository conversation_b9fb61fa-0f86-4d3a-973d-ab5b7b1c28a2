package index

import (
	"encoding/json"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases/group"
	db "gitlab.dailyyoga.com.cn/server/children-admin-api/databases/index"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
	libi "gitlab.dailyyoga.com.cn/server/children-admin-api/library/index"
)

type UserGroupItem struct {
	ID            int64  `json:"id"`
	UserGroupID   int64  `json:"user_group_id"`
	UserGroupName string `json:"user_group_name"`
	Remark        string `json:"remark"`
	Priority      int64  `json:"priority"`
	IsOnline      int32  `json:"is_online"`
	CreateTime    int64  `json:"create_time"`
	UpdateTime    int64  `json:"update_time"`
	ResourceTab   int    `json:"resource_tab"`
}

func NewGroup(t *http.Context) {
	params := &UserGroupItem{}
	t.ParseRequestStruct(params)
	if params.ResourceTab == 0 {
		params.ResourceTab = library.Yes
	}
	item := &db.UserGroup{
		UserGroupID: params.UserGroupID,
		Remark:      params.Remark,
		Priority:    params.Priority,
		IsOnline:    params.IsOnline,
		IsDel:       library.No,
		ResourceTab: params.ResourceTab,
	}
	repeat := db.TbUserGroup.GetRepeatItem(params.UserGroupID, 0, item.ResourceTab)
	if repeat != nil {
		t.Result(errorcode.SystemError, "分群已被关联")
		return
	}
	if err := item.Save(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

func UpdateGroup(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	if id < 1 {
		t.Result(errorcode.InvalidParams)
		return
	}
	item := db.TbUserGroup.GetItemByID(id)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	params := &UserGroupItem{}
	t.ParseRequestStruct(params)
	repeat := db.TbUserGroup.GetRepeatItem(params.UserGroupID, id, item.ResourceTab)
	if repeat != nil {
		t.Result(errorcode.SystemError, "分群已被关联")
		return
	}
	item.Remark = params.Remark
	item.UserGroupID = params.UserGroupID
	item.Priority = params.Priority
	item.IsOnline = params.IsOnline
	if err := item.Update(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

func ListGroup(t *http.Context) {
	isOnline := t.GetRequestIntD("is_online", 0)
	resourceTab := t.GetRequestIntD("resource_tab", library.Yes)
	var list []*db.UserGroup
	// 获取在线分群是在配置分群下的容器页面用
	if isOnline > 0 {
		list = db.TbUserGroup.GetListByStatus(isOnline, resourceTab)
	} else {
		list = db.TbUserGroup.GetList(resourceTab)
	}
	rlist := make([]*UserGroupItem, 0)
	if len(list) == 0 {
		t.Result(map[string]interface{}{
			"list": rlist,
		})
		return
	}
	for _, v := range list {
		if (v.ID == libi.DefaultIndexGroup ||
			v.ID == libi.DefaultIndexGroupHome) && isOnline == 0 {
			continue
		}
		rlist = append(rlist, formatItem(v))
	}
	t.Result(map[string]interface{}{
		"list": rlist,
	})
}
func formatItem(item *db.UserGroup) *UserGroupItem {
	op := &UserGroupItem{
		ID:          item.ID,
		UserGroupID: item.UserGroupID,
		Priority:    item.Priority,
		IsOnline:    item.IsOnline,
		Remark:      item.Remark,
		CreateTime:  item.CreateTime,
		UpdateTime:  item.UpdateTime,
	}
	groupItem := group.TbUserGroupRuleConfig.GetDetail(item.UserGroupID)
	if groupItem != nil {
		op.UserGroupName = groupItem.GroupName
	}
	if item.ID == libi.DefaultIndexGroup || item.ID == libi.DefaultIndexGroupHome {
		op.UserGroupName = "默认"
	}
	return op
}

func GroupInfo(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	if id < 1 {
		t.Result(errorcode.InvalidParams)
		return
	}
	item := db.TbUserGroup.GetItemByID(id)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	t.Result(formatItem(item))
}

func GroupDel(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	if id < 1 {
		t.Result(errorcode.InvalidParams)
		return
	}
	item := db.TbUserGroup.GetItemByID(id)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	item.IsDel = library.Yes
	if err := item.Update(); err != nil {
		logger.Error(err)
		t.Result(errorcode.DBError)
		return
	}
	t.Result(library.EmptyResponse{})
}

type GroupResourceAddReq struct {
	GroupID       int64 `json:"index_group_id"`
	Sort          int64 `json:"sort"`
	ContainerID   int64 `json:"container_id"`
	ContainerType int64 `json:"container_type"`
	ResourceTab   int64 `json:"resource_tab"`
}

// GroupResourceAdd 分群下面添加容器
// nolint
func GroupResourceAdd(t *http.Context) {
	params := &GroupResourceAddReq{}
	t.ParseRequestStruct(params)
	if params.ResourceTab == 0 {
		params.ResourceTab = library.Yes
	}
	if params.ContainerType == int64(libi.ContainerTypeEnum.GuessLike) {
		params.ContainerID = libi.DefaultGuessLike
		list := db.TbGroupContainer.GetList(params.GroupID, params.ContainerID, params.ResourceTab)
		if len(list) != 0 {
			t.Result(errorcode.InvalidParams, "猜你喜欢不能重复配置")
			return
		}
	}
	if params.ContainerType == int64(libi.ContainerTypeEnum.RecentPractice) {
		params.ContainerID = libi.DefaultRecentPractice
		list := db.TbGroupContainer.GetList(params.GroupID, params.ContainerID, params.ResourceTab)
		if len(list) != 0 {
			t.Result(errorcode.InvalidParams, "最近练习不能重复配置")
			return
		}
	}
	if params.ContainerType == int64(libi.ContainerTypeEnum.CourseCollect) {
		params.ContainerID = libi.DefaultCourseCollect
		iContainer := db.TbContainer.GetItemByType(params.ContainerType)
		if iContainer == nil {
			t.Result(errorcode.SystemError, "配置错误，请检查")
			return
		}
		params.ContainerID = iContainer.ID
		list := db.TbGroupContainer.ValidUnique(0, params.GroupID, iContainer.ID, params.ResourceTab)
		if len(list) != 0 {
			t.Result(errorcode.InvalidParams, "不能重复配置")
			return
		}
	}
	item := &db.GroupContainer{
		IndexGroupID: params.GroupID,
		ContainerID:  params.ContainerID,
		Sort:         params.Sort,
		IsDel:        library.No,
		ResourceTab:  params.ResourceTab,
	}
	if du := db.TbGroupContainer.GetGroupSameSort(params.GroupID, params.Sort, 0, params.ResourceTab); du != nil {
		t.Result(errorcode.InvalidParams, "不允许重复的排序值")
		return
	}
	if err := item.Save(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

type GroupResourceUpdateReq struct {
	ID   int64 `json:"id"`
	Sort int64 `json:"sort"`
}

// GroupResource 分群下面容器排序更新
func GroupResourceUpdate(t *http.Context) {
	params := &GroupResourceUpdateReq{}
	t.ParseRequestStruct(params)
	if params.ID <= 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	item := db.TbGroupContainer.GetItemByID(params.ID)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	if du := db.TbGroupContainer.GetGroupSameSort(item.IndexGroupID, params.Sort, item.ID, item.ResourceTab); du != nil {
		t.Result(errorcode.InvalidParams, "不允许重复的排序值")
		return
	}
	item.Sort = params.Sort
	if err := item.Update(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

func GroupContainerDel(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	if id < 1 {
		t.Result(errorcode.InvalidParams)
		return
	}
	item := db.TbGroupContainer.GetItemByID(id)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	item.IsDel = library.Yes
	if err := item.Update(); err != nil {
		logger.Error(err)
		t.Result(errorcode.DBError)
		return
	}
	t.Result(library.EmptyResponse{})
}

type GroupContainerListRsp struct {
	List []*GroupContainerListItem `json:"list"`
}

type GroupContainerListItem struct {
	ID            int64  `json:"id"`
	ContainerID   int64  `json:"container_id"`
	IndexGroupID  int64  `json:"index_group_id"`
	Sort          int64  `json:"sort"`
	Name          string `json:"name"`
	BgName        string `json:"bg_name"`
	BeginTime     int64  `json:"begin_time"`
	EndTime       int64  `json:"end_time"`
	ContainerType int64  `json:"container_type"`
	ResourceNum   int    `json:"resource_num"`
}

func GroupCotainerList(t *http.Context) {
	groupID := t.GetRequestInt64D("index_group_id", 0)
	containerID := t.GetRequestInt64D("container_id", 0)
	resourceTab := t.GetRequestInt64D("resource_tab", library.Yes)
	if groupID == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	list := db.TbGroupContainer.GetList(groupID, containerID, resourceTab)
	res := &GroupContainerListRsp{
		List: make([]*GroupContainerListItem, 0),
	}
	if len(list) == 0 {
		t.Result(res)
		return
	}
	for _, v := range list {
		item := formatGroupContainerItem(v)
		if item == nil {
			continue
		}
		res.List = append(res.List, item)
	}
	t.Result(res)
}

func formatGroupContainerItem(gcItem *db.GroupContainer) *GroupContainerListItem {
	res := &GroupContainerListItem{
		ID:           gcItem.ID,
		ContainerID:  gcItem.ContainerID,
		IndexGroupID: gcItem.IndexGroupID,
		Sort:         gcItem.Sort,
	}
	container := db.TbContainer.GetItemByID(gcItem.ContainerID)
	if container == nil || container.IsDel == library.Yes {
		return nil
	}
	res.Name = container.Name
	res.BgName = container.BgName
	res.ContainerType = container.ContainerType
	res.BeginTime = container.BeginTime
	res.EndTime = container.EndTime
	rList := make([]*ContainerResourceItem, 0)
	err := json.Unmarshal([]byte(container.ResourceList), &rList)
	if err == nil {
		res.ResourceNum = len(rList)
	}
	return res
}
