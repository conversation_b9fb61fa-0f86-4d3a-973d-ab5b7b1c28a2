package pay

import (
	"errors"
	"strings"

	"github.com/go-xorm/xorm"
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
	dbp "gitlab.dailyyoga.com.cn/server/children-admin-api/databases/product"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	libc "gitlab.dailyyoga.com.cn/server/children-admin-api/library/client"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
	libpay "gitlab.dailyyoga.com.cn/server/children-admin-api/library/pay"
	libproduct "gitlab.dailyyoga.com.cn/server/children-admin-api/library/product"
)

type aproduct struct {
}

var AdminProduct aproduct

type updateProductRequest struct {
	ID           int64   `json:"id"`
	Name         string  `json:"name"`
	ProductType  int     `json:"product_type"`
	PayType      string  `json:"pay_type"`
	OriginPrice  float64 `json:"origin_price"`
	Price        float64 `json:"price"`
	IsSubscribe  int     `json:"is_subscribe"`
	DefaultSkuID int     `json:"default_sku_id"`
	TerminalType int     `json:"terminal_type"`
	IOSProductID string  `json:"ios_product_id"`
	VipSku       int64   `json:"vip_sku"`
	OtherSku     int64   `json:"other_sku"`
	Remark       string  `json:"remark"`
	HasGift      int32   `json:"has_gift"`
	GiftInfo     string  `json:"gift_info"`
}

type productRequest struct {
	Name               string  `json:"name"`
	ProductType        int     `json:"product_type"`
	PayType            string  `json:"pay_type"`
	OriginPrice        float64 `json:"origin_price"`
	Price              float64 `json:"price"`
	IsSubscribe        int     `json:"is_subscribe"`
	DefaultSkuID       int     `json:"default_sku_id"`
	TerminalType       int     `json:"terminal_type"`
	IOSProductID       string  `json:"ios_product_id"`
	DurationType       int     `json:"duration_type"`
	DurationValue      int     `json:"duration_value"`
	SubscribeDesc      string  `json:"subscribe_desc"`
	VipType            int     `json:"vip_type"`
	OfferType          int     `json:"offer_type"`
	OfferFirstBuyPrice float64 `json:"offer_first_buy_price"`
	OfferFirstBuyCycle int     `json:"offer_first_buy_cycle"`
	OfferTrialPrice    float64 `json:"offer_trial_price"`
	OfferTrialDay      int     `json:"offer_trial_day"`
	VipSku             int64   `json:"vip_sku"`
	OtherSku           int64   `json:"other_sku"`
	Remark             string  `json:"remark"`
	HasGift            int32   `json:"has_gift"`
	GiftInfo           string  `json:"gift_info"`
}

type ConfigInfo struct {
	ProductType      map[int]string                     `json:"product_type"`
	ProductVoiceType map[int]string                     `json:"product_voice_type"`
	PayType          map[int]string                     `json:"pay_type"`
	TerminalType     map[libc.DeviceTypeInt]string      `json:"terminal_type"`
	OfferType        map[int]string                     `json:"offer_type"`
	DurationType     map[libproduct.DurationType]string `json:"duration_type"`
	ProductVipType   map[int]string                     `json:"product_vip_type"`
}

// Config 会员产品枚举信息接口
func (*aproduct) Config(t *http.Context) {
	t.Result(&ConfigInfo{
		ProductType:      libproduct.ProductTypeDesc,
		ProductVoiceType: libproduct.ProductVoiceType,
		PayType:          libpay.PayTypeDesc,
		TerminalType:     libc.TerminalTypeDesc,
		OfferType:        libproduct.OfferTypeDesc,
		DurationType:     libproduct.DurationTypeDesEnum,
		ProductVipType:   libproduct.ProductVipTypeDesc,
	})
}

// New 新增会员产品
// nolint
func (*aproduct) New(t *http.Context) {
	req := &productRequest{}
	t.ParseRequestStruct(req)
	if req.ProductType == 0 {
		req.ProductType = libproduct.ProductVipTypeVIP
	}
	if req.IOSProductID != "" {
		iosExist := dbp.TbWebProduct.GetByIosProductID(req.IOSProductID, 0)
		if iosExist != nil {
			t.Result(errorcode.SystemError, "ios产品ID重复")
			return
		}
	}
	if req.OfferType == 0 || req.OfferType == libproduct.ConstOfferTypeNo {
		req.OfferType = libproduct.ConstOfferTypeNo
		req.OfferFirstBuyPrice = 0
		req.OfferFirstBuyCycle = 0
		req.OfferTrialPrice = 0
		req.OfferTrialDay = 0
	}
	webProduct := &dbp.WebProduct{
		Name:               req.Name,
		ProductType:        req.ProductType,
		PayType:            req.PayType,
		OriginPrice:        req.OriginPrice,
		Price:              req.Price,
		IsSubscribe:        req.IsSubscribe,
		TerminalType:       int32(req.TerminalType),
		IOSProductID:       req.IOSProductID,
		DurationType:       req.DurationType,
		DurationValue:      req.DurationValue,
		SubscribeDesc:      req.SubscribeDesc,
		VipType:            library.Yes,
		OfferType:          req.OfferType,
		OfferFirstBuyPrice: req.OfferFirstBuyPrice,
		OfferFirstBuyCycle: req.OfferFirstBuyCycle,
		OfferTrialPrice:    req.OfferTrialPrice,
		OfferTrialDay:      req.OfferTrialDay,
		IsDel:              library.No,
		VipSku:             req.VipSku,
		OtherSku:           req.OtherSku,
		Remark:             req.Remark,
		HasGift:            req.HasGift,
		GiftInfo:           req.GiftInfo,
	}
	if err := validOffer(webProduct); err != nil {
		t.Result(errorcode.InvalidParams, err.Error())
		return
	}
	if err := webProduct.Save(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

func ProcessPackageSave(req *productRequest) error {
	product := dbp.TbWebProduct.GetItemByID(req.VipSku)
	if product == nil {
		return errors.New("会员产品错误")
	}
	req.ProductType = product.ProductType
	req.PayType = product.PayType
	req.IsSubscribe = product.IsSubscribe
	req.DurationType = product.DurationType
	req.DurationValue = product.DurationValue
	req.SubscribeDesc = product.SubscribeDesc
	req.OfferType = libproduct.ConstOfferTypeNo
	return nil
}

func ProcessPackageUpdate(packageProduct *dbp.WebProduct, vipSku int64, product *dbp.WebProduct) error {
	if product == nil {
		product = dbp.TbWebProduct.GetItemByID(vipSku)
		if product == nil {
			return errors.New("会员产品错误")
		}
	}
	packageProduct.ProductType = product.ProductType
	packageProduct.PayType = product.PayType
	packageProduct.IsSubscribe = product.IsSubscribe
	packageProduct.DurationType = product.DurationType
	packageProduct.DurationValue = product.DurationValue
	packageProduct.SubscribeDesc = product.SubscribeDesc
	packageProduct.OfferType = libproduct.ConstOfferTypeNo
	return nil
}

// nolint
func validOffer(data *dbp.WebProduct) error {
	if data.OfferType == libproduct.ConstOfferTypeNo {
		return nil
	}
	switch data.OfferType {
	case libproduct.ConstOfferTypeFirstBuy:
		if data.OfferFirstBuyPrice == 0 || data.OfferFirstBuyCycle < 1 || data.OfferFirstBuyCycle > 30 {
			return errors.New("首购优惠价配置错误")
		}
	case libproduct.ConstOfferTypeTrial:
		if data.OfferTrialDay == 0 || data.OfferTrialDay > 365 {
			return errors.New("试用价格周期配置错误")
		}
	case libproduct.ConstOfferTypeTrialFirstBuy:
		if data.OfferFirstBuyPrice == 0 || data.OfferFirstBuyCycle < 1 || data.OfferFirstBuyCycle > 30 {
			return errors.New("首购优惠价配置错误")
		}
		if data.OfferTrialDay == 0 || data.OfferTrialDay > 365 {
			return errors.New("试用价格周期配置错误")
		}
	default:
		return errors.New("未知优惠方式")
	}
	return nil
}

// Update 会员产品更新接口
// nolint
func (*aproduct) Update(t *http.Context) {
	req := &updateProductRequest{}
	t.ParseRequestStruct(req)
	if req.ID == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	product := dbp.TbWebProduct.GetItemByID(req.ID)
	if product == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	if req.IOSProductID != "" {
		iosExist := dbp.TbWebProduct.GetByIosProductID(req.IOSProductID, product.ID)
		if iosExist != nil {
			t.Result(errorcode.SystemError, "ios产品ID重复")
			return
		}
	}
	product.Name = req.Name
	product.OriginPrice = req.OriginPrice
	product.IOSProductID = req.IOSProductID
	product.VipSku = req.VipSku
	product.OtherSku = req.OtherSku
	product.Remark = req.Remark
	product.PayType = req.PayType
	product.HasGift = req.HasGift
	product.GiftInfo = req.GiftInfo
	session := databases.GetEngineMaster().NewSession()
	defer session.Close()
	err := product.UpdateMustCol(session)
	if err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	if err = session.Begin(); err != nil {
		t.Result(errorcode.DBError)
		return
	}
	defer func() {
		if err != nil {
			logger.Error(err)
			if err = session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()
	if product.VipType == libproduct.ProductVipTypeVIP {
		if err = PackageUpdate(session, product); err != nil {
			t.Result(errorcode.DBError)
			return
		}
	}
	if err = session.Commit(); err != nil {
		t.Result(errorcode.DBError)
		return
	}
	t.Result(library.EmptyResponse{})
}

func PackageUpdate(session *xorm.Session, product *dbp.WebProduct) error {
	productList := dbp.TbWebProduct.GetListByVipSku(product.ID)
	for i := range productList {
		if err := ProcessPackageUpdate(productList[i], product.VipSku, product); err != nil {
			return err
		}
		if err := productList[i].UpdateMustCol(session); err != nil {
			return err
		}
	}
	return nil
}

// Info 会员产品更新接口
func (*aproduct) Info(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	if id == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	product := dbp.TbWebProduct.GetItemByID(id)
	if product == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	item := &ProductItem{
		ID:                 product.ID,
		Name:               product.Name,
		ProductType:        product.ProductType,
		PayType:            product.PayType,
		TerminalType:       product.TerminalType,
		DurationType:       product.DurationType,
		DurationValue:      product.DurationValue,
		IsSubscribe:        product.IsSubscribe,
		SubscribeDesc:      product.SubscribeDesc,
		IOSProductID:       product.IOSProductID,
		IsDel:              product.IsDel,
		CreateTime:         product.CreateTime,
		UpdateTime:         product.UpdateTime,
		Price:              product.Price,
		OriginPrice:        product.OriginPrice,
		VipType:            product.VipType,
		OfferType:          product.OfferType,
		OfferFirstBuyPrice: product.OfferFirstBuyPrice,
		OfferFirstBuyCycle: product.OfferFirstBuyCycle,
		OfferTrialPrice:    product.OfferTrialPrice,
		OfferTrialDay:      product.OfferTrialDay,
		ProductTypeDesc:    libproduct.ProductTypeAll[product.ProductType],
		VipSku:             product.VipSku,
		OtherSku:           product.OtherSku,
		Remark:             product.Remark,
		HasGift:            product.HasGift,
		GiftInfo:           product.GiftInfo,
	}
	t.Result(item)
}

type ProductList struct {
	List []*ProductItem `json:"list"`
}

type ProductItem struct {
	ID                 int64   `json:"id"`
	Name               string  `json:"name"`
	ProductType        int     `json:"product_type"`
	ProductTypeDesc    string  `json:"product_type_desc"`
	PayType            string  `json:"pay_type"`
	TerminalType       int32   `json:"terminal_type"`
	DurationType       int     `json:"duration_type"`
	DurationValue      int     `json:"duration_value"`
	Price              float64 `json:"price"`
	OriginPrice        float64 `json:"origin_price"`
	IsSubscribe        int     `json:"is_subscribe"`
	SubscribeDesc      string  `json:"subscribe_desc"`
	IOSProductID       string  `json:"ios_product_id"`
	IsDel              int     `json:"is_del"`
	VipType            int     `json:"vip_type"`
	CreateTime         int64   `json:"create_time"`
	UpdateTime         int64   `json:"update_time"`
	OfferType          int     `json:"offer_type"`             // 优惠类型：1-无 2-首购 3-试用 4-试用+首购
	OfferFirstBuyPrice float64 ` json:"offer_first_buy_price"` // 首购优惠价
	OfferFirstBuyCycle int     `json:"offer_first_buy_cycle"`  // 首购优惠周期
	OfferTrialPrice    float64 `json:"offer_trial_price"`
	OfferTrialDay      int     `json:"offer_trial_day"`
	VipSku             int64   `json:"vip_sku"`
	OtherSku           int64   `json:"other_sku"`
	Remark             string  `json:"remark"`
	HasGift            int32   `json:"has_gift"`
	GiftInfo           string  `json:"gift_info"`
}

// List 会员产品更新接口
func (p *aproduct) List(t *http.Context) {
	isSubscribe := t.GetRequestIntD("is_subscribe", library.Const999)
	terminalType := t.GetRequestInt32D("terminal_type", 0)
	productType := t.GetRequestIntD("product_type", 0)
	price := t.GetRequestFloat64D("price", 0)
	name := t.GetRequestStringD("name", "")
	vipType := t.GetRequestIntD("vip_type", 0)
	offerType := t.GetRequestIntD("offer_type", 0)
	strongPaymentType := t.GetRequestIntD("strong_payment_type", 0)
	var products []dbp.WebProduct
	if isSubscribe <= library.No {
		products = dbp.TbWebProduct.GetListBySubscribe(isSubscribe, productType, price)
	} else {
		products = dbp.TbWebProduct.GetList(productType, price)
	}
	if len(products) == 0 {
		t.Result(&ProductList{})
		return
	}
	res := make([]*ProductItem, 0)
	for k := range products {
		if products[k].TerminalType != int32(libc.DeviceTypeEnum.All) &&
			terminalType > 0 && products[k].TerminalType != terminalType {
			continue
		}
		if !strings.Contains(products[k].Name, name) {
			continue
		}
		if !p.CheckList(vipType, offerType, strongPaymentType, &products[k]) {
			continue
		}
		item := &ProductItem{
			ID:                 products[k].ID,
			Name:               products[k].Name,
			ProductType:        products[k].ProductType,
			PayType:            products[k].PayType,
			TerminalType:       products[k].TerminalType,
			DurationType:       products[k].DurationType,
			DurationValue:      products[k].DurationValue,
			IsSubscribe:        products[k].IsSubscribe,
			SubscribeDesc:      products[k].SubscribeDesc,
			IOSProductID:       products[k].IOSProductID,
			IsDel:              products[k].IsDel,
			CreateTime:         products[k].CreateTime,
			UpdateTime:         products[k].UpdateTime,
			Price:              products[k].Price,
			OriginPrice:        products[k].OriginPrice,
			OfferType:          products[k].OfferType,
			OfferFirstBuyPrice: products[k].OfferFirstBuyPrice,
			OfferFirstBuyCycle: products[k].OfferFirstBuyCycle,
			OfferTrialPrice:    products[k].OfferTrialPrice,
			OfferTrialDay:      products[k].OfferTrialDay,
			VipType:            products[k].VipType,
			ProductTypeDesc:    libproduct.ProductTypeAll[products[k].ProductType],
			VipSku:             products[k].VipSku,
			OtherSku:           products[k].OtherSku,
			Remark:             products[k].Remark,
			HasGift:            products[k].HasGift,
			GiftInfo:           products[k].GiftInfo,
		}
		res = append(res, item)
	}
	t.Result(&ProductList{List: res})
}

func (*aproduct) CheckIsShieldPlan(products *dbp.WebProduct) bool {
	otherProduct := dbp.TbWebProduct.GetItemByID(products.OtherSku)
	if otherProduct == nil {
		return false
	}
	return false
}

func (*aproduct) CheckList(vipType, offerType, strongPaymentType int, products *dbp.WebProduct) bool {
	if vipType != 0 && vipType != products.VipType {
		return false
	}
	if offerType > 0 && offerType != products.OfferType {
		return false
	}
	if strongPaymentType == libproduct.ProductType1Month && strongPaymentType != products.ProductType {
		return false
	}
	if strongPaymentType == libproduct.ProductType3Month && libproduct.ProductType1Month == products.ProductType {
		return false
	}
	return true
}
