package pay

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases/product"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
)

type iosOfferGroup struct {
}

var AdminIosOfferGroup iosOfferGroup

func (*iosOfferGroup) Save(t *http.Context) {
	req := &product.IosOfferGroup{}
	t.ParseRequestStruct(req)
	if req.GroupName == "" || req.SkuList == "" {
		t.Result(errorcode.InvalidParams, "组名 和 sku 不能为空")
		return
	}
	req.IsDel = library.No
	if req.ID == 0 {
		if err := req.Save(); err != nil {
			t.Result(errorcode.DBError, err.Error())
			return
		}
	} else {
		if err := req.Update(); err != nil {
			t.Result(errorcode.DBError, err.Error())
			return
		}
	}
	t.Result(errorcode.Success)
}

func (f *iosOfferGroup) Info(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	item := product.TbIosOfferGroup.GetItemByID(id)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	t.Result(item)
}

func (f *iosOfferGroup) List(t *http.Context) {
	item := product.TbIosOfferGroup.GetList()
	if item == nil {
		t.Result(make([]*product.IosOfferGroup, 0))
		return
	}
	t.Result(item)
}

type DelPrm struct {
	ID int64 `json:"id"`
}

func (f *iosOfferGroup) Del(t *http.Context) {
	params := &DelPrm{}
	t.ParseRequestStruct(params)
	item := product.TbIosOfferGroup.GetItemByID(params.ID)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	item.IsDel = library.Yes
	if err := item.Update(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(errorcode.Success)
}
