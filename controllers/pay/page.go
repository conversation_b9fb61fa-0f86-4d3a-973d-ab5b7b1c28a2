package pay

import (
	"encoding/json"
	"errors"
	"fmt"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
	dbclient "gitlab.dailyyoga.com.cn/server/children-admin-api/databases/client"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases/obpay"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases/product"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/client"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
	libproduct "gitlab.dailyyoga.com.cn/server/children-admin-api/library/product"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/service/util"
)

type paypage struct {
}

var AdminPayPage paypage

const (
	PayPageSkuNumber = 2
)

type APageInfo struct {
	ID                     int64       `json:"id"`
	Title                  string      `json:"title"`
	TerminalType           int32       `json:"terminal_type"`
	HeadImg                string      `json:"head_img"`
	HeadImgMan             string      `json:"head_img_man"`
	HeadVideo              string      `json:"head_video"`
	HeadVideoMan           string      `json:"head_video_man"`
	DescImg                string      `json:"desc_img"`
	SkuStyle               int32       `json:"sku_style"`
	RetainSkuID            int64       `json:"retain_sku_id"`
	RetainImg              string      `json:"retain_img"`
	RetainImgPad           string      `json:"retain_img_pad"`
	RetainSkuDesc          string      `json:"retain_sku_desc"`
	PayRetainSkuID         int64       `json:"pay_retain_sku_id"`
	PayRetainImg           string      `json:"pay_retain_img"`
	PayRetainSkuDesc       string      `json:"pay_retain_sku_desc"`
	BgImg                  string      `json:"bg_img"`
	BgVideo                string      `json:"bg_video"`
	BottomImg              string      `json:"bottom_img"`
	PayButtonImgPhone      string      `json:"pay_button_img_phone"`
	PayButtonImgPad        string      `json:"pay_button_img_pad"`
	SkuList                []*ASkuItem `json:"sku_list"`
	RetainImgSelected      string      `json:"retain_img_selected"`
	RetainImgNoSelected    string      `json:"retain_img_no_selected"`
	PayRetainImgSelected   string      `json:"pay_retain_img_selected"`
	PayRetainNoSelected    string      `json:"pay_retain_no_selected"`
	RetainOfferImg         string      `json:"retain_offer_image"`
	PayRetainOfferImg      string      `json:"pay_retain_offer_image"`
	RetainPopupID          int64       `json:"retain_popup_id"`
	PayRetainPopupID       int64       `json:"pay_retain_popup_id"`
	NotBuyPayRetainPopupID int64       `json:"not_buy_pay_retain_popup_id"`
}

type ASkuItem struct {
	ProductID        int64  `json:"product_id"`
	Img              string `json:"img"`
	SelectImg        string `json:"select_img"`
	Selected         int32  `json:"selected"`
	SkuDesc          string `json:"sku_desc"`
	OfferSelectImage string `json:"offer_select_image"`
	OfferImage       string `json:"offer_image"`
	OfferType        int    `json:"offer_type"`
	// 取消支付挽留配置
	RetainPopupID  int64  `json:"retain_popup_id"`
	RetainImg      string `json:"retain_img"`
	RetainOfferImg string `json:"retain_offer_img"`
	// 新SKU替换配置
	ReplaceType             int32  `json:"replace_type"`
	ReplaceProductID        int64  `json:"replace_product_id"`
	ReplaceImg              string `json:"replace_img"`
	ReplaceSelectImg        string `json:"replace_select_img"`
	ReplaceOfferImage       string `json:"replace_offer_image"`
	ReplaceOfferSelectImage string `json:"replace_offer_select_image"`
}

// nolint
func (*paypage) NewInfo(t *http.Context) {
	pageData := t.GetRequestStringD("page_data", "")
	params := &APageInfo{}
	err := json.Unmarshal([]byte(pageData), params)
	if err != nil {
		t.Result(errorcode.SystemError, err.Error())
		return
	}
	// sku不允许为空，
	if len(params.SkuList) == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	if params.RetainSkuID > 0 && params.RetainOfferImg == "" {
		productInfo := product.TbWebProduct.GetItemByID(params.RetainSkuID)
		if productInfo == nil {
			t.Result(errorcode.InvalidParams, "退出挽留弹窗配置无效产品ID")
			return
		}
		if productInfo.OfferType > libproduct.ConstOfferTypeNo {
			t.Result(errorcode.InvalidParams, "退出挽留弹窗sku未配置对应优惠素材")
			return
		}
	}
	if params.PayRetainSkuID > 0 && params.PayRetainOfferImg == "" {
		productInfo := product.TbWebProduct.GetItemByID(params.PayRetainSkuID)
		if productInfo == nil {
			t.Result(errorcode.InvalidParams, "支付挽留弹窗配置无效产品ID")
			return
		}
		if productInfo.OfferType > libproduct.ConstOfferTypeNo {
			t.Result(errorcode.InvalidParams, "支付挽留弹窗sku未配置对应优惠素材")
			return
		}
	}
	session := databases.GetEngineMaster().NewSession()
	defer session.Close()
	if err := session.Begin(); err != nil {
		logger.Error(err)
		t.Result(errorcode.DBError)
		return
	}
	defer func() {
		if err != nil {
			logger.Warn(err)
			if err = session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()
	pageInfo := &obpay.Page{
		Title:                  params.Title,
		TerminalType:           params.TerminalType,
		HeadImg:                util.FormatImageInfoStr(params.HeadImg),
		HeadImgMan:             util.FormatImageInfoStr(params.HeadImgMan),
		HeadVideo:              util.FormatVideoInfoStr(params.HeadVideo),
		HeadVideoMan:           util.FormatVideoInfoStr(params.HeadVideoMan),
		DescImg:                util.FormatImageInfoStr(params.DescImg),
		RetainSkuID:            params.RetainSkuID,
		PayRetainSkuID:         params.PayRetainSkuID,
		RetainImg:              util.FormatImageInfoStr(params.RetainImg),
		RetainImgPad:           util.FormatImageInfoStr(params.RetainImgPad),
		PayRetainImg:           util.FormatImageInfoStr(params.PayRetainImg),
		BgImg:                  util.FormatImageInfoStr(params.BgImg),
		BgVideo:                params.BgVideo,
		BottomImg:              util.FormatImageInfoStr(params.BottomImg),
		PayButtonImgPhone:      params.PayButtonImgPhone,
		PayButtonImgPad:        params.PayButtonImgPad,
		IsDel:                  library.No,
		RetainImgSelected:      util.FormatImageInfoStr(params.RetainImgSelected),
		RetainImgNoSelected:    util.FormatImageInfoStr(params.RetainImgNoSelected),
		PayRetainImgSelected:   util.FormatImageInfoStr(params.PayRetainImgSelected),
		PayRetainNoSelected:    util.FormatImageInfoStr(params.PayRetainNoSelected),
		SkuStyle:               params.SkuStyle,
		RetainOfferImg:         util.FormatImageInfoStr(params.RetainOfferImg),
		PayRetainOfferImg:      util.FormatImageInfoStr(params.PayRetainOfferImg),
		RetainPopupID:          params.RetainPopupID,
		PayRetainPopupID:       params.PayRetainPopupID,
		NotBuyPayRetainPopupID: params.NotBuyPayRetainPopupID,
	}
	if err = pageInfo.SaveByTran(session); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	for _, v := range params.SkuList {
		if v.ProductID > 0 {
			productInfo := product.TbWebProduct.GetItemByID(v.ProductID)
			if productInfo == nil {
				t.Result(errorcode.InvalidParams, "无效产品ID")
				return
			}
			if productInfo.OfferType > libproduct.ConstOfferTypeNo && (v.OfferSelectImage == "" || v.OfferImage == "") {
				err = errors.New("优惠产品需要配置优惠图")
				t.Result(errorcode.InvalidParams, "优惠产品需要配置优惠图")
				return
			}
		}
		if v.RetainPopupID > 0 {
			productInfo := product.TbWebProduct.GetItemByID(v.RetainPopupID)
			if productInfo == nil {
				t.Result(errorcode.InvalidParams, "无效产品ID")
				return
			}
			if productInfo.OfferType > libproduct.ConstOfferTypeNo && v.RetainOfferImg == "" {
				err = errors.New("优惠产品需要配置优惠图")
				t.Result(errorcode.InvalidParams, "优惠产品需要配置优惠图")
				return
			}
		}
		if v.ReplaceProductID > 0 {
			productInfo := product.TbWebProduct.GetItemByID(v.ReplaceProductID)
			if productInfo == nil {
				t.Result(errorcode.InvalidParams, "无效产品ID")
				return
			}
			if productInfo.OfferType > libproduct.ConstOfferTypeNo && (v.ReplaceOfferImage == "" || v.ReplaceOfferSelectImage == "") {
				err = errors.New("优惠产品需要配置优惠图")
				t.Result(errorcode.InvalidParams, "优惠产品需要配置优惠图")
				return
			}
		}
		skuItem := &obpay.PageSku{
			PageID:           pageInfo.ID,
			ProductID:        v.ProductID,
			Img:              util.FormatImageInfoStr(v.Img),
			SelectImg:        util.FormatImageInfoStr(v.SelectImg),
			OfferSelectImage: util.FormatImageInfoStr(v.OfferSelectImage),
			OfferImage:       util.FormatImageInfoStr(v.OfferImage),
			IsDel:            library.No,
			IsSelected:       v.Selected,
			// 取消支付挽留配置
			RetainPopupID:  v.RetainPopupID,
			RetainImg:      util.FormatImageInfoStr(v.RetainImg),
			RetainOfferImg: util.FormatImageInfoStr(v.RetainOfferImg),
			// 新SKU替换配置
			ReplaceType:             v.ReplaceType,
			ReplaceProductID:        v.ReplaceProductID,
			ReplaceImg:              util.FormatImageInfoStr(v.ReplaceImg),
			ReplaceSelectImg:        util.FormatImageInfoStr(v.ReplaceSelectImg),
			ReplaceOfferImage:       util.FormatImageInfoStr(v.ReplaceOfferImage),
			ReplaceOfferSelectImage: util.FormatImageInfoStr(v.ReplaceOfferSelectImage),
		}
		if err = skuItem.SaveByTran(session); err != nil {
			t.Result(errorcode.DBError, err.Error())
			return
		}
	}
	if err = session.Commit(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

// nolint
func (*paypage) UpdatePage(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	pageData := t.GetRequestStringD("page_data", "")
	params := &APageInfo{}
	err := json.Unmarshal([]byte(pageData), params)
	if err != nil {
		t.Result(errorcode.InvalidParams, err.Error())
		return
	}
	// sku不允许为空，
	if len(params.SkuList) == 0 || id == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	if params.RetainSkuID > 0 && params.RetainOfferImg == "" {
		productInfo := product.TbWebProduct.GetItemByID(params.RetainSkuID)
		if productInfo == nil {
			t.Result(errorcode.InvalidParams, "退出挽留弹窗配置无效产品ID")
			return
		}
		if productInfo.OfferType > libproduct.ConstOfferTypeNo {
			t.Result(errorcode.InvalidParams, "退出挽留弹窗sku未配置对应优惠素材")
			return
		}
	}
	if params.PayRetainSkuID > 0 && params.PayRetainOfferImg == "" {
		productInfo := product.TbWebProduct.GetItemByID(params.PayRetainSkuID)
		if productInfo == nil {
			t.Result(errorcode.InvalidParams, "支付挽留弹窗配置无效产品ID")
			return
		}
		if productInfo.OfferType > libproduct.ConstOfferTypeNo {
			t.Result(errorcode.InvalidParams, "支付挽留弹窗sku未配置对应优惠素材")
			return
		}
	}
	pageInfo := obpay.TbPage.GetItemByID(id)
	if pageInfo == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	session := databases.GetEngineMaster().NewSession()
	defer session.Close()
	if err := session.Begin(); err != nil {
		logger.Error(err)
		t.Result(errorcode.DBError)
		return
	}
	defer func() {
		if err != nil {
			logger.Warn(err)
			if err = session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()
	pageInfo.Title = params.Title
	pageInfo.TerminalType = params.TerminalType
	pageInfo.RetainSkuID = params.RetainSkuID
	pageInfo.PayRetainSkuID = params.PayRetainSkuID
	pageInfo.HeadImg = util.FormatImageInfoStr(params.HeadImg)
	pageInfo.HeadImgMan = util.FormatImageInfoStr(params.HeadImgMan)
	pageInfo.HeadVideo = util.FormatVideoInfoStr(params.HeadVideo)
	pageInfo.HeadVideoMan = util.FormatVideoInfoStr(params.HeadVideoMan)
	pageInfo.DescImg = util.FormatImageInfoStr(params.DescImg)
	pageInfo.RetainImg = util.FormatImageInfoStr(params.RetainImg)
	pageInfo.RetainImgPad = util.FormatImageInfoStr(params.RetainImgPad)
	pageInfo.PayRetainImg = util.FormatImageInfoStr(params.PayRetainImg)
	pageInfo.BgImg = util.FormatImageInfoStr(params.BgImg)
	pageInfo.BgVideo = params.BgVideo
	pageInfo.BottomImg = util.FormatImageInfoStr(params.BottomImg)
	pageInfo.PayButtonImgPhone = params.PayButtonImgPhone
	pageInfo.PayButtonImgPad = params.PayButtonImgPad
	pageInfo.RetainImgSelected = util.FormatImageInfoStr(params.RetainImgSelected)
	pageInfo.RetainImgNoSelected = util.FormatImageInfoStr(params.RetainImgNoSelected)
	pageInfo.PayRetainImgSelected = util.FormatImageInfoStr(params.PayRetainImgSelected)
	pageInfo.PayRetainNoSelected = util.FormatImageInfoStr(params.PayRetainNoSelected)
	pageInfo.SkuStyle = params.SkuStyle
	pageInfo.RetainOfferImg = util.FormatImageInfoStr(params.RetainOfferImg)
	pageInfo.PayRetainOfferImg = util.FormatImageInfoStr(params.PayRetainOfferImg)
	pageInfo.RetainPopupID = params.RetainPopupID
	pageInfo.PayRetainPopupID = params.PayRetainPopupID
	pageInfo.NotBuyPayRetainPopupID = params.NotBuyPayRetainPopupID
	if err = pageInfo.UpdateMustColByTran(session); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	skuItem := &obpay.PageSku{PageID: pageInfo.ID}
	if err = skuItem.DeleteByPageIDByTran(session); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	for _, v := range params.SkuList {
		if v.ProductID > 0 {
			productInfo := product.TbWebProduct.GetItemByID(v.ProductID)
			if productInfo == nil {
				t.Result(errorcode.InvalidParams)
				return
			}
			if productInfo.OfferType > libproduct.ConstOfferTypeNo && (v.OfferSelectImage == "" || v.OfferImage == "") {
				err = errors.New("优惠产品需要配置优惠图")
				t.Result(errorcode.InvalidParams, "优惠产品需要配置优惠图")
				return
			}
		}
		skuItem := &obpay.PageSku{
			PageID:           pageInfo.ID,
			ProductID:        v.ProductID,
			Img:              util.FormatImageInfoStr(v.Img),
			SelectImg:        util.FormatImageInfoStr(v.SelectImg),
			IsDel:            library.No,
			IsSelected:       v.Selected,
			OfferSelectImage: util.FormatImageInfoStr(v.OfferSelectImage),
			OfferImage:       util.FormatImageInfoStr(v.OfferImage),
			// 取消支付挽留配置
			RetainPopupID:  v.RetainPopupID,
			RetainImg:      util.FormatImageInfoStr(v.RetainImg),
			RetainOfferImg: util.FormatImageInfoStr(v.RetainOfferImg),
			// 新SKU替换配置
			ReplaceType:             v.ReplaceType,
			ReplaceProductID:        v.ReplaceProductID,
			ReplaceImg:              util.FormatImageInfoStr(v.ReplaceImg),
			ReplaceSelectImg:        util.FormatImageInfoStr(v.ReplaceSelectImg),
			ReplaceOfferImage:       util.FormatImageInfoStr(v.ReplaceOfferImage),
			ReplaceOfferSelectImage: util.FormatImageInfoStr(v.ReplaceOfferSelectImage),
		}
		if err = skuItem.SaveByTran(session); err != nil {
			t.Result(errorcode.DBError, err.Error())
			return
		}
	}
	if err = session.Commit(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

func (*paypage) PageInfo(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	if id == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	pageInfo := obpay.TbPage.GetItemByID(id)
	if pageInfo == nil {
		t.Result(errorcode.DBError)
		return
	}
	productMap := product.TbWebProduct.GetAllProductMap()
	if productMap == nil {
		t.Result(errorcode.DBError)
		return
	}
	t.Result(formatPayPage(pageInfo, productMap))
}

// 校验参数
func (p *paypage) SaveCheckParams(params *obpay.Page) error {
	if params.RetainSkuID > 0 && params.RetainOfferImg == "" {
		productInfo := product.TbWebProduct.GetItemByID(params.RetainSkuID)
		if productInfo == nil {
			return errors.New("退出挽留弹窗配置无效产品ID")
		}
		if productInfo.OfferType > libproduct.ConstOfferTypeNo {
			return errors.New("退出挽留弹窗sku未配置对应优惠素材")
		}
	}
	if params.PayRetainSkuID > 0 && params.PayRetainOfferImg == "" {
		productInfo := product.TbWebProduct.GetItemByID(params.PayRetainSkuID)
		if productInfo == nil {
			return errors.New("支付挽留弹窗配置无效产品ID")
		}
		if productInfo.OfferType > libproduct.ConstOfferTypeNo {
			return errors.New("支付挽留弹窗sku未配置对应优惠素材")
		}
	}
	return nil
}

func (p *paypage) Copy(t *http.Context) {
	params := &library.IDPrm{}
	t.ParseRequestStruct(params)
	pageInfo := obpay.TbPage.GetItemByID(params.ID)
	if pageInfo == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	pageInfo.ID = 0
	if err := p.SaveCheckParams(pageInfo); err != nil {
		t.Result(errorcode.InvalidParams, err.Error())
		return
	}
	var err error
	session := databases.GetEngineMaster().NewSession()
	defer session.Close()
	if err := session.Begin(); err != nil {
		logger.Error(err)
		t.Result(errorcode.DBError)
		return
	}
	defer func() {
		if pageInfo != nil {
			logger.Warn(err)
			if err = session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()
	if err := pageInfo.SaveByTran(session); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	skuList := obpay.TbPageSku.GetListByPageID(params.ID)
	if len(skuList) == 0 {
		t.Result(errorcode.DBError, "skuList不能为空")
		return
	}
	for _, v := range skuList {
		productInfo := product.TbWebProduct.GetItemByID(v.ProductID)
		if productInfo == nil {
			t.Result(errorcode.InvalidParams, "无效产品ID")
			return
		}
		if productInfo.OfferType > libproduct.ConstOfferTypeNo && (v.OfferSelectImage == "" || v.OfferImage == "") {
			err = errors.New("优惠产品需要配置优惠图")
			t.Result(errorcode.InvalidParams, "优惠产品需要配置优惠图")
			return
		}
		v.ID = 0
		v.PageID = pageInfo.ID
		if err = v.SaveByTran(session); err != nil {
			t.Result(errorcode.DBError, err.Error())
			return
		}
	}
	if err = session.Commit(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

func formatPayPage(pageInfo *obpay.Page, productMap map[int64]*product.WebProduct) *APageInfo {
	res := &APageInfo{
		ID:                     pageInfo.ID,
		Title:                  pageInfo.Title,
		TerminalType:           pageInfo.TerminalType,
		RetainSkuID:            pageInfo.RetainSkuID,
		PayRetainSkuID:         pageInfo.PayRetainSkuID,
		HeadImg:                util.UnmarshalImageStr(pageInfo.HeadImg).URL,
		HeadImgMan:             util.UnmarshalImageStr(pageInfo.HeadImgMan).URL,
		HeadVideo:              util.UnmarshalVideoStr(pageInfo.HeadVideo).URL,
		HeadVideoMan:           util.UnmarshalVideoStr(pageInfo.HeadVideoMan).URL,
		DescImg:                util.UnmarshalImageStr(pageInfo.DescImg).URL,
		PayRetainImg:           util.UnmarshalImageStr(pageInfo.PayRetainImg).URL,
		RetainImg:              util.UnmarshalImageStr(pageInfo.RetainImg).URL,
		RetainImgPad:           util.UnmarshalImageStr(pageInfo.RetainImgPad).URL,
		BgImg:                  util.UnmarshalImageStr(pageInfo.BgImg).URL,
		BgVideo:                pageInfo.BgVideo,
		BottomImg:              util.UnmarshalImageStr(pageInfo.BottomImg).URL,
		PayButtonImgPhone:      pageInfo.PayButtonImgPhone,
		PayButtonImgPad:        pageInfo.PayButtonImgPad,
		SkuList:                make([]*ASkuItem, 0),
		RetainImgSelected:      util.UnmarshalImageStr(pageInfo.RetainImgSelected).URL,
		RetainImgNoSelected:    util.UnmarshalImageStr(pageInfo.RetainImgNoSelected).URL,
		PayRetainImgSelected:   util.UnmarshalImageStr(pageInfo.PayRetainImgSelected).URL,
		PayRetainNoSelected:    util.UnmarshalImageStr(pageInfo.PayRetainNoSelected).URL,
		SkuStyle:               pageInfo.SkuStyle,
		RetainOfferImg:         util.UnmarshalImageStr(pageInfo.RetainOfferImg).URL,
		PayRetainOfferImg:      util.UnmarshalImageStr(pageInfo.PayRetainOfferImg).URL,
		RetainPopupID:          pageInfo.RetainPopupID,
		PayRetainPopupID:       pageInfo.PayRetainPopupID,
		NotBuyPayRetainPopupID: pageInfo.NotBuyPayRetainPopupID,
	}
	if v, ok := productMap[pageInfo.RetainPopupID]; ok {
		res.RetainSkuDesc = fmt.Sprintf("%s_%d_%.2f", v.Name, v.ID, v.Price)
	}
	if v, ok := productMap[pageInfo.PayRetainPopupID]; ok {
		res.PayRetainSkuDesc = fmt.Sprintf("%s_%d_%.2f", v.Name, v.ID, v.Price)
	}
	skuList := obpay.TbPageSku.GetListByPageID(pageInfo.ID)
	if len(skuList) == 0 {
		return res
	}
	for _, v := range skuList {
		item := &ASkuItem{
			ProductID:        v.ProductID,
			Img:              util.UnmarshalImageStr(v.Img).URL,
			SelectImg:        util.UnmarshalImageStr(v.SelectImg).URL,
			OfferSelectImage: util.UnmarshalImageStr(v.OfferSelectImage).URL,
			OfferImage:       util.UnmarshalImageStr(v.OfferImage).URL,
			Selected:         v.IsSelected,
			// 取消支付挽留配置
			RetainPopupID:  v.RetainPopupID,
			RetainImg:      util.UnmarshalImageStr(v.RetainImg).URL,
			RetainOfferImg: util.UnmarshalImageStr(v.RetainOfferImg).URL,
			// 新SKU替换配置
			ReplaceType:             v.ReplaceType,
			ReplaceProductID:        v.ReplaceProductID,
			ReplaceImg:              util.UnmarshalImageStr(v.ReplaceImg).URL,
			ReplaceSelectImg:        util.UnmarshalImageStr(v.ReplaceSelectImg).URL,
			ReplaceOfferImage:       util.UnmarshalImageStr(v.ReplaceOfferImage).URL,
			ReplaceOfferSelectImage: util.UnmarshalImageStr(v.ReplaceOfferSelectImage).URL,
		}
		if v, ok := productMap[v.ProductID]; ok {
			item.SkuDesc = fmt.Sprintf("%s_%d_%.2f", v.Name, v.ID, v.Price)
		}
		res.SkuList = append(res.SkuList, item)
	}
	return res
}

type PageListRsp struct {
	List []*APageInfo `json:"list"`
}

func (*paypage) PageList(t *http.Context) {
	terminalType := t.GetRequestIntD("terminal_type", 0)
	title := t.GetRequestStringD("title", "")
	pageList := obpay.TbPage.GetAll(terminalType, title)
	res := &PageListRsp{
		List: make([]*APageInfo, 0),
	}
	if len(pageList) == 0 {
		t.Result(res)
		return
	}
	productMap := product.TbWebProduct.GetAllProductMap()
	if productMap == nil {
		t.Result(errorcode.DBError)
		return
	}
	for _, v := range pageList {
		res.List = append(res.List, formatPayPage(v, productMap))
	}
	t.Result(res)
}

func (*paypage) DeletePage(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	// id为1的为默认数据，不允许删除
	if id <= 1 {
		t.Result(errorcode.InvalidParams)
	}
	pageInfo := obpay.TbPage.GetItemByID(id)
	if pageInfo == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	pageInfo.IsDel = library.Yes
	if err := pageInfo.Update(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

type SubscribeModeConfigItem struct {
	SubscribeMode int    `json:"subscribe_mode"`
	Name          string `json:"name"`
	Ratio         int32  `json:"ratio"`
}

type ChannelRes struct {
	Channel map[int]string `json:"channel"`
}

type ObSkipNumConf struct {
	SkipButtonNum int `json:"skip_button_num"`
	StartTimeDay  int `json:"start_time_day"`
}

func (p *paypage) ObSkipNumSave(t *http.Context) {
	req := &ObSkipNumConf{}
	t.ParseRequestStruct(req)
	if req.SkipButtonNum == 0 || req.StartTimeDay == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	valueByte, _ := json.Marshal(req)
	value := string(valueByte)
	info := dbclient.TbConfig.GetItemByKey(client.ObSkipNumConfig)
	if info == nil {
		item := &dbclient.Config{
			Key:     client.ObSkipNumConfig,
			Value:   value,
			KeyName: "OB流程跳过配置",
		}
		if err := item.Save(); err != nil {
			t.Result(errorcode.DBError, err)
			return
		}
	} else {
		info.Value = value
		if err := info.Update(); err != nil {
			t.Result(errorcode.DBError, err)
			return
		}
	}
	t.Result(library.EmptyResponse{})
}

func (p *paypage) ObSkipNumInfo(t *http.Context) {
	resp := ObSkipNumConf{}
	info := dbclient.TbConfig.GetItemByKey(client.ObSkipNumConfig)
	if info != nil {
		if err := json.Unmarshal([]byte(info.Value), &resp); err != nil {
			t.Result(errorcode.InvalidParams, err)
			return
		}
	}
	t.Result(resp)
}
