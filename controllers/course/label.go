package course

import (
	"sort"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases/course"
	libc "gitlab.dailyyoga.com.cn/server/children-admin-api/library/course"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
)

type Info struct {
	ID   int64  `json:"id"`
	Pid  int64  `json:"pid"`
	Name string `json:"name"`
	Sort int32  `json:"sort"`
}

func InfoLabel(t *http.Context) {
	data := course.TbLabelConfig.GetListByPIDList(libc.CourseLableList)
	if len(data) < 1 {
		t.Result(errorcode.InvalidParams)
		return
	}
	labelInfoList := make(map[int64][]*Info)
	for _, v := range data {
		if _, ok := labelInfoList[v.<PERSON>d]; !ok {
			labelInfoList[v.Pid] = make([]*Info, 0)
		}
		labelInfoList[v.Pid] = append(labelInfoList[v.Pid], &Info{
			ID:   v.ID,
			Pid:  v.Pid,
			Name: v.Title,
			Sort: v.Sort,
		})
	}
	resp := make(map[string][]*Info)
	for _, v := range libc.CourseLableList {
		labelKey, ok := libc.CourseLableKeyMap[v]
		if !ok {
			continue
		}
		if _, ok = resp[labelKey]; !ok {
			resp[labelKey] = make([]*Info, 0)
		}
		sort.Slice(labelInfoList[int64(v)], func(i, j int) bool {
			return labelInfoList[int64(v)][i].Sort < labelInfoList[int64(v)][j].Sort
		})
		resp[labelKey] = append(resp[labelKey], labelInfoList[int64(v)]...)
	}
	t.Result(resp)
}

type labelListReq struct {
	LabelList    []Info `json:"label_list"`
	TypeList     []Info `json:"type_list"`
	LevelList    []Info `json:"level_list"`
	AgeList      []Info `json:"age_list"`
	DurationList []Info `json:"duration_list"`
}

func SaveLabel(t *http.Context) {
	params := &labelListReq{}
	t.ParseRequestStruct(params)
	labelLists := map[int][]Info{
		libc.LabelCourseEffectPID:   params.LabelList,
		libc.LabelCourseTypePID:     params.TypeList,
		libc.LabelCourseLevelPID:    params.LevelList,
		libc.LabelCourseAgePID:      params.AgeList,
		libc.LabelCourseDurationPID: params.DurationList,
	}
	for k, list := range labelLists {
		for _, v := range list {
			v.Pid = int64(k)
			if err := SessionCourseTypeSave(v); err != nil {
				t.Result(errorcode.DBError, err.Error())
				continue
			}
		}
	}
	t.Result(errorcode.Success)
}

func SessionCourseTypeSave(v Info) error {
	item := course.LabelConfig{
		ID:    v.ID,
		Title: v.Name,
		Pid:   v.Pid,
		Level: 2,
		Sort:  v.Sort,
	}
	if v.ID == 0 {
		if err := item.Save(); err != nil {
			return err
		}
	} else {
		if err := item.Update(); err != nil {
			return err
		}
	}
	return nil
}
