package course

import (
	"encoding/json"
	"errors"
	"fmt"
	"math"
	"math/rand"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/go-xorm/xorm"
	"github.com/xuri/excelize/v2"
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases/course"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/client"
	libc "gitlab.dailyyoga.com.cn/server/children-admin-api/library/course"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/service/util"
)

type ConfigCourseRsp struct {
	LabelList    map[int64]string        `json:"label_list"`
	Parts        map[int64]string        `json:"parts"`
	Horizontal   map[int]string          `json:"horizontal"`
	CourseType   map[libc.TypeInt]string `json:"course_type"`
	CoachList    map[int64]string        `json:"coach_list"`
	TypeList     map[int64]string        `json:"type_list"`
	LevelList    map[int64]string        `json:"level_list"`
	AgeList      map[int64]string        `json:"age_list"`
	DurationList map[int64]string        `json:"duration_list"`
}

func ConfigCourse(t *http.Context) {
	res := &ConfigCourseRsp{
		LabelList:    make(map[int64]string),
		Parts:        make(map[int64]string),
		Horizontal:   libc.HorizontalEnumDesc,
		CoachList:    make(map[int64]string),
		TypeList:     make(map[int64]string),
		LevelList:    make(map[int64]string),
		AgeList:      make(map[int64]string),
		DurationList: make(map[int64]string),
	}
	labelList := course.TbLabelConfig.GetList()
	if len(labelList) > 0 {
		for _, v := range labelList {
			switch v.Pid {
			case libc.LabelCourseEffectPID:
				res.LabelList[v.ID] = v.Title
			case libc.LabelCoachPID:
				res.CoachList[v.ID] = v.Title
			case libc.LabelCourseTypePID:
				res.TypeList[v.ID] = v.Title
			case libc.LabelCourseLevelPID:
				res.LevelList[v.ID] = v.Title
			case libc.LabelCourseDurationPID:
				res.DurationList[v.ID] = v.Title
			case libc.LabelCourseAgePID:
				res.AgeList[v.ID] = v.Title
			}
		}
	}
	t.Result(res)
}

type ListRsp struct {
	List []*CItemAdmin `json:"list"`
}

type CItemAdmin struct {
	ID              int64    `json:"id"`
	Title           string   `json:"title"`
	Desc            string   `json:"desc"`
	Level           int32    `json:"level"`
	Cover           string   `json:"cover"`
	HorizontalCover string   `json:"horizontal_cover"`
	LabelIDList     []string `json:"label_id_list"`
	LabelIDListName []string `json:"label_id_list_name"`
	IsVIP           int32    `json:"is_vip"`
	IsOnline        int32    `json:"is_online"`
	Duration        float64  `json:"duration"`
	Calorie         int32    `json:"calorie"`
	CreateTime      int64    `json:"create_time"`
	UpdateTime      int64    `json:"update_time"`
	IsHorizontal    int32    `json:"is_horizontal"`
	VideoURL        string   `json:"video_url"`
	Coach           int64    `json:"coach"`
	Precautions     string   `json:"precautions"`
	IsNewCourse     int32    `json:"is_new_course"`
	TypeLabel       int32    `json:"type_label"`
	CustomLabels    []string `json:"custom_labels"`
	AgeLabel        []int32  `json:"age_label"`
	DurationLabel   int32    `json:"duration_label"`
}

// ListCourse 课程列表
func ListCourse(t *http.Context) {
	params := &course.ListQuery{}
	t.ParseRequestStruct(params)
	res := &ListRsp{
		List: make([]*CItemAdmin, 0),
	}
	list := course.TbCLibrary.GetListByAdminQuery(params)
	if len(list) == 0 {
		t.Result(res)
		return
	}
	labelList := course.TbLabelConfig.GetList()
	labelMap := make(map[int64]*course.LabelConfig)
	for _, v := range labelList {
		labelMap[v.ID] = v
	}
	needDetail := true
	maxDetail := 20
	if params.PageSize > int32(maxDetail) || params.PageSize == 0 {
		needDetail = false
	}
	for _, v := range list {
		res.List = append(res.List, formatCourseItem(v, labelMap, needDetail))
	}
	t.Result(res)
}

// nolint
func formatCourseItem(item *course.DBCourseLibrary,
	labelMap map[int64]*course.LabelConfig, needDetail bool) *CItemAdmin {
	res := &CItemAdmin{
		ID:              item.ID,
		Title:           item.Title,
		Desc:            item.Desc,
		Duration:        item.Duration,
		Cover:           item.CoverURL,
		HorizontalCover: item.HorizontalCoverURL,
		IsVIP:           item.IsVIP,
		IsOnline:        item.IsOnline,
		CreateTime:      item.CreateTime,
		UpdateTime:      item.UpdateTime,
		LabelIDList:     make([]string, 0),
		LabelIDListName: make([]string, 0),
		IsHorizontal:    item.IsHorizontal,
		VideoURL:        item.VideoURL,
		Coach:           item.Coach,
		Precautions:     item.Precautions,
		IsNewCourse:     item.IsNewCourse,
		CustomLabels:    make([]string, 0),
		AgeLabel:        make([]int32, 0),
	}
	if err := json.Unmarshal([]byte(item.CustomLabels), &res.CustomLabels); err != nil {
		res.CustomLabels = make([]string, 0)
	}
	if !needDetail {
		return res
	}
	labelList := course.TbLabelResource.GetListByResourceID(item.ID, libc.ResourceTypeEnum.Course)
	if len(labelList) > 0 {
		for _, v := range labelList {
			label := labelMap[v.LabelID]
			if label == nil {
				continue
			}
			switch label.Pid {
			case libc.LabelCourseEffectPID:
				res.LabelIDList = append(res.LabelIDList, fmt.Sprintf("%d", v.LabelID))
				res.LabelIDListName = append(res.LabelIDListName, label.Title)
			case libc.LabelCourseLevelPID:
				res.Level = int32(v.LabelID)
				ct := libc.LevelToCalorieRatio[libc.EnumByLevelDBID[int(v.LabelID)]]
				fenToSecond := 60
				var wt float64 = 70
				res.Calorie = int32(math.Round(ct * wt * item.Duration / float64(fenToSecond)))
			case libc.LabelCourseTypePID:
				res.TypeLabel = int32(v.LabelID)
			case libc.LabelCourseDurationPID:
				res.DurationLabel = int32(v.LabelID)
			case libc.LabelCourseAgePID:
				res.AgeLabel = append(res.AgeLabel, int32(v.LabelID))
			}
		}
	}
	res.LabelIDList = util.RemoveDuplicatesString(res.LabelIDList)
	return res
}

type CItemAdminSave struct {
	ID              int64    `json:"id"`
	Title           string   `json:"title"`
	Desc            string   `json:"desc"`
	Level           int32    `json:"level"`
	Cover           string   `json:"cover"`
	HorizontalCover string   `json:"horizontal_cover"`
	LabelIDList     []string `json:"label_id_list"`
	IsVIP           int32    `json:"is_vip"`
	VideoURL        string   `json:"video_url"`
	Duration        float64  `json:"duration"`
	IsHorizontal    int32    `json:"is_horizontal"`
	IsOnline        int32    `json:"is_online"`
	CustomLabels    []string `json:"custom_labels"`
	Coach           int64    `json:"coach"`
	Precautions     string   `json:"precautions"`
	IsNewCourse     int32    `json:"is_new_course"`
	TypeLabel       int32    `json:"type_label"`
	AgeLabel        []int32  `json:"age_label"`
	DurationLabel   int32    `json:"duration_label"`
}

func NewCourse(t *http.Context) {
	req := CItemAdminSave{}
	body := library.GetBody(t)
	if err := json.Unmarshal(body, &req); err != nil {
		t.Result(errorcode.InvalidParams, err.Error())
		return
	}
	err := SaveCourseIntegral(&req)
	if err != nil {
		t.Result(errorcode.InvalidParams, err.Error())
		return
	}
	t.Result(errorcode.Success)
}

func SaveCourseIntegral(req *CItemAdminSave) error {
	if req.ID == 0 {
		if err := SaveCourse(req); err != nil {
			return err
		}
	} else {
		if err := UpdateCourseIntegral(req); err != nil {
			return err
		}
		itemCourse := course.TbCLibrary.GetItem(req.ID)
		if itemCourse == nil {
			logger.Error("获取课程失败：", req.ID)
			return errors.New("获取课程失败")
		}
	}
	return nil
}

// nolint
func SaveCourse(req *CItemAdminSave) error {
	session := databases.GetEngineMaster().NewSession()
	var err error
	defer session.Close()
	if err := session.Begin(); err != nil {
		return err
	}
	defer func() {
		if err != nil {
			logger.Error(err)
			if err = session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()
	// nolint
	source := rand.New(rand.NewSource(time.Now().UnixNano()))
	// nolint
	rng := rand.New(source)
	videoURL := ""
	duration := 0.0
	isHorizontal := int32(0)
	isOnline := library.No

	videoURL = req.VideoURL
	duration = req.Duration
	isHorizontal = req.IsHorizontal
	isOnline = library.Yes
	if len(req.CustomLabels) == 0 {
		req.CustomLabels = make([]string, 0)
	}
	if req.IsNewCourse == 0 {
		req.IsNewCourse = library.No
	}
	customLabels, _ := json.Marshal(req.CustomLabels)
	courseLibrary := course.DBCourseLibrary{
		Title:              req.Title,
		Desc:               req.Desc,
		Level:              req.Level,
		CoverURL:           req.Cover,
		HorizontalCoverURL: req.HorizontalCover,
		IsVIP:              req.IsVIP,
		IsOnline:           int32(isOnline),
		PracticeNum:        int64(rng.Intn(2000) + 3000), // nolint
		VideoURL:           videoURL,
		Duration:           duration,
		IsHorizontal:       isHorizontal,
		CustomLabels:       string(customLabels),
		Coach:              req.Coach,
		Precautions:        req.Precautions,
		IsNewCourse:        req.IsNewCourse,
	}
	err = courseLibrary.SaveByTran(session)
	if err != nil {
		return err
	}
	if err := SaveLabelResource(req, courseLibrary.ID, session); err != nil {
		return err
	}
	err = session.Commit()
	if err != nil {
		return err
	}
	itemCourse := course.TbCLibrary.GetItem(courseLibrary.ID)
	if itemCourse == nil {
		logger.Error("获取课程失败：", courseLibrary.ID)
		return errors.New("获取课程失败")
	}
	return nil
}

func SaveLabelResource(req *CItemAdminSave, resourceID int64, session *xorm.Session) error {
	labelList := make([]*course.LabelResource, 0)

	// 处理标签ID列表
	for _, v := range req.LabelIDList {
		labelID, err := strconv.ParseInt(v, 10, 64)
		if err != nil {
			return err
		}
		labelList = append(labelList, createLabelResource(resourceID, labelID))
	}
	for _, v := range req.AgeLabel {
		labelList = append(labelList, createLabelResource(resourceID, int64(v)))
	}

	// 批量添加固定标签
	fixedLabels := []int64{
		int64(req.Level),
		int64(req.TypeLabel),
		int64(req.DurationLabel),
	}

	for _, labelID := range fixedLabels {
		if labelID > 0 { // 只添加有效的标签ID
			labelList = append(labelList, createLabelResource(resourceID, labelID))
		}
	}
	// 批量保存
	for _, v := range labelList {
		if err := v.SaveByTran(session); err != nil {
			return err
		}
	}
	return nil
}

// 创建标签资源的辅助函数
func createLabelResource(resourceID, labelID int64) *course.LabelResource {
	return &course.LabelResource{
		ResourceType: int64(libc.ResourceTypeEnum.Course),
		ResourceID:   resourceID,
		LabelID:      labelID,
		IsDel:        library.No,
	}
}

func UpdateCourseIntegral(req *CItemAdminSave) error {
	session := databases.GetEngineMaster().NewSession()
	var err error
	defer session.Close()
	if err := session.Begin(); err != nil {
		return err
	}
	defer func() {
		if err != nil {
			logger.Error(err)
			if err = session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()
	if len(req.CustomLabels) == 0 {
		req.CustomLabels = make([]string, 0)
	}
	item := course.TbCLibrary.GetItem(req.ID)
	if item == nil {
		logger.Error("获取课程失败：", req.ID)
		return errors.New("获取课程失败")
	}
	if item.IsNewCourse != library.Yes && req.IsNewCourse == library.Yes {
		item.NewCourseTime = time.Now().Unix()
	}
	customLabels, _ := json.Marshal(req.CustomLabels)
	courseLibrary := course.DBCourseLibrary{
		ID:                 req.ID,
		Title:              req.Title,
		Desc:               req.Desc,
		Level:              req.Level,
		CoverURL:           req.Cover,
		HorizontalCoverURL: req.HorizontalCover,
		IsVIP:              req.IsVIP,
		VideoURL:           req.VideoURL,
		Duration:           req.Duration,
		IsHorizontal:       req.IsHorizontal,
		IsOnline:           req.IsOnline,
		CustomLabels:       string(customLabels),
		Coach:              req.Coach,
		Precautions:        req.Precautions,
		IsNewCourse:        req.IsNewCourse,
		NewCourseTime:      item.NewCourseTime,
	}
	if err := courseLibrary.UpdateByTran(session); err != nil {
		return err
	}
	delRe := course.LabelResource{}
	if err := delRe.DelByTran(req.ID, make([]string, 0), session, int(libc.ResourceTypeEnum.Course)); err != nil {
		return err
	}
	if err := SaveLabelResource(req, courseLibrary.ID, session); err != nil {
		return err
	}
	err = session.Commit()
	if err != nil {
		return err
	}
	return nil
}

func InfoCourse(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	if id == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	item := course.TbCLibrary.GetItem(id)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	labelList := course.TbLabelConfig.GetList()
	labelMap := make(map[int64]*course.LabelConfig)
	for _, v := range labelList {
		labelMap[v.ID] = v
	}
	t.Result(formatCourseItem(item, labelMap, true))
}

type obProgramReq struct {
	UID          int64 `json:"uid"`
	IsPractice   int64 `json:"is_practice"`
	BackwardDays int64 `json:"backward_days"`
}

// nolint
func ObProgramBackwardDays(t *http.Context) {
	params := &obProgramReq{}
	t.ParseRequestStruct(params)
	inProcessProgram := course.TbObProgram.GetInProcessProgram(params.UID)
	if inProcessProgram == nil {
		t.Result(errorcode.Success)
		return
	}
	if params.BackwardDays > 21 {
		t.Result(errorcode.InvalidParams, "参数错误，倒退天数不能大于21天")
		return
	}
	inProcessProgram.BeginTime -= params.BackwardDays * 86400
	if err := inProcessProgram.Update(); err != nil {
		logger.Error(err)
		return
	}
	programCourseList := course.TbObProgramCourse.GetList(params.UID, inProcessProgram.ID)
	for _, v := range programCourseList {
		v.PracticeDate -= params.BackwardDays * 86400
		if params.IsPractice == library.Yes && time.Now().Unix() > v.PracticeDate {
			v.IsPractice = library.Yes
		}
		v.UID = params.UID
		if err := v.Update(); err != nil {
			logger.Error(err)
		}
	}
	t.Result(errorcode.Success)
}

func ScriptCourse(t *http.Context) {
	list := course.TbCLibrary.GetListByAdminQuery(&course.ListQuery{Page: 1, PageSize: 100})
	labelResource := make([]*course.LabelResource, 0)
	for _, v := range list {
		LevelDBID := libc.EnumByDBIDLevel[libc.LevelInt(v.Level)]
		lItem := &course.LabelResource{
			ResourceType: int64(libc.ResourceTypeEnum.Course),
			ResourceID:   v.ID,
			LabelID:      int64(LevelDBID),
			IsDel:        library.No,
		}
		courseTypeDBID := libc.TypeEnumDBID[libc.TypeInt(v.CourseType)]
		lItem2 := &course.LabelResource{
			ResourceType: int64(libc.ResourceTypeEnum.Course),
			ResourceID:   v.ID,
			LabelID:      int64(courseTypeDBID),
			IsDel:        library.No,
		}
		labelResource = append(labelResource, lItem, lItem2)
		v.NewCourseTime = v.CreateTime + v.ID
		if err := v.Update(); err != nil {
			logger.Error("ScriptCourse", err)
		}
	}
	for _, v := range labelResource {
		if v.LabelID == 0 {
			continue
		}
		item := course.TbLabelResource.GetByResourceID(v.ResourceID, v.LabelID, libc.ResourceTypeEnum.Course)
		if item != nil {
			continue
		}
		if err := v.Save(); err != nil {
			logger.Error(err)
		}
	}
	t.Result(errorcode.Success)
}

// 课程导入脚本
// nolint
func CourseImport(t *http.Context) {
	return
	// 从请求中获取本地文件路径
	filePath := "/Users/<USER>/Desktop/导入课程.xlsx"
	if filePath == "" {
		t.Result(errorcode.InvalidParams, "请提供Excel文件路径")
		return
	}

	// 检查文件扩展名
	ext := filepath.Ext(filePath)
	if ext != ".xlsx" && ext != ".xls" {
		t.Result(errorcode.InvalidParams, "请提供Excel文件(.xlsx或.xls)")
		return
	}

	// 检查文件是否存在
	if _, err := os.Stat(filePath); os.IsNotExist(err) {
		t.Result(errorcode.InvalidParams, "文件不存在")
		return
	}

	// 解析Excel
	xlsx, err := excelize.OpenFile(filePath)
	if err != nil {
		logger.Error("打开Excel文件失败:", err)
		t.Result(errorcode.SystemError, "解析Excel文件失败")
		return
	}

	// 获取第一个工作表
	sheetList := xlsx.GetSheetList()
	if len(sheetList) == 0 {
		t.Result(errorcode.InvalidParams, "Excel文件格式错误，未找到工作表")
		return
	}
	sheetName := sheetList[0]

	// 读取数据
	rows, err := xlsx.GetRows(sheetName)
	if err != nil || len(rows) <= 1 { // 至少要有标题行和一行数据
		t.Result(errorcode.InvalidParams, "Excel文件内容为空或格式不正确")
		return
	}

	// 获取教练列表，用于名称到ID的映射
	labelList := course.TbLabelConfig.GetList()
	coachMap := make(map[string]int64)
	for _, v := range labelList {
		if v.Pid == libc.LabelCoachPID {
			coachMap[v.Title] = v.ID
		}
	}

	// 跳过标题行
	dataRows := rows[1:]
	successCount := 0
	failCount := 0
	failReasons := make([]string, 0)

	// 开始导入
	for i, row := range dataRows {
		if len(row) < 9 { // 检查数据行是否基本完整，至少需要9个必要字段
			failCount++
			failReasons = append(failReasons, fmt.Sprintf("第%d行: 数据不完整", i+2))
			continue
		}

		// 初始化课程对象
		courseItem := &CItemAdminSave{
			CustomLabels: make([]string, 0),
			LabelIDList:  make([]string, 0),
		}

		// 课程类型
		courseType := row[0]

		// 1. 课程名称
		courseItem.Title = row[2]

		// 2. 课程时长
		duration, err := strconv.ParseFloat(row[3], 64)
		if err != nil {
			failCount++
			failReasons = append(failReasons, fmt.Sprintf("第%d行: 课程时长格式错误", i+2))
			continue
		}
		courseItem.Duration = duration * 60

		// 3. 课程类型
		// courseItem.CourseType = int32(libc.CourseTypeEnumDesc[row[4]])

		// 4. 老师姓名 - 需要转换为教练ID
		courseItem.Coach = 14

		// 5. 课程简介
		courseItem.Desc = row[6]

		if courseType == "体态课程" {
			courseType = "改善体态"
		}

		// 6. 课程封面（横版）
		courseItem.HorizontalCover = client.QiniuCDNResourceDomain + "/image/horizontal_cover/" + util.Byte2Md5([]byte(fmt.Sprintf("%s/%s", courseType, courseItem.Title))) + ".jpg"
		// 7. 课程封面（竖版）
		courseItem.Cover = client.QiniuCDNResourceDomain + "/image/cover/" + util.Byte2Md5([]byte(fmt.Sprintf("%s/%s", courseType, courseItem.Title))) + ".jpg"
		// 8. 课程视频地址
		courseItem.VideoURL = client.QiniuCDNResourceDomain + "/videos/" + util.Byte2Md5([]byte(fmt.Sprintf("%s/%s", courseType, courseItem.Title))) + ".mp4"
		// 9. 是否VIP
		courseItem.IsVIP = int32(library.YesNoEnumDesc[row[10]])

		// 10. 是否在线（如果有）
		courseItem.IsOnline = int32(library.YesNoEnumDesc[row[11]])

		// 11. 课程难度（如果有）
		courseItem.Level = int32(libc.LevelEnumDesc[row[12]])

		// 12. 横竖屏（如果有）
		courseItem.IsHorizontal = int32(libc.HorizontalEnum[row[13]])

		// 13. 是否新课（如果有）
		courseItem.IsNewCourse = int32(library.YesNoEnumDesc[row[14]])

		// 14. 课程标签（如果有）- 需要转换为标签ID
		if len(row) > 15 && row[15] != "" {
			tagNames := strings.Split(row[15], ",")
			for _, tagName := range tagNames {
				tagName = strings.TrimSpace(tagName)
				// 在这里需要查找标签名称对应的ID
				for _, label := range labelList {
					if label.Pid == libc.LabelCourseEffectPID && label.Title == tagName {
						courseItem.LabelIDList = append(courseItem.LabelIDList, fmt.Sprintf("%d", label.ID))
						break
					}
				}
			}
		}

		// 15. 自定义标签（如果有）
		if len(row) > 16 && row[16] != "" {
			customLabels := strings.Split(row[16], "、")
			for _, label := range customLabels {
				courseItem.CustomLabels = append(courseItem.CustomLabels, strings.TrimSpace(label))
			}
		}

		// 16. 注意事项（如果有）
		courseItem.Precautions = row[17]

		// 17. 练习建议（如果有）- 可能需要添加到数据结构中
		// 当前数据结构中没有练习建议字段，可以考虑添加到自定义标签或注意事项中
		//courseItem.Precautions = row[18]

		jsonData, _ := json.Marshal(courseItem)
		logger.Info("导入课程数据:", string(jsonData))
		// // 保存课程
		err = SaveCourseIntegral(courseItem)
		if err != nil {
			failCount++
			failReasons = append(failReasons, fmt.Sprintf("第%d行: 保存失败 - %s", i+2, err.Error()))
			continue
		}
		successCount++
	}
	// 返回导入结果
	t.Result(map[string]interface{}{
		"success_count": successCount,
		"fail_count":    failCount,
		"fail_reasons":  failReasons,
	})
}
