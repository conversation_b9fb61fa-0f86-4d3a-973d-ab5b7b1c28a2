package config

type ImageItem struct {
	ID  int    `json:"id"`
	Key string `json:"key"`
	Img string `json:"img"`
}

var ImageMap = []ImageItem{
	{ID: 1, Key: "transitionTarget", Img: "https://dancefitimage.dailyyoga.com.cn/577f8ece0a23b7778691c36d1fa9e7fe.png"},
	{ID: 2, Key: "purpose", Img: "https://fitnessimg.dailyworkout.cn/image/b86ae0eaac9a97268172e38e33bdf528.png"},
	{ID: 47, Key: "purposeKegel", Img: "https://fitnessimg.dailyworkout.cn/image/3d9afd3b26b13780ae1f7b81262ecec8.png"},
	{ID: 3, Key: "purposeAll", Img: "https://dancefitimage.dailyyoga.com.cn/cc1a6ab8d7667605a19c98d09d5eaf3c.png"},
	{ID: 4, Key: "bodyPosition", Img: "https://dancefitimage.dailyyoga.com.cn/69323e5c30559d408c83285fb892f942.png"},
	{ID: 5, Key: "bodyPositionAll", Img: "https://dancefitimage.dailyyoga.com.cn/fe114d6f85885ccd4d88eee60e21ae29.png"},
	{ID: 6, Key: "transitionBody", Img: "https://dancefitimage.dailyyoga.com.cn/31139c1b7915a0a4a4837078fbb36d20.png"},
	{ID: 7, Key: "gender", Img: "https://dancefitimage.dailyyoga.com.cn/4609159056529cda74e7a007068f0b44.png"},
	{ID: 8, Key: "age", Img: "https://dancefitimage.dailyyoga.com.cn/73ee50a304ad651f5e9fb1f25d2c3f8a.png"},
	{ID: 9, Key: "height", Img: "https://dancefitimage.dailyyoga.com.cn/dd4d94d2ea32a1b7d7694ccf42b20018.png"},
	{ID: 10, Key: "weightCurrent", Img: "https://fitnessimg.dailyworkout.cn/image/fa1eafd36dab087aae2bca54075d200b.jpg"},
	{ID: 11, Key: "weightTarget", Img: "https://dancefitimage.dailyyoga.com.cn/18c75b8b0711970734c20580af1e4857.png"},
	{ID: 12, Key: "bodyCurrent", Img: "https://dancefitimage.dailyyoga.com.cn/601469716e80bd7ac261dc404c457871.png"},
	{ID: 13, Key: "bodyTarget", Img: "https://dancefitimage.dailyyoga.com.cn/257bef9dc341a3e3b57c339b8d789c5a.png"},
	{ID: 14, Key: "resultBody", Img: "https://dancefitimage.dailyyoga.com.cn/3fd59a830517c1384c035961853c3b0c.png"},
	{ID: 15, Key: "transitionAboutYou", Img: "https://dancefitimage.dailyyoga.com.cn/7e6eb77bf2325c782c983aefd27f4102.png"},
	{ID: 16, Key: "condition", Img: "https://dancefitimage.dailyyoga.com.cn/40e6a03dae22785870a7f8b588938632.png"},
	{ID: 17, Key: "bodyHurt", Img: "https://dancefitimage.dailyyoga.com.cn/e87e31bb214eca8e9336b1c59c1b2630.png"},
	{ID: 18, Key: "reward", Img: "https://dancefitimage.dailyyoga.com.cn/9cec971c69ef4926c62e755adc513a7e.png"},
	{ID: 19, Key: "resultAboutYou", Img: "https://dancefitimage.dailyyoga.com.cn/a04d631aed9905f5a5d96402df2edd32.png"},
	{ID: 20, Key: "transitionSport", Img: "https://dancefitimage.dailyyoga.com.cn/257367439c4c6c52b8e0c84f7d32d861.png"},
	{ID: 21, Key: "practicePeriod", Img: "https://dancefitimage.dailyyoga.com.cn/c8277d8d6a642c497ad2b462cf9d9397.png"},
	{ID: 22, Key: "ability", Img: "https://dancefitimage.dailyyoga.com.cn/ad77f50e4a57e4d2a1aa030797ece436.png"},
	{ID: 23, Key: "apparatus", Img: "https://dancefitimage.dailyyoga.com.cn/390da85aaf37a26808fa89db47a00029.png"},
	{ID: 24, Key: "questionPracticeModel", Img: "https://dancefitimage.dailyyoga.com.cn/e26131f2a782f4ec55c919b813bb7f3f.png"},
	{ID: 25, Key: "questionSportGiveUp", Img: "https://dancefitimage.dailyyoga.com.cn/c0d77e856a94def0832ffdbff416f2a9.png"},
	{ID: 26, Key: "questionSportPersist", Img: "https://dancefitimage.dailyyoga.com.cn/302a03a367f7ee78575b9387f4465204.png"},
	{ID: 27, Key: "resultSport", Img: "https://dancefitimage.dailyyoga.com.cn/ed2d550bd92467a0ba826a3bec81f412.png"},
	{ID: 28, Key: "questionImageFigure", Img: "https://dancefitimage.dailyyoga.com.cn/bc345fc3819a3f3e7230ef4b8e46685a.png"},
	{ID: 29, Key: "questionImageAttraction", Img: "https://dancefitimage.dailyyoga.com.cn/6e69def9dcec413e9b10f7d3f8105a05.png"},
	{ID: 30, Key: "weightBeforeBlue", Img: "https://dancefitimage.dailyyoga.com.cn/3a42a787e07d22531d62aab738abf2c9.jpg"},
	{ID: 31, Key: "weightBeforeOrange", Img: "https://dancefitimage.dailyyoga.com.cn/02e9b3a9538ea82d13ee8c627ccc1c52.jpg"},
	{ID: 32, Key: "weightAfterBlueRobot", Img: "https://dancefitimage.dailyyoga.com.cn/d237b80aed0dfaf80d1ad3834434b802.jpg"},
	{ID: 33, Key: "weightAfterOrangeRobot", Img: "https://dancefitimage.dailyyoga.com.cn/c272e599f201f576f80a20314923d661.jpg"},
	{ID: 34, Key: "weightAfterBlueCoach", Img: "https://dancefitimage.dailyyoga.com.cn/49978f1a324cf97c2b9cfb72b2c90c9c.jpg"},
	{ID: 35, Key: "weightAfterOrangeCoach", Img: "https://dancefitimage.dailyyoga.com.cn/2155aca9591be308d71f4192707ae919.jpg"},
	{ID: 36, Key: "scanBody", Img: "https://dancefitimage.dailyyoga.com.cn/a96306daa573a21c13979e49c172282b.png"},
	{ID: 37, Key: "transitionVideo", Img: "https://dancefitimage.dailyyoga.com.cn/47526e7b27b2207555279bb476e05a2e.png"},
	{ID: 38, Key: "bodyInfoConfirm", Img: "https://fitnessimg.dailyworkout.cn/image/455941bf9320f02312c29eaa379c0e4a.png"},
	{ID: 39, Key: "emotionalLife", Img: "https://fitnessimg.dailyworkout.cn/image/fc95c30cbd407e5cb9ff2c77be551081.png"},
	{ID: 40, Key: "intimacyFrequency", Img: "https://fitnessimg.dailyworkout.cn/image/d560c6ef7ecd62eb70d1ac256922367f.png"},
	{ID: 41, Key: "intimacyTime", Img: "https://fitnessimg.dailyworkout.cn/image/33db49a0e1c181bbde735d96454ebfdd.png"},
	{ID: 42, Key: "kegelExam", Img: "https://fitnessimg.dailyworkout.cn/image/9abc1f5ffe0af6e4034f731346d329b8.png"},
	{ID: 43, Key: "maleAbility", Img: "https://fitnessimg.dailyworkout.cn/image/7ee3f4120645f4644ba8225e1b29d76b.png"},
	{ID: 44, Key: "smoke", Img: "https://fitnessimg.dailyworkout.cn/image/973e52783a36cc59b9b9687e90237aee.png"},
	{ID: 45, Key: "questionImageConfidence", Img: "https://fitnessimg.dailyworkout.cn/image/81af22d70181832d7d7a5ae0a86bf585.png"},
	{ID: 46, Key: "questionImageAttractionKegel", Img: "https://fitnessimg.dailyworkout.cn/image/4fc8544442f4c1b4b40ccc2d43a356e8.png"},
	{ID: 48, Key: "bodyTargetNew", Img: "https://fitnessimg.dailyworkout.cn/image/d47898a646bc3bc2b99cf32f706f6d0d.jpg"},
	{ID: 49, Key: "mealPlan", Img: "https://fitnessimg.dailyworkout.cn/image/d0f670123d7b47ec56f1d9bde74fe338.png"},
	{ID: 50, Key: "resultApparatus", Img: "https://fitnessimg.dailyworkout.cn/image/7eac045c7d6a60e70bd01026033e0193xayl.png"},
	{ID: 51, Key: "resultBodyNew", Img: "https://fitnessimg.dailyworkout.cn/image/04d9ee36197429c84bf44d72b17f015a.png"},
	{ID: 52, Key: "purposeThree", Img: "https://fitnessimg.dailyworkout.cn/image/4b1063d5b4f1106a3adcacf6b1de78a0.png"},
	{ID: 53, Key: "bodyCurrentNew", Img: "https://fitnessimg.dailyworkout.cn/image/1a50a4964f125d08a284a27ab0d6f714.jpg"},
	{ID: 54, Key: "resultRightPlace", Img: "https://fitnessimg.dailyworkout.cn/image/ff6a0ef664bebd52977af0e3d9b08cbb.png"},
	{ID: 55, Key: "conformToReality", Img: "https://fitnessimg.dailyworkout.cn/image/a9009252f65ee32502207dd7b45bb8d5.png"},
	{ID: 56, Key: "questionImageHealth", Img: "https://fitnessimg.dailyworkout.cn/image/99629f2cf41e07d285a5a3fcabde6faa.png"},
	{ID: 57, Key: "questionSportImproveHealth", Img: "https://fitnessimg.dailyworkout.cn/image/bf6e9ed871b5273ca31e289df769b12e.png"},
	{ID: 58, Key: "resultBMI", Img: "https://fitnessimg.dailyworkout.cn/image/75353503d3f76e99edcc4bed9bd84222.png"},
	{ID: 59, Key: "sessionLevel", Img: "https://fitnessimg.dailyworkout.cn/image/a8949494a212fb8e72e8a17fea643e91.png"},
	{ID: 60, Key: "sportActive", Img: "https://fitnessimg.dailyworkout.cn/image/6a59f3bfc21ef795bb4f3dfe05debe8e.png"},
	{ID: 61, Key: "kegelInquiry", Img: "https://fitnessimg.dailyworkout.cn/image/07bd3b2a63d074ab2f692f2ece4ab474.png"},
	{ID: 62, Key: "kegelPFMECount", Img: "https://fitnessimg.dailyworkout.cn/image/23a1fb7e6523552b928add1f6e4c0f07.png"},
	{ID: 63, Key: "kegelInstructions", Img: "https://fitnessimg.dailyworkout.cn/image/a777c4a74f79c3ddf89a10e708f40b8c.png"},
	{ID: 64, Key: "kegelIntoPlan", Img: "https://fitnessimg.dailyworkout.cn/image/476175df00b9910f5578ac07af9c54c8.png"},
	{ID: 65, Key: "sexualAbility", Img: "https://fitnessimg.dailyworkout.cn/image/89c2e62439669723a99c20f27ed01b89.png"},
	{ID: 66, Key: "questionImageConfidenceNew", Img: "https://fitnessimg.dailyworkout.cn/image/5960c62094d24f0ce8eb4f4c07fd0908.jpg"},
	{ID: 67, Key: "kegelInquiryNew", Img: "https://fitnessimg.dailyworkout.cn/image/8d708e728b5b97b082d8a9daddbd6f64.png"},
	{ID: 68, Key: "kegelInstructionsNew", Img: "https://fitnessimg.dailyworkout.cn/image/f87508eae64737cfd6ef92b619e560a0.jpg"},
	{ID: 69, Key: "kegelIntoPlanNew", Img: "https://fitnessimg.dailyworkout.cn/image/7ebf2380c466a77a2f6427c7bf049c09.jpg"},
	{ID: 70, Key: "sexualAbilityNew", Img: "https://fitnessimg.dailyworkout.cn/image/f8c42866c851acd0c3dc0233745386ba.jpg"},
	{ID: 71, Key: "sportPlace", Img: "https://fitnessimg.dailyworkout.cn/image/84b7624c72f916793cea53582664b0b9xayl.png"},
	{ID: 72, Key: "apparatusUnderstand", Img: "https://fitnessimg.dailyworkout.cn/image/af4627f658d99326a728c4e85e1fc4edxayl.png"},
	{ID: 73, Key: "gymPeriod", Img: "https://fitnessimg.dailyworkout.cn/image/a06e95cbd7b71d5889f9c283f6b0dcaaxayl.png"},
	{ID: 74, Key: "eatingHabits", Img: "https://fitnessimg.dailyworkout.cn/image/6c26655253c436740347a998d92a517bxayl.png"},
	{ID: 75, Key: "healthSummary", Img: "https://fitnessimg.dailyworkout.cn/image/a3dd6cce5a30cdfcb91028b4002a020cxayl.png"},
	{ID: 76, Key: "questionImageMuscleLines", Img: "https://fitnessimg.dailyworkout.cn/image/22ff73aadbe64be172bdb1e1290fe47fxayl.png"},
	{ID: 77, Key: "questionImageRobustBody", Img: "https://fitnessimg.dailyworkout.cn/image/1c0ca8ea4f6b1c5fb3551fb15840214bxayl.png"},
	{ID: 78, Key: "sedentaryWork", Img: "https://fitnessimg.dailyworkout.cn/image/a277763850037254345797e166327d27xayl.png"},
	{ID: 79, Key: "sleepTime", Img: "https://fitnessimg.dailyworkout.cn/image/e3391c407efe55ecc986ba797826697bxayl.png"},
	{ID: 80, Key: "transitionCalendar", Img: "https://fitnessimg.dailyworkout.cn/image/e907747c082d118bea2fbb4542a436cfxayl.png"},
	{ID: 81, Key: "weightCurrentNew", Img: "https://fitnessimg.dailyworkout.cn/image/fa7aa85c697bf9e04bddeded983769c9xayl.png"},
	{ID: 82, Key: "weightTargetNew", Img: "https://fitnessimg.dailyworkout.cn/image/baeaa9600ac449b2ba430c3450c3d413xayl.png"},
	{ID: 83, Key: "ageDescribe", Img: "https://fitnessimg.dailyworkout.cn/image/456784e18c130f7c7894d25a4f6e0ec8xayl.png"},
	{ID: 84, Key: "bodyCurrentSquare", Img: "https://fitnessimg.dailyworkout.cn/image/e780201565aacd526105805fb8928ee9xayl.png"},
	{ID: 85, Key: "bodyTargetSquare", Img: "https://fitnessimg.dailyworkout.cn/image/fbd1fae838f9261e813d34976c947d03xayl.png"},
	{ID: 86, Key: "climbStairs", Img: "https://fitnessimg.dailyworkout.cn/image/e980383927eb2aa315788ee2e5126f81xayl.png"},
	{ID: 87, Key: "definitelySuccessful", Img: "https://fitnessimg.dailyworkout.cn/image/fb740655a8c3ce23b2c0302eeb9ffec9xayl.png"},
	{ID: 88, Key: "healthNutrition", Img: "https://fitnessimg.dailyworkout.cn/image/b5da43fcd3e35af4253944a3db0120b3xayl.png"},
	{ID: 89, Key: "imageSportPlace", Img: "https://fitnessimg.dailyworkout.cn/image/614d2183daf05a47816714ae0c3890edxayl.png"},
	{ID: 90, Key: "loseFatResult", Img: "https://fitnessimg.dailyworkout.cn/image/d7800a23823bd9146554b85e74dbc98bxayl.png"},
	{ID: 91, Key: "muscleTarget", Img: "https://fitnessimg.dailyworkout.cn/image/2630078befa6354a1acc81f47670cc13xayl.png"},
	{ID: 92, Key: "muscleTargetImageDescribe", Img: "https://fitnessimg.dailyworkout.cn/image/d4e9e10f9eefc6fa1a292f5abd0f3f67xayl.png"},
	{ID: 93, Key: "purposeThreeNew", Img: "https://fitnessimg.dailyworkout.cn/image/6b2e51150c80fa5d18156e241694fc73xayl.png"},
	{ID: 94, Key: "sedentaryWorkCaption", Img: "https://fitnessimg.dailyworkout.cn/image/59b39b6721d8565e8e3e834ccf46b2dbxayl.png"},
	{ID: 95, Key: "sleepTimeCaption", Img: "https://fitnessimg.dailyworkout.cn/image/70f675b7f1b98729e8e089107f581ba1xayl.png"},
	{ID: 96, Key: "transitionAboutYouDescribe", Img: "https://fitnessimg.dailyworkout.cn/image/0c76186f5aefae060d495780361fbfabxayl.png"},
	{ID: 97, Key: "age136", Img: "https://fitnessimg.dailyworkout.cn/image/9f4fd01c3e2a1a879bf50c9d5598623cxayl.png"},
	{ID: 98, Key: "height136", Img: "https://fitnessimg.dailyworkout.cn/image/95c75d28b812dff3f952418193801b24xayl.png"},
	{ID: 99, Key: "weightCurrentNew136", Img: "https://fitnessimg.dailyworkout.cn/image/6051a064cbd0a56535303f20f9a8e079xayl.png"},
	{ID: 100, Key: "purposeThreeNewHot136", Img: "https://fitnessimg.dailyworkout.cn/image/003d25a50fededa6fed27501f3bcb060xayl.png"},
	{ID: 101, Key: "loseFatResult136", Img: "https://fitnessimg.dailyworkout.cn/image/e00f67122d1e6d0c7bace7d2990c6161xayl.png"},
	{ID: 102, Key: "bodyHurt136", Img: "https://fitnessimg.dailyworkout.cn/image/520f4105ca72b9658edccfb596246edexayl.png"},
	{ID: 103, Key: "imageSportPlace136", Img: "https://fitnessimg.dailyworkout.cn/image/c25ed831f417571274180746a63d03e4xayl.png"},
	{ID: 104, Key: "ability136", Img: "https://fitnessimg.dailyworkout.cn/image/018ed87ea07715ab660f773b10c51f82xayl.png"},
	{ID: 105, Key: "climbStairs136", Img: "https://fitnessimg.dailyworkout.cn/image/f5b90293ea945ae1030dc5ee2a9a85efxayl.png"},
	{ID: 106, Key: "weightAfterOrangeComparison", Img: "https://fitnessimg.dailyworkout.cn/image/3d8432a10acc41e37adb179ecb3f25ddxayl.png"},
	{ID: 107, Key: "achieveGoal", Img: "https://fitnessimg.dailyworkout.cn/image/2b9fff83e166f38efe7491290c3d1c89xayl.png"},
	{ID: 108, Key: "easyFatLoss", Img: " https://fitnessimg.dailyworkout.cn/image/f5c9d96dbe9e86c31d6d4ab365b5173axayl.png"},
	{ID: 109, Key: "bodyCurrentNew136", Img: "https://fitnessimg.dailyworkout.cn/image/26f24cb3453e99db51464368051e7451xayl.png"},
	{ID: 110, Key: "bodyTargetNew136", Img: "https://fitnessimg.dailyworkout.cn/image/25ddd24d11e5fcfbfd99d4a2ba4aeee2xayl.png"},
	{ID: 111, Key: "bodyHurtFeedback", Img: "https://fitnessimg.dailyworkout.cn/image/d69e8711237a1f3c396cabcedd10edfexayl.png"},
	{ID: 112, Key: "doubleFatBurning", Img: "https://fitnessimg.dailyworkout.cn/image/c9e336f7ae0c7e85cb9a92997b3e3a01xayl.png"},
	{ID: 113, Key: "questionImageBetterShape", Img: "https://fitnessimg.dailyworkout.cn/image/a8452790cfc37cfaadb7fde8f2173acaxayl.png"},
	{ID: 114, Key: "questionImageHealthyHabits", Img: "https://fitnessimg.dailyworkout.cn/image/989eb794b0888ec9d98ccb1ec035fef6xayl.png"},
	{ID: 115, Key: "practicePeriodNew", Img: "https://fitnessimg.dailyworkout.cn/image/04f9601e6819245e35f6e0012f9ebc90xayl.png"},
	{ID: 116, Key: "yoYoEffect", Img: "https://fitnessimg.dailyworkout.cn/image/dcb4614771f2f6d13852b59a037abb2bxayl.png"},
	{ID: 117, Key: "sedentaryWorkCaption136", Img: "https://fitnessimg.dailyworkout.cn/image/9290a1b86abaad746d03c822ee9c9a6fxayl.PNG"},
}

var TransitionVideos = []ImageItem{
	{ID: 1001, Key: "transitionVideo1", Img: "https://dancefitimage.dailyyoga.com.cn/47526e7b27b2207555279bb476e05a2e.png"},
	{ID: 1002, Key: "transitionVideo2", Img: "https://dancefitimage.dailyyoga.com.cn/8d8c85ee45deff6e8d52d014cbd16fb4.png"},
	{ID: 1003, Key: "transitionVideo3", Img: "https://dancefitimage.dailyyoga.com.cn/37fe8dd737dded36b63be0b49cab307c.png"},
}

var TransitionPageIDs = []int{36, 37}

// 1:顶部Banner 2:我的评估结果 3:图片 4:图片轮播 5:目标体重曲线图 6:练习建议， 7:练习强度&分布， 8:练习部位，9:吸底按钮 10:凯格尔,11凯格尔练习建议,
// 12为你定制专属【目标】方案，13为你定制专属【目标】方案，14凯格尔练习建议
const (
	PlanPageTopBanner = iota + 1
	PlanPagePrediction
	PlanPageImage
	PlanPageBannerSwiper
	PlanPageChartLine
	PlanPagePracticeIdea
	PlanPagePracticeStrength
	PlanPagePracticePart
	PlanPageSwiper
	PlanPagekegel
	PlanPageKegelPracticeSug
	PlanPageGymTrainingSchedule
	PlanPageTargetPlan
	PlanPageMuscle
)

const (
	ObTypeStartApp = iota + 1
	ObTypePlan
	ObTypekegel
)

const BodyInfo = 38

const PageDef = 20

const Lay3Tiles = 3

const MaxSort = 10000

var ObkegelIDs = []string{"emotionalLife", "kegelExam",
	"smoke", "questionImageAttractionKegel"}

const (
	Purpose      = "purpose"
	PurposeAll   = "purposeAll"
	PurposeKegel = "purposeKegel"
	BodyCurrent  = "bodyCurrent"
	BodyTarget   = "bodyTarget"
)
const PurposeImg = "https://fitnessimg.dailyworkout.cn/image/3d9afd3b26b13780ae1f7b81262ecec8.png"
