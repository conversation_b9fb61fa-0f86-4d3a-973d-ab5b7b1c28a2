package config

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases/conf"
	dbp "gitlab.dailyyoga.com.cn/server/children-admin-api/databases/product"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/client"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/product"
)

type maconfig struct{}

var MAConfig maconfig

type MAConfigRsp struct {
	MerchantIDList  map[int64]string `json:"merchant_id_list"`
	ProductTypeList map[int64]string `json:"product_type_list"`
}

func (*maconfig) Config(t *http.Context) {
	t.Result(&MAConfigRsp{
		MerchantIDList: client.AlipayMchIDMap,
		ProductTypeList: map[int64]string{
			0: "默认",
			int64(product.FTProductTypeEnum.MemberProduct): "会员产品",
		},
	})
}

const DefaultConfigID = 1

type MAConfigItem struct {
	ID          int64  `json:"id"`
	ProductType int64  `json:"product_select_type"`
	ProductID   int64  `json:"product_id"`
	ProductName string `json:"product_name"`
	MerchantID  string `json:"merchant_id"`
}

func (*maconfig) List(t *http.Context) {
	page := t.GetRequestIntD("page", 1)
	pageSize := t.GetRequestIntD("page_size", 20) //nolint
	list := conf.TbMchAccountConf.GetList(page, pageSize)
	res := make([]*MAConfigItem, 0)
	productMap := dbp.TbWebProduct.GetAllProductMap()
	for _, v := range list {
		res = append(res, formatMchAccountConfItem(v, productMap))
	}
	t.Result(map[string]interface{}{
		"list": res,
	})
}

func (*maconfig) Create(t *http.Context) {
	req := &MAConfigItem{}
	t.ParseRequestStruct(req)
	item := &conf.MchAccountConfig{
		ProductType: int32(req.ProductType),
		ProductID:   req.ProductID,
		MchID:       req.MerchantID,
		IsDel:       library.No,
	}
	if req.ProductType == 0 {
		t.Result(errorcode.InvalidParams, "不允许创建多个默认配置")
		return
	}
	if req.ProductID == 0 {
		t.Result(errorcode.InvalidParams, "产品ID不能为空")
		return
	}
	err := item.Save()
	if err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

func (*maconfig) Update(t *http.Context) {
	req := &MAConfigItem{}
	t.ParseRequestStruct(req)
	item := conf.TbMchAccountConf.GetItem(req.ID)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	item.ProductType = int32(req.ProductType)
	item.ProductID = req.ProductID
	if req.ID == DefaultConfigID {
		item.ProductType = 0
		item.ProductID = 0
	}
	item.MchID = req.MerchantID
	if err := item.Update(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

func (*maconfig) Delete(t *http.Context) {
	req := &MAConfigItem{}
	t.ParseRequestStruct(req)
	item := conf.TbMchAccountConf.GetItem(req.ID)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	if req.ID == DefaultConfigID {
		t.Result(errorcode.InvalidParams, "默认ID不允许删除")
		return
	}
	item.IsDel = library.Yes
	if err := item.Update(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

func (*maconfig) Check(t *http.Context) {
	req := &MAConfigItem{}
	t.ParseRequestStruct(req)
	item := conf.TbMchAccountConf.GetValid(req.ID, int32(req.ProductType), req.ProductID)
	if item != nil {
		t.Result(errorcode.InvalidParams, "已存在相同配置")
		return
	}
	t.Result(library.EmptyResponse{})
}

func (*maconfig) Info(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	item := conf.TbMchAccountConf.GetItem(id)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	productMap := dbp.TbWebProduct.GetAllProductMap()
	t.Result(formatMchAccountConfItem(item, productMap))
}

func formatMchAccountConfItem(item *conf.MchAccountConfig, productMap map[int64]*dbp.WebProduct) *MAConfigItem {
	res := &MAConfigItem{
		ID:          item.ID,
		ProductType: int64(item.ProductType),
		ProductID:   item.ProductID,
		MerchantID:  item.MchID,
	}
	pItem := productMap[item.ProductID]
	if pItem != nil {
		res.ProductName = pItem.Name
	}
	return res
}
