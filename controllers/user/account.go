package user

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases/client"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases/order"
	dbproduct "gitlab.dailyyoga.com.cn/server/children-admin-api/databases/product"
	dbuser "gitlab.dailyyoga.com.cn/server/children-admin-api/databases/user"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	libc "gitlab.dailyyoga.com.cn/server/children-admin-api/library/client"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/pay"
	lbuser "gitlab.dailyyoga.com.cn/server/children-admin-api/library/user"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/service/cache"
)

const DefaultUtmSource = "【FT_硬汗健身】"

type SubscribeListItem struct {
	UID             int64  `json:"uid"`
	SubscribeID     string `json:"subscribe_id"`
	OrderID         string `json:"order_id"`
	ProductID       int64  `json:"product_id"`
	SubscribeTime   int64  `json:"subscribe_time"`
	SubscribeStatus int32  `json:"subscribe_status"`
}

type SubscribeListRsp struct {
	List []*SubscribeListItem `json:"list"`
}

// SubscribeList 用户的订阅记录
func SubscribeList(t *http.Context) {
	uid := t.GetRequestInt64D("uid", 0)
	if uid == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	res := &SubscribeListRsp{
		List: make([]*SubscribeListItem, 0),
	}
	list := dbuser.TbWPSubU.GetUserSubscribeList(uid)
	if len(list) == 0 {
		t.Result(res)
		return
	}
	for _, v := range list {
		item := &SubscribeListItem{
			UID:             v.UID,
			SubscribeID:     v.OriginalTransactionID,
			SubscribeTime:   v.CreateTime,
			OrderID:         v.OriginalOrderID,
			ProductID:       int64(v.ProductID),
			SubscribeStatus: int32(v.Status),
		}
		res.List = append(res.List, item)
	}
	t.Result(res)
}

const (
	UserInfoByUIDType     = 1
	UserInfoByMobileType  = 2
	UserInfoByOrderIDType = 3
)

type InfoAdmin struct {
	UID             int64        `json:"uid"`
	Mobile          string       `json:"mobile"`
	Avatar          string       `json:"avatar"`
	Nickname        string       `json:"nickname"`
	EndTime         int64        `json:"end_time"`
	VoiceEndTime    int64        `json:"voice_end_time"`
	KegelEndTime    int64        `json:"kegel_card_end_time"`
	PlanCardEndTime int64        `json:"plan_card_end_time"`
	SubscribeInfo   []*Subscribe `json:"subscribe_info"`
}
type Subscribe struct {
	PayType int64  `json:"pay_type"`
	Desc    string `json:"desc"`
}

type InfoAdminRsp struct {
	List []*InfoAdmin `json:"list"`
}

// LogoffUserInfo 获取注销用户信息
func LogoffUserInfo(t *http.Context) {
	nType := t.GetRequestInt32D("type", UserInfoByUIDType)
	name := t.GetRequestStringD("name", "")
	var acc *dbuser.Account
	accList := make([]*dbuser.Account, 0)
	if nType == UserInfoByUIDType {
		uid, err := strconv.ParseInt(name, 10, 64)
		if err != nil {
			t.Result(errorcode.InvalidParams)
			return
		}
		acc = dbuser.TbAccount.GetLogoffUserByID(uid)
		if acc != nil {
			accList = append(accList, acc)
		}
	} else if nType == UserInfoByOrderIDType {
		orderInfo := order.TbWebOrder.GetItemByOrderID(name)
		if orderInfo == nil || orderInfo.OrderStatus != pay.OrderStatus.Paid {
			t.Result(library.EmptyResponse{})
			return
		}
		acc = dbuser.TbAccount.GetLogoffUserByID(orderInfo.UID)
		if acc != nil {
			accList = append(accList, acc)
		}
	} else {
		acc := dbuser.TbAccount.GetLogoffUserByMobile(name)
		if len(acc) > 0 {
			accList = append(accList, acc...)
		}
	}
	if len(accList) == 0 {
		t.Result(library.EmptyResponse{})
		return
	}
	t.Result(formatUserInfoList(accList))
}

func formatUserInfoList(accountList []*dbuser.Account) *InfoAdminRsp {
	resp := &InfoAdminRsp{
		List: make([]*InfoAdmin, 0),
	}
	for k := range accountList {
		resp.List = append(resp.List, formatUserInfo(accountList[k]))
	}
	return resp
}

// AccountInfo 用户信息接口
func AccountInfo(t *http.Context) {
	name := t.GetRequestStringD("name", "")
	nType := t.GetRequestInt32D("type", UserInfoByUIDType)
	var acc *dbuser.Account
	if nType == UserInfoByUIDType {
		uid, err := strconv.ParseInt(name, 10, 64)
		if err != nil {
			t.Result(errorcode.InvalidParams)
			return
		}
		acc = dbuser.TbAccount.GetUserByID(uid)
	} else if nType == UserInfoByOrderIDType {
		orderInfo := order.TbWebOrder.GetItemByOrderID(name)
		if orderInfo == nil || orderInfo.OrderStatus != pay.OrderStatus.Paid {
			t.Result(library.EmptyResponse{})
			return
		}
		acc = dbuser.TbAccount.GetUserByID(orderInfo.UID)
	} else {
		acc = dbuser.TbAccount.GetUserByMobile(name)
	}
	// 用订单号查询
	if acc == nil {
		t.Result(library.EmptyResponse{})
		return
	}
	t.Result(formatUserInfoList([]*dbuser.Account{acc}))
}

// formatUserInfo 格式化后台用户信息展示
func formatUserInfo(acc *dbuser.Account) *InfoAdmin {
	res := &InfoAdmin{
		UID:           acc.ID,
		Mobile:        acc.Mobile,
		Avatar:        acc.Avatar,
		Nickname:      acc.Nickname,
		EndTime:       acc.EndTime,
		SubscribeInfo: make([]*Subscribe, 0),
	}
	subList := dbuser.TbWPSubU.GetUserSubscribeList(acc.ID)
	if len(subList) == 0 {
		return res
	}
	for _, v := range subList {
		item := &Subscribe{
			PayType: int64(v.PayType),
		}
		item.Desc = fmt.Sprintf("%s自动订阅%s", pay.PayTypeDesc[v.PayType],
			pay.SubscribeStatusDesc[v.Status])
		res.SubscribeInfo = append(res.SubscribeInfo, item)
	}
	return res
}

type OrderListRsp struct {
	OrderList []*OrderItem `json:"order_list"`
}

type OrderItem struct {
	OrderID     string `json:"order_id"`
	CreateTime  int64  `json:"create_time"`
	PayTime     int64  `json:"pay_time"`
	ProductName string `json:"product_name"`
	PayType     int    `json:"pay_type"`
	Amount      string `json:"amount"`
	IsSubscribe int    `json:"is_subscribe"`
}

// OrderList 用户订单列表
func OrderList(t *http.Context) {
	uid := t.GetRequestInt64D("uid", 0)
	if uid == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	orderList := order.TbWebOrder.GetListByUID(uid)
	if len(orderList) == 0 {
		t.Result(library.EmptyResponse{})
		return
	}
	pMap := dbproduct.TbWebProduct.GetAllProductMap()
	if pMap == nil {
		t.Result(errorcode.DBError)
		return
	}
	res := OrderListRsp{
		OrderList: make([]*OrderItem, 0),
	}
	for _, v := range orderList {
		item := &OrderItem{
			OrderID:    v.OrderID,
			Amount:     strconv.FormatFloat(v.OrderAmount, 'f', 2, 64),
			CreateTime: v.CreateTime,
			PayTime:    v.PayTime,
			PayType:    v.PayType,
		}

		if p, ok := pMap[v.ProductID]; ok {
			item.IsSubscribe = p.IsSubscribe
			item.ProductName = p.Name
		}
		res.OrderList = append(res.OrderList, item)
	}
	t.Result(res)
}

// nolint
func Authority(t *http.Context) {
	type ItemInfo struct {
		Key      string     `json:"key"`
		Title    string     `json:"title"`
		Children []ItemInfo `json:"children,omitempty"`
	}
	var list = map[string][]ItemInfo{
		"list": {
			{
				Key:   "/home",
				Title: "首页",
			},
			{
				Key:   "/userInformation",
				Title: "用户信息",
				Children: []ItemInfo{
					{
						Key:   "/userInformation/userInfo",
						Title: "用户信息查询",
					},
					{
						Key:   "/userInformation/orderList",
						Title: "支付订单查询",
					},
				},
			},
			{
				Key:   "/operationSupport",
				Title: "运营支持",
				Children: []ItemInfo{
					{
						Key:   "/resourceManage/homeBanner",
						Title: "首页资源位",
					},
					{
						Key:   "/operationSupport/obSkipConfig",
						Title: "OB跳过开关",
					},
					{
						Key:   "/operationSupport/popupList",
						Title: "全屏弹窗",
					},
					{
						Key:   "/operationSupport/userGroup",
						Title: "用户分群配置",
					},
					{
						Key:   "/operationSupport/memberTimeChange",
						Title: "会员时间变动",
					},
					{
						Key:   "/operationSupport/obResourcePool",
						Title: "OB流程资源池",
					},
					{
						Key:   "/operationSupport/completeTraining",
						Title: "完训弹窗配置",
					},
					{
						Key:   "/operationSupport/forcedPopupList",
						Title: "强付费弹窗配置",
					},
					{
						Key:   "/operationSupport/abTestList",
						Title: "AB实验配置",
					},
					{
						Key:   "/operationSupport/appStoreFeedback",
						Title: "应用商店好评",
					},
					{
						Key:   "/operationSupport/questionList",
						Title: "客服中心",
					},
					{
						Key:   "/operationSupport/channelManage",
						Title: "更改渠道",
					},
				},
			},
			{
				Key:   "/paymentManage",
				Title: "支付相关",
				Children: []ItemInfo{
					{
						Key:   "/paymentManage/productList",
						Title: "产品信息",
					},
					{
						Key:   "/paymentManage/obPaymentList",
						Title: "付费方案页配置",
					},
					{
						Key:   "/paymentManage/merchantAccountList",
						Title: "商户号切换",
					},
				},
			},
			{
				Key:   "/auditConfig",
				Title: "审核&合规",
				Children: []ItemInfo{
					{
						Key:   "/auditConfig/iosAuditSwitch",
						Title: "iOS审核开关",
					},
					{
						Key:   "/auditConfig/androidAuditSwitch",
						Title: "安卓审核开关",
					},
					{
						Key:   "/auditConfig/compliance",
						Title: "合规",
					},
				},
			},
			{
				Key:   "/resourceManage",
				Title: "内容资源管理",
				Children: []ItemInfo{
					{
						Key:   "/resourceManage/actionList",
						Title: "动作库",
					},
					{
						Key:   "/resourceManage/courseList",
						Title: "课程列表",
					},
					{
						Key:   "/resourceManage/voicePackageList",
						Title: "语音包管理",
					},
				},
			},
			{
				Key:   "/homeContentConfig",
				Title: "首页内容配置",
				Children: []ItemInfo{
					{
						Key:   "/homeContentConfig/containerList",
						Title: "容器列表",
					},
					{
						Key:   "/homeContentConfig/resourceBitList",
						Title: "首页分群内容配置",
					},
					{
						Key:   "/homeContentConfig/articleList",
						Title: "干货专区",
					},
					{
						Key:   "/homeContentConfig/courseRecommendList",
						Title: "首页课程推荐",
					},
				},
			},
		},
	}
	t.Result(list)
}

const (
	IdentifyUID      = 1
	IdentifyDeviceID = 2
)

type ChannelRes struct {
	Channel      map[int]string `json:"channel"`
	IdentifyType map[int]string `json:"identify_type"`
}

func Conf(t *http.Context) {
	t.Result(&ChannelRes{
		Channel:      libc.ChannelMap,
		IdentifyType: libc.RangeUserClient,
	})
}

func UpdateADChannel(t *http.Context) {
	channel := t.GetRequestIntD("channel", 0)
	Identify := t.GetRequestStringD("identify", "")
	IdentifyType := t.GetRequestIntD("identify_type", 0)
	if Identify == "" || IdentifyType == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	channelStr, ok := libc.ChannelADIdenMap[channel]
	if !ok {
		t.Result(errorcode.InvalidParams)
		return
	}
	if IdentifyType == IdentifyUID {
		uid, err := strconv.ParseInt(Identify, 10, 64)
		if err != nil {
			t.Result(errorcode.InvalidParams)
			return
		}
		utmItem := client.TbAdUIDChannel.GetItemByUID(uid)
		if utmItem != nil {
			utmItem.UtmSource = channelStr
			if err := utmItem.Update(); err != nil {
				t.Result(errorcode.DBError, err.Error())
				return
			}
		}
		t.Result("更新成功")
		return
	}
	sensorsItem := client.TbAdSensorsData.GetItemByDeviceID(Identify)
	if sensorsItem != nil {
		sensorsItem.UtmSource = channelStr
		if err := sensorsItem.Update(); err != nil {
			t.Result(errorcode.DBError, err.Error())
			return
		}
	}
	t.Result("更新成功")
}

type ChannelData struct {
	UtmSource string `json:"utm_source"`
}

// getChannel 获取渠道
func GetChannel(t *http.Context) {
	Identify := t.GetRequestStringD("identify", "")
	IdentifyType := t.GetRequestIntD("identify_type", 0)
	if Identify == "" || IdentifyType == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	var utmSource string
	if IdentifyType == IdentifyUID {
		uid, err := strconv.ParseInt(Identify, 10, 64)
		if err != nil {
			t.Result(errorcode.InvalidParams, "识别不到有效uid")
			return
		}
		utmItem := client.TbAdUIDChannel.GetItemByUID(uid)
		if utmItem != nil {
			utmSource = utmItem.UtmSource
			if utmItem.UtmSource == "" {
				utmSource = DefaultUtmSource
			}
		}
	} else {
		sensorsItem := client.TbAdSensorsData.GetItemByDeviceID(Identify)
		if sensorsItem != nil {
			utmSource = sensorsItem.UtmSource
			if sensorsItem.UtmSource == "" {
				utmSource = DefaultUtmSource
			}
		}
	}
	channelStr, ok := libc.ChannelMapping[utmSource]
	if !ok {
		channelStr = "【自然量】"
	}
	var data ChannelData
	data.UtmSource = channelStr
	t.Result(data)
}

type LogoffMobileReq struct {
	Mobile string `json:"mobile"`
	UID    int64  `json:"id"`
}

// LogoffMobile 解绑手机号
func LogoffMobile(t *http.Context) {
	params := &LogoffMobileReq{}
	if err := json.Unmarshal(library.GetBody(t), params); err != nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	if params.Mobile == "" {
		t.Result(errorcode.InvalidParams)
		return
	}
	account := dbuser.TbAccount.GetUserByMobile(params.Mobile)
	if account == nil {
		t.Result(errorcode.DBError)
		return
	}
	account.Mobile = ""
	if err := account.Update(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	safelygo.GoSafelyByTraceID(func() {
		UIDBindSid(account.ID)
	})
	t.Result(library.EmptyResponse{})
}

func LogoffMobileUpdate(t *http.Context) {
	params := &LogoffMobileReq{}
	if err := json.Unmarshal(library.GetBody(t), params); err != nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	if params.Mobile == "" || params.UID == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	account := dbuser.TbAccount.GetUserByMobile(params.Mobile)
	if account != nil {
		t.Result(errorcode.DBError, "当前手机号已绑定")
		return
	}
	account = dbuser.TbAccount.GetUserByID(params.UID)
	if account == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	if account.UpMobileNum >= lbuser.UpMobileNum {
		t.Result(errorcode.DBError, "手机号最多修改3次")
		return
	}
	if account.LoginType == lbuser.LoginTypeShenCe {
		account.LoginType = lbuser.LoginTypeMobileVercode
		third := dbuser.TbAccountThirdAuth.GetBindThirdInfoByUID(account.ID, lbuser.LoginTypeShenCe)
		if third != nil {
			third.IsBind = library.No
			if err := third.Update(); err != nil {
				t.Result(errorcode.DBError)
				return
			}
		}
	}
	account.Mobile = params.Mobile
	account.UpMobileNum++
	if err := account.Update(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	safelygo.GoSafelyByTraceID(func() {
		UIDBindSid(params.UID)
	})
	t.Result(library.EmptyResponse{})
}

func UIDBindSid(uid int64) {
	rdc := cache.GetCRedis().GetClient()
	dataKey := fmt.Sprintf("%s:%d", lbuser.UserOnlineCacheKeyPrefix, uid)
	ctx := context.Background()
	resultArr, err := rdc.LRange(ctx, dataKey, 0, lbuser.UIDBindSidLen).Result()
	if err != nil {
		logger.Warn(err)
		return
	}
	for _, v := range resultArr {
		cacheKey := fmt.Sprintf("%s%s", lbuser.LoginSIDCacheKeyPrefix, v)
		if _, err := rdc.Del(context.Background(), cacheKey).Result(); err == nil {
			logger.Warn(err)
		}
	}
}
