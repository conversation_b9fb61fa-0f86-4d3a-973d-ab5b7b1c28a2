package user

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strings"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"

	"gitlab.dailyyoga.com.cn/protogen/children-go/children"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases/client"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases/user"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/grpc"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	libc "gitlab.dailyyoga.com.cn/server/children-admin-api/library/client"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/product"
	libuser "gitlab.dailyyoga.com.cn/server/children-admin-api/library/user"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/service/sensor"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/service/util"
)

func StorePraiseList(t *http.Context) {
	uid := t.GetRequestInt64D("uid", 0)
	startTime := t.GetRequestInt64D("start_time", 0)
	endTime := t.GetRequestInt64D("end_time", 0)
	status := t.GetRequestIntD("status", 0)
	praiseLatForm := t.GetRequestIntD("praise_latform", 0)
	page := t.GetRequestIntD("page", 1)
	pageSize := t.GetRequestIntD("page_size", 20)        //nolint
	entranceType := t.GetRequestIntD("entrance_type", 0) //nolint
	list := user.TbStorePraise.GetList(uid, startTime, endTime, status, page, pageSize, praiseLatForm, entranceType)
	if len(list) == 0 {
		t.Result(make([]*user.StorePraise, 0))
		return
	}
	t.Result(map[string]interface{}{
		"list":  list,
		"total": user.TbStorePraise.GetCount(uid, startTime, endTime, status),
	})
}

type exportCsvResp struct {
	FileData string `json:"file_data"`
	FileName string `json:"file_name"`
}

func StorePraiseExport(t *http.Context) {
	uid := t.GetRequestInt64D("uid", 0)
	startTime := t.GetRequestInt64D("start_time", 0)
	endTime := t.GetRequestInt64D("end_time", 0)
	status := t.GetRequestIntD("status", 0)
	praiseLatForm := t.GetRequestIntD("praise_latform", 0)
	entranceType := t.GetRequestIntD("entrance_type", 0) //nolint
	list := user.TbStorePraise.GetList(uid, startTime, endTime, status, 0, 0, praiseLatForm, entranceType)
	resp := &exportCsvResp{
		FileData: strings.Join(StorePraiseExportResp(list), "\n"),
		FileName: fmt.Sprintf("%s_%s_%d",
			"好评导出", time.Now().Format("20060102"), time.Now().Unix()),
	}
	t.Result(resp)
}

func StorePraiseExportResp(list []*user.StorePraise) []string {
	rsp := make([]string, 0)
	header := []string{"uid", "上传时间", "好评类型", "好评入口", "奖励内容", "状态", "截图"}
	rsp = append(rsp, strings.Join(header, ","))
	for v := range list {
		item := list[v]
		entranceTypeDesc := libuser.EntranceTypeDesc[int(item.EntranceType)]
		if entranceTypeDesc == "" {
			entranceTypeDesc = "未知"
		}
		awardTypeDesc := "VIP"
		if item.AwardType > 0 {
			awardTypeDesc = fmt.Sprintf("%d天%s", item.AwardDuration, product.AwardTypeDesc[int(item.AwardType)])
			if item.AwardType == libuser.IdeaRecipes {
				awardTypeDesc = product.AwardTypeDesc[int(item.AwardType)]
			}
		}
		statusDesc := "已拒绝"
		if item.AuditStatus == library.Yes {
			statusDesc = "待审核"
		}
		if item.AuditStatus == library.No {
			statusDesc = "已通过"
		}
		temp := []string{
			fmt.Sprintf("%d", item.UID),
			time.Unix(item.CreateTime, 0).Format("2006-01-02 15:04:05"),
			libuser.PraiseLatFormDesc[int(item.PraiseLatform)],
			entranceTypeDesc,
			awardTypeDesc,
			statusDesc,
			item.Img,
		}
		rsp = append(rsp, strings.Join(temp, ","))
	}
	return rsp
}

func StorePraiseAudit(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	status := t.GetRequestInt64D("status", 0)

	item := user.TbStorePraise.GetItemByID(id)
	if item == nil {
		t.Result(errorcode.InvalidParams, "错误的ID")
		return
	}
	if item.AuditStatus != int32(libuser.StorePraiseAuditEnum.Wait) {
		t.Result(errorcode.InvalidParams, "非待审核状态")
		return
	}
	item.AuditStatus = int32(status)
	if err := item.Update(); err != nil {
		t.Result(errorcode.InvalidParams, "更新失败")
		return
	}
	if status == int64(libuser.StorePraiseAuditEnum.Pass) {
		adminName := t.GetHeader(library.HeaderOperatorName)
		adminName, _ = url.QueryUnescape(adminName)
		durationValue := int32(0)
		vipType := 0
		if item.PraiseResourceID == 0 {
			durationValue = libuser.Vip3Day
			vipType = product.ProductVipTypeVIP
		} else {
			if item.AwardType == libuser.IdeaRecipes {
				SensorEvaluateResults(true, item)
				t.Result(library.EmptyResponse{})
				return
			}
			durationValue = int32(item.AwardDuration)
			vipType = int(item.AwardType)
		}
		if vipType == 0 || durationValue == 0 {
			t.Result(errorcode.SystemError, "发放会员失败")
			return
		}
		fClient := grpc.GetchildrenClient()
		rsp, err := fClient.ChangeVipEquity(context.Background(), &children.ChangeVipEquityReq{
			UIDStrList:       fmt.Sprintf("%d", item.UID),
			OperateType:      int32(libuser.VipOperateTypeEnum.Add),
			DurationType:     int32(product.DurationTypeEnum.Day),
			DurationValue:    durationValue,
			ChangeReasonType: int32(libuser.VipChangeReasonEnum.StorePraise),
			Remark:           "好评活动赠送",
			AdminName:        adminName,
			VipType:          int32(vipType),
		})
		if err != nil || rsp.ErrorCode > 0 {
			t.Result(errorcode.SystemError, "发放会员失败")
			return
		}
		accountInfo := user.TbAccount.GetUserByID(item.UID)
		if accountInfo != nil && accountInfo.Mobile != "" {
			if err := util.SrvVercode.PraiseMsg(accountInfo.Mobile); err != nil {
				logger.Error(err)
			}
		}
		SensorEvaluateResults(true, item)
	} else {
		SensorEvaluateResults(false, item)
	}
	t.Result(library.EmptyResponse{})
}

func SensorEvaluateResults(isPass bool, item *user.StorePraise) {
	evaluateResults := sensor.EvaluateResults{
		IsPass:  isPass,
		Channel: libuser.PraiseLatFormDesc[int(item.PraiseLatform)],
	}
	if evaluateResults.Channel == "" {
		evaluateResults.Channel = libuser.PraiseLatFormDesc[libuser.PraiseLatFormAppStore]
	}
	itemResource := user.TbPraiseResource.GetItemByID(int64(item.PraiseResourceID))
	durationValue := int(itemResource.AwardDuration)
	vipType := int(itemResource.AwardType)

	evaluateResults.GiftName = fmt.Sprintf("%d天%s", durationValue, product.ProductVipTypeDesc[vipType])
	evaluateResults.Track(fmt.Sprintf("%d", item.UID))
}

type StorePraiseConfig struct {
	IsPopupPositive bool `json:"is_popup_positive"`
}

// PraiseConfig 好评配置
func PraiseConfig(t *http.Context) {
	bc := client.TbConfig.GetItemByKey(libc.ConfigKeyStoreParise)
	if bc == nil {
		t.Result(errorcode.DBError)
		return
	}
	sp := &StorePraiseConfig{}
	err := json.Unmarshal([]byte(bc.Value), sp)
	if err != nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	t.Result(sp)
}

// PraiseUpdate 好评配置
func PraiseUpdate(t *http.Context) {
	req := &StorePraiseConfig{}
	t.ParseRequestStruct(req)
	bc := client.TbConfig.GetItemByKey(libc.ConfigKeyStoreParise)
	if bc == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	sp := &StorePraiseConfig{}
	err := json.Unmarshal([]byte(bc.Value), sp)
	if err != nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	sp.IsPopupPositive = req.IsPopupPositive
	spByte, err := json.Marshal(sp)
	if err != nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	bc.Value = string(spByte)
	if err := bc.Update(); err != nil {
		t.Result(errorcode.DBError)
		return
	}
	t.Result("更新成功")
}

type ConfigResp struct {
	PraiseLatForm map[int]string    `json:"praise_latform"`
	IsLottery     map[int]string    `json:"is_lottery"`
	AwardType     map[int]string    `json:"award_type"`
	EntranceType  map[int]string    `json:"entrance_type"`
	Channel       map[string]string `json:"channel"`
}

// ResourceConfig 好评资源池配置信息
func ResourceConfig(t *http.Context) {
	t.Result(ConfigResp{
		PraiseLatForm: libuser.PraiseLatFormDesc,
		IsLottery:     libuser.LotteryDesc,
		AwardType:     product.AwardTypeDesc,
		Channel:       library.NameToChannelMap,
		EntranceType:  libuser.EntranceTypeDesc,
	})
}

type ResourceData struct {
	ID            int64    `json:"id"`
	Title         string   `json:"title"`
	PraiseLatform int32    `json:"praise_latform"`
	IsLottery     int64    `json:"is_lottery"`
	AwardType     int64    `json:"award_type"`
	AwardDuration int64    `json:"award_duration"`
	Img           string   `json:"img"`
	ChannelArr    []string `json:"channel_arr"`
	UserGroupID   int64    `json:"user_group_id"`
}

func ResourceSave(t *http.Context) {
	req := &ResourceData{}
	t.ParseRequestStruct(req)
	var clientChannelStr string
	if len(req.ChannelArr) > 0 {
		clientChannel, err := json.Marshal(req.ChannelArr)
		clientChannelStr = string(clientChannel)
		if err != nil {
			t.Result(errorcode.InvalidParams)
			return
		}
	}
	praiseResource := user.PraiseResource{
		ID:            req.ID,
		Title:         req.Title,
		PraiseLatform: req.PraiseLatform,
		IsLottery:     req.IsLottery,
		AwardType:     req.AwardType,
		AwardDuration: req.AwardDuration,
		Img:           util.FormatImageInfoStr(req.Img),
		IsDel:         library.No,
		ClientChannel: clientChannelStr,
		UserGroupID:   req.UserGroupID,
	}
	if praiseResource.ID == 0 {
		if err := praiseResource.Save(); err != nil {
			t.Result(errorcode.SystemError, err.Error())
			return
		}
	} else {
		if err := praiseResource.Update(); err != nil {
			t.Result(errorcode.SystemError, err.Error())
			return
		}
	}
	t.Result(errorcode.Success)
}

func ResourceInfo(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	item := user.TbPraiseResource.GetItemByID(id)
	t.Result(formatResourceInfo(item))
}
func formatResourceInfo(item *user.PraiseResource) *ResourceData {
	if item == nil {
		return nil
	}
	resp := &ResourceData{
		ID:            item.ID,
		Title:         item.Title,
		PraiseLatform: item.PraiseLatform,
		IsLottery:     item.IsLottery,
		AwardType:     item.AwardType,
		AwardDuration: item.AwardDuration,
		Img:           util.UnmarshalImageStr(item.Img).URL,
		UserGroupID:   item.UserGroupID,
	}
	clientChannel := make([]string, 0)
	_ = json.Unmarshal([]byte(item.ClientChannel), &clientChannel)
	resp.ChannelArr = clientChannel
	return resp
}

func ResourceList(t *http.Context) {
	page := t.GetRequestIntD("page", 1)
	pageSize := t.GetRequestIntD("page_size", 20) //nolint
	resp := make([]*ResourceData, 0)
	for _, v := range user.TbPraiseResource.GetList(page, pageSize) {
		resp = append(resp, formatResourceInfo(v))
	}
	t.Result(resp)
}

func ResourceDel(t *http.Context) {
	req := &ResourceData{}
	t.ParseRequestStruct(req)
	item := user.TbPraiseResource.GetItemByID(req.ID)
	if item == nil {
		t.Result(errorcode.SystemError)
		return
	}
	item.IsDel = library.Yes
	if err := item.Update(); err != nil {
		t.Result(errorcode.SystemError, err.Error())
		return
	}
	t.Result(errorcode.Success)
}
