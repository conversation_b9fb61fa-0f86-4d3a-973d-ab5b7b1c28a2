package user

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"sort"
	"strconv"
	"sync"
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/protogen/children-user-group-go/childrenusergroup"
	db "gitlab.dailyyoga.com.cn/server/children-admin-api/databases/group"
	dbUser "gitlab.dailyyoga.com.cn/server/children-admin-api/databases/user"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/grpc"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	libCache "gitlab.dailyyoga.com.cn/server/children-admin-api/library/cache"
	clib "gitlab.dailyyoga.com.cn/server/children-admin-api/library/client"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
	lib "gitlab.dailyyoga.com.cn/server/children-admin-api/library/group"
	libp "gitlab.dailyyoga.com.cn/server/children-admin-api/library/product"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/service/cache"
)

type group struct {
}

var GroupAdmin group

func (*group) NewInfo(t *http.Context) {
	name := t.GetRequestStringD("group_name", "")
	rule := t.GetRequestStringD("group_rule", "")
	if name == "" || rule == "" {
		t.Result(errorcode.InvalidParams)
		return
	}
	node1 := make(map[string]interface{})
	err := json.Unmarshal([]byte(rule), &node1)
	if err != nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	GroupRuleLabel, err := formatGroupRuleLabel(node1)
	if err != nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	rpc := grpc.GetchildrenGroupClient()
	rsp, err := rpc.Create(context.Background(), &childrenusergroup.CreateRequest{
		GroupName:      name,
		GroupRule:      rule,
		GroupRuleLabel: GroupRuleLabel,
	})
	if err != nil {
		t.Result(errorcode.SystemError, err.Error())
		return
	}
	if rsp.ErrorCode > 0 {
		t.Result(errorcode.SystemError, rsp.GetErrorMsg())
		return
	}
	t.Result(library.EmptyResponse{})
}

func (*group) UpdateInfo(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	name := t.GetRequestStringD("group_name", "")
	rule := t.GetRequestStringD("group_rule", "")
	if name == "" || rule == "" || id == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	node1 := make(map[string]interface{})
	err := json.Unmarshal([]byte(rule), &node1)
	if err != nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	GroupRuleLabel, err := formatGroupRuleLabel(node1)
	if err != nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	rpc := grpc.GetchildrenGroupClient()
	rsp, err := rpc.Save(context.Background(), &childrenusergroup.SaveRequest{
		ID:             id,
		GroupName:      name,
		GroupRule:      rule,
		GroupRuleLabel: GroupRuleLabel,
	})
	if err != nil {
		t.Result(errorcode.SystemError, err.Error())
		return
	}
	if rsp.ErrorCode > 0 {
		t.Result(errorcode.SystemError, rsp.GetErrorMsg())
		return
	}
	t.Result(library.EmptyResponse{})
}

type infoRsp struct {
	ID             int64    `json:"id,omitempty"`
	CreateTime     int64    `json:"create_time,omitempty"`
	UpdateTime     int64    `json:"update_time,omitempty"`
	DeleteStatus   int32    `json:"delete_status,omitempty"`
	GroupName      string   `json:"group_name,omitempty"`
	GroupRule      string   `json:"group_rule,omitempty"`
	GroupRuleLabel []L1Node `json:"group_rule_label,omitempty"`
}

func (*group) EditInfo(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	if id == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	rpc := grpc.GetchildrenGroupClient()
	rsp, err := rpc.Detail(context.Background(), &childrenusergroup.DetailRequest{
		ID: id,
	})
	if err != nil {
		t.Result(errorcode.SystemError, err.Error())
		return
	}
	if rsp.GetID() < 0 {
		t.Result(errorcode.SystemError)
		return
	}
	res := &infoRsp{
		ID:           rsp.GetID(),
		CreateTime:   rsp.GetCreateTime(),
		UpdateTime:   rsp.GetUpdateTime(),
		DeleteStatus: rsp.GetDeleteStatus(),
		GroupName:    rsp.GetGroupName(),
		GroupRule:    rsp.GroupRule,
	}
	var labels []L1Node
	err = json.Unmarshal([]byte(rsp.GetGroupRuleLabel()), &labels)
	if err != nil {
		t.Result(errorcode.SystemError)
		return
	}
	res.GroupRuleLabel = labels
	t.Result(res)
}

type listRsp struct {
	ID            int64  `json:"id,omitempty"`
	GroupRuleDesc string `json:"group_rule_desc,omitempty"`
	GroupName     string `json:"group_name"`
}

func (*group) ListInfo(t *http.Context) {
	page := t.GetRequestInt32D("page", 1)
	pageSize := t.GetRequestInt32D("page_size", library.MaxPageSize)
	name := t.GetRequestStringD("group_name", "")
	rpc := grpc.GetchildrenGroupClient()
	rsp, err := rpc.GetList(context.Background(), &childrenusergroup.ListRequest{
		Page:      page,
		PageSize:  pageSize,
		GroupName: name,
	})
	if err != nil {
		t.Result(errorcode.SystemError, err.Error())
		return
	}
	res := make([]*listRsp, 0)
	var wg sync.WaitGroup
	var lock sync.Mutex
	for _, val := range rsp.GetObjects() {
		v := val
		wg.Add(1)
		safelygo.GoSafelyByTraceID(func() {
			defer wg.Done()
			node1 := make(map[string]interface{})
			err = json.Unmarshal([]byte(v.GetGroupRule()), &node1)
			if err != nil {
				logger.Warn(err)
				return
			}
			node := &listRsp{
				ID:        v.GetID(),
				GroupName: v.GetGroupName(),
			}
			for i := 1; i <= len(node1); i++ {
				var ruleDesc string
				idx := strconv.Itoa(i)
				switch node1Val := node1[idx].(type) {
				case string:
					n1, _ := strconv.Atoi(node1Val)
					ruleDesc = lib.RuleRelationTypeDesc[lib.RuleRelationType(n1)]
					node.GroupRuleDesc = fmt.Sprintf("%s %s", node.GroupRuleDesc, ruleDesc)
				case map[string]interface{}:
					for i1 := 1; i1 <= len(node1Val); i1++ {
						idx1 := strconv.Itoa(i1)
						switch node2Val := node1Val[idx1].(type) {
						case string:
							n1, _ := strconv.Atoi(node2Val)
							ruleDesc = lib.RuleRelationTypeDesc[lib.RuleRelationType(n1)]
						case map[string]interface{}:
							val1Json, err := json.Marshal(node1Val[idx1])
							if err != nil {
								logger.Warn("rule_config Marshal", err)
								continue
							}
							var newData temNodeData
							err = json.Unmarshal(val1Json, &newData)
							if err != nil {
								logger.Warn("rule_config Unmarshal", err)
								continue
							}
							ruleDesc = formatLabelValue(newData)
						}
						node.GroupRuleDesc = fmt.Sprintf("%s %s", node.GroupRuleDesc, ruleDesc)
					}
				}
			}
			lock.Lock()
			defer lock.Unlock()
			res = append(res, node)
		})
	}
	wg.Wait()
	sort.Slice(res, func(i, j int) bool {
		return res[i].ID > res[j].ID
	})
	t.Result(res)
}

type DefaultConfigRsp struct {
	LabelCategoryList map[int]string `json:"label_category_list"`
}

func (*group) DefaultConfig(t *http.Context) {
	t.Result(&DefaultConfigRsp{
		LabelCategoryList: lib.LabelCategoryType,
	})
}

type GroupLabelItem struct {
	ID           int64  `json:"id"`
	CategoryType int32  `json:"category_type"`
	LabelDesc    string `json:"label_desc"`
	LabelName    string `json:"label_name"`
	LabelTitle   string `json:"label_title"`
}

func (*group) GetGroupLabelList(t *http.Context) {
	labelList := db.TbUserGroupLabelConfig.GetGroupLabelList()
	res := make([]*GroupLabelItem, 0)
	for _, v := range labelList {
		res = append(res, &GroupLabelItem{
			ID:           v.ID,
			CategoryType: v.CategoryType,
			LabelDesc:    v.LabelDesc,
			LabelName:    v.LabelName,
			LabelTitle:   v.LabelTitle,
		})
	}
	t.Result(res)
}

type GroupRuleConfigRsp struct {
	VIPStatus      map[int]string                    `json:"vip_status"`
	AdChannel      map[int]string                    `json:"ad_channel"`
	FitnessPurpose map[libp.FitnessPurposeInt]string `json:"fitness_purpose"`
	Gender         map[int]string                    `json:"gender"`
}

func (*group) GroupRuleConfig(t *http.Context) {
	t.Result(&GroupRuleConfigRsp{
		VIPStatus:      lib.CommonStatusRadioType,
		AdChannel:      clib.ChannelMap,
		FitnessPurpose: libp.FitnessPurposeDesc,
		Gender:         lib.GenderTypeDesc,
	})
}

// nolint
func formatLabelValue(r temNodeData) string {
	var res string
	labelList := db.TbUserGroupLabelConfig.GetGroupLabelList()
	labelConf := make(map[string]string)
	for _, v := range labelList {
		labelConf[v.LabelName] = v.LabelTitle
	}
	if _, ok := labelConf[r.LabelName]; !ok {
		return ""
	}
	var labelValue string
	switch r.LabelName {
	case "vip_status":
		n1, _ := strconv.Atoi(r.LabelValue)
		labelValue = lib.CommonStatusRadioType[n1]
	case "register_time":
		labelValue = r.LabelValue
	case "ad_channel":
		n1, _ := strconv.Atoi(r.LabelValue)
		labelValue = clib.ChannelMap[n1]
	case "fitness_purpose":
		n1, _ := strconv.Atoi(r.LabelValue)
		labelValue = libp.FitnessPurposeDesc[libp.FitnessPurposeInt(n1)]
	case "age":
		labelValue = r.LabelValue
	case "gender":
		n1, _ := strconv.Atoi(r.LabelValue)
		labelValue = lib.GenderTypeDesc[n1]
	}
	condition, _ := strconv.ParseInt(r.LabelCondition, 10, 64)
	res = fmt.Sprintf("%s %s %s", labelConf[r.LabelName], lib.ConditionDesc[int32(condition)], labelValue)
	return res
}

type temNodeData struct {
	LabelName      string `json:"label_name"`
	LabelCondition string `json:"label_condition"`
	LabelValue     string `json:"label_value"`
}

type NodeData struct {
	LabelName      string `json:"label_name"`
	LabelCondition int    `json:"label_condition"`
	LabelValue     int    `json:"label_value"`
}

type L2Node struct {
	RuleConfig NodeData `json:"rule_config"`
	Condition  string   `json:"condition,omitempty"`
}

type L1Node struct {
	RuleConfig []L2Node `json:"rule_config"`
	Condition  string   `json:"condition,omitempty"`
}

func formatGroupRuleLabel(node1 map[string]interface{}) (string, error) {
	nodeD := make([]L1Node, 0)
	for i := 1; i <= len(node1); i++ {
		idx := strconv.Itoa(i)
		nodeD2 := make([]L2Node, 0)
		switch node1Val := node1[idx].(type) {
		case string:
			fH := "AND" //nolint
			if node1Val == "1" {
				fH = "OR" //nolint
			}
			nodeD[len(nodeD)-1].Condition = fH
			continue
		case map[string]interface{}:
			for i1 := 1; i1 <= len(node1Val); i1++ {
				idx1 := strconv.Itoa(i1)
				switch node2Val := node1Val[idx1].(type) {
				case string:
					fH := "AND"
					if node2Val == "1" {
						fH = "OR"
					}
					nodeD2[len(nodeD2)-1].Condition = fH
					continue
				case map[string]interface{}:
					val1Json, err := json.Marshal(node1Val[idx1])
					if err != nil {
						logger.Warn("rule_config Marshal", err)
						return "", err
					}
					var newData temNodeData
					err = json.Unmarshal(val1Json, &newData)
					if err != nil {
						logger.Warn("rule_config Unmarshal", err)
						return "", err
					}
					lCondition, _ := strconv.Atoi(newData.LabelCondition)
					lValue, _ := strconv.Atoi(newData.LabelValue)
					ruleC := NodeData{
						LabelName:      newData.LabelName,
						LabelCondition: lCondition,
						LabelValue:     lValue,
					}
					nodeD2 = append(nodeD2, L2Node{
						RuleConfig: ruleC,
					})
				}
			}
		}
		l1Node := L1Node{
			RuleConfig: nodeD2,
		}
		nodeD = append(nodeD, l1Node)
	}
	data, err := json.Marshal(nodeD)
	if err != nil {
		return "", err
	}
	return string(data), nil
}

const ExpireTime = 10 * time.Minute

func (*group) SaveUserGroup(t *http.Context) {
	uid := t.GetRequestInt64D("uid", 0)
	userGroupID := t.GetRequestInt64D("user_group_id", 0)
	if uid == 0 || userGroupID == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	if err := cache.GetYogaRedis().GetClient().Set(context.Background(),
		fmt.Sprintf("%s%d", libCache.BindingUserGroupID, uid), userGroupID, ExpireTime).Err(); err != nil {
		logger.Warnf("后台修改分群失败：%v", err)
	}
	logDetail := fmt.Sprintf("后台绑定分群，uid:%d,user_group_id:%d", uid, userGroupID)
	adminNameInterface, _ := url.QueryUnescape(t.GetHeader(library.HeaderOperatorName))
	temp := dbUser.BindingOperateLog{
		UID:         uid,
		Parameter:   logDetail,
		OperateUser: adminNameInterface,
	}
	if err := temp.Save(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

func (*group) SaveUserRop(t *http.Context) {
	uid := t.GetRequestInt64D("uid", 0)
	experimentVersion := t.GetRequestStringD("experiment_version", "")
	if uid == 0 || experimentVersion == "" {
		t.Result(errorcode.InvalidParams)
		return
	}
	if err := cache.GetCRedis().GetClient().Set(context.Background(),
		fmt.Sprintf("%s%d", libCache.BindingRopExperimentVersion, uid), experimentVersion, ExpireTime).Err(); err != nil {
		logger.Warnf("后台修改rop失败：%v", err)
	}
	logDetail := fmt.Sprintf("后台绑定rop，uid:%d,experiment_version:%s", uid, experimentVersion)
	adminNameInterface, _ := url.QueryUnescape(t.GetHeader(library.HeaderOperatorName))
	temp := dbUser.BindingOperateLog{
		UID:         uid,
		Parameter:   logDetail,
		OperateUser: adminNameInterface,
	}
	if err := temp.Save(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

func (*group) DelUserBinding(t *http.Context) {
	uid := t.GetRequestInt64D("uid", 0)
	if uid == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	_, err := cache.GetYogaRedis().GetClient().Del(context.Background(),
		fmt.Sprintf("%s%d", libCache.BindingUserGroupID, uid)).Result()
	if err != nil {
		logger.Warnf("后台删除用户分群标签失败：%v", err)
		return
	}
	_, err = cache.GetCRedis().GetClient().Del(context.Background(),
		fmt.Sprintf("%s%d", libCache.BindingRopExperimentVersion, uid)).Result()
	if err != nil {
		logger.Warnf("后台删除用户rop标签失败：%v", err)
		return
	}
	logDetail := fmt.Sprintf("后台删除用户绑定标签，uid:%d", uid)
	adminNameInterface, _ := url.QueryUnescape(t.GetHeader(library.HeaderOperatorName))
	temp := dbUser.BindingOperateLog{
		UID:         uid,
		Parameter:   logDetail,
		OperateUser: adminNameInterface,
	}
	if err := temp.Save(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}
