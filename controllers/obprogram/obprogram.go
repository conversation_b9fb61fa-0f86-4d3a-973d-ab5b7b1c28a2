package obprogram

import (
	"encoding/json"
	"errors"
	"fmt"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases/course"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	libc "gitlab.dailyyoga.com.cn/server/children-admin-api/library/course"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
)

type programSchedule struct {
}

var ProgramSchedule programSchedule

type SessionItem struct {
	SessionIDs     []int64       `json:"session_ids"`
	DaySessionList []*DaySession `json:"day_session_list"`
}

type DaySession struct {
	SessionID   int64  `json:"session_id"`
	SessionName string `json:"session_name"`
	<PERSON><PERSON>       string `json:"alias"`
	CoverImg    string `json:"cover_img"`
}

type Item struct {
	ID          int64          `json:"id"`
	CycleSort   int64          `json:"cycle_sort"`
	Name        string         `json:"name"`
	SessionList []*SessionItem `json:"session_list"`
}

type Schedule struct {
	ID            int64   `json:"id"`
	Name          string  `json:"name"`
	UserGroupID   int64   `json:"user_group_id"`
	Priority      int64   `json:"priority"`
	ChangeType    int32   `json:"change_type"`
	CreateTime    int64   `json:"create_time"`
	ObProgramList []*Item `json:"obprogram_list"`
}

func (p *programSchedule) Save(t *http.Context) {
	req := &Schedule{}
	if err := json.Unmarshal(library.GetBody(t), &req); err != nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	if err := p.SaveSrv(req); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(errorcode.Success)
}

// nolint
func CheckSaveSrv(req *Schedule) error {
	if len(req.ObProgramList) == 0 {
		return errors.New("课程表配置不能为空")
	}
	item := course.TbObProSch.GetItemByPriority(req.Priority)
	if req.Priority != 0 && item != nil && (req.ID == 0 || req.ID != item.ID) {
		return errors.New(fmt.Sprintf("优先级和课表id=%d 重复，请修改", item.ID))
	}
	cycleSort := make(map[int64]int)
	for _, v := range req.ObProgramList {
		if len(v.SessionList) != libc.SessionListNum {
			return errors.New("课表课程必须是21天")
		}
		cycleSort[v.CycleSort]++
		if cycleSort[v.CycleSort] != 1 {
			return errors.New("循环顺序不能重复")
		}
		for _, sessionItem := range v.SessionList {
			if len(sessionItem.SessionIDs) == 0 || len(sessionItem.SessionIDs) > library.No {
				return errors.New("每天的课程支持多选，上限两个")
			}
		}
	}
	return nil
}

// nolint
func (*programSchedule) SaveSrv(req *Schedule) error {
	if err := CheckSaveSrv(req); err != nil {
		return err
	}
	schedule := course.ObProgramSchedule{
		ID:          req.ID,
		Name:        req.Name,
		UserGroupID: req.UserGroupID,
		Priority:    req.Priority,
		IsDel:       library.No,
		ChangeType:  library.Yes,
	}
	session := databases.GetEngineMaster().NewSession()
	var err error
	defer session.Close()
	if err := session.Begin(); err != nil {
		return err
	}
	defer func() {
		if err != nil {
			logger.Error(err)
			if err = session.Rollback(); err != nil {
				logger.Error(err)
			}
		}
	}()
	if schedule.ID == 0 {
		err = schedule.SaveByTran(session)
		if err != nil {
			return err
		}
	} else {
		err = schedule.UpdateByTran(session)
		if err != nil {
			return err
		}
	}
	schTableMap := make(map[int64]bool)
	for _, v := range course.TbObProSchTable.GetListByScheduleID(schedule.ID) {
		schTableMap[v.ID] = true
	}
	for _, v := range req.ObProgramList {
		if schTableMap[v.ID] {
			schTableMap[v.ID] = false
		}
	}
	for k, v := range schTableMap {
		if !v {
			continue
		}
		if item := course.TbObProSchTable.GetItemByID(k); item != nil {
			item.IsDel = library.Yes
			err = item.UpdateByTran(session)
			if err != nil {
				return err
			}
		}
	}
	for _, v := range req.ObProgramList {
		sessionList, _ := json.Marshal(v.SessionList)
		scheduleTable := course.ObProgramScheduleTable{
			ID:          v.ID,
			ScheduleID:  schedule.ID,
			Name:        v.Name,
			CycleSort:   v.CycleSort,
			SessionList: string(sessionList),
			IsDel:       library.No,
		}
		if scheduleTable.ID == 0 {
			err = scheduleTable.SaveByTran(session)
			if err != nil {
				return err
			}
		} else {
			err = scheduleTable.UpdateByTran(session)
			if err != nil {
				return err
			}
		}
	}
	if err := session.Commit(); err != nil {
		return err
	}
	return nil
}

func (p *programSchedule) Info(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	if id == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	item := course.TbObProSch.GetItemByID(id)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	t.Result(formatObProSch(item))
}

func formatObProSch(item *course.ObProgramSchedule) *Schedule {
	resp := &Schedule{
		ID:            item.ID,
		Name:          item.Name,
		UserGroupID:   item.UserGroupID,
		Priority:      item.Priority,
		ChangeType:    item.ChangeType,
		CreateTime:    item.CreateTime,
		ObProgramList: make([]*Item, 0),
	}
	sessionIDArr := make([]int64, 0)
	for _, v := range course.TbObProSchTable.GetListByScheduleID(item.ID) {
		sessionList := make([]*SessionItem, 0)
		if err := json.Unmarshal([]byte(v.SessionList), &sessionList); err != nil {
			logger.Error(err)
		}
		for i := range sessionList {
			sessionIDArr = append(sessionIDArr, sessionList[i].SessionIDs...)
		}
	}
	courseMap := course.TbCLibrary.GetListMap(sessionIDArr)
	for _, v := range course.TbObProSchTable.GetListByScheduleID(item.ID) {
		sessionList := make([]*SessionItem, 0)
		if err := json.Unmarshal([]byte(v.SessionList), &sessionList); err != nil {
			logger.Error(err)
		}
		for i := range sessionList {
			if len(sessionList[i].SessionIDs) == len(sessionList[i].DaySessionList) {
				continue
			}
			sessionList[i].DaySessionList = make([]*DaySession, 0)
			for _, sessionID := range sessionList[i].SessionIDs {
				courseInfo := courseMap[sessionID]
				if courseInfo == nil {
					continue
				}
				sessionList[i].DaySessionList = append(sessionList[i].DaySessionList, &DaySession{
					SessionID:   sessionID,
					SessionName: courseInfo.Title,
				})
			}
		}
		scheduleItem := &Item{
			ID:          v.ID,
			CycleSort:   v.CycleSort,
			Name:        v.Name,
			SessionList: sessionList,
		}
		resp.ObProgramList = append(resp.ObProgramList, scheduleItem)
	}
	return resp
}

func (p *programSchedule) Del(t *http.Context) {
	req := &Schedule{}
	if err := json.Unmarshal(library.GetBody(t), &req); err != nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	item := course.TbObProSch.GetItemByID(req.ID)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	item.IsDel = library.Yes
	if err := item.Update(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	for _, v := range course.TbObProSchTable.GetListByScheduleID(item.ID) {
		v.IsDel = library.Yes
		if err := v.Update(); err != nil {
			logger.Error(err)
		}
	}
	t.Result(errorcode.Success)
}

func (p *programSchedule) List(t *http.Context) {
	name := t.GetRequestStringD("name", "")
	userGroupID := t.GetRequestInt64D("user_group_id", 0)
	resp := make([]*Schedule, 0)
	for _, v := range course.TbObProSch.GetList(name, userGroupID) {
		resp = append(resp, formatObProSch(v))
	}
	t.Result(resp)
}

func (p *programSchedule) Change(t *http.Context) {
	req := &Schedule{}
	if err := json.Unmarshal(library.GetBody(t), &req); err != nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	item := course.TbObProSch.GetItemByID(req.ID)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	if item.ChangeType == library.Yes {
		item.ChangeType = library.No
	} else {
		item.ChangeType = library.Yes
	}
	if err := item.Update(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(errorcode.Success)
}
