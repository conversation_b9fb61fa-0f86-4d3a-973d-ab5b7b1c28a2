package admin

import (
	"context"
	"errors"
	"fmt"
	"io"
	"mime/multipart"
	"os"
	"path"
	"strings"
	"time"

	"github.com/qiniu/go-sdk/v7/auth"
	"github.com/qiniu/go-sdk/v7/auth/qbox"
	"github.com/qiniu/go-sdk/v7/storage"
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/config"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/client"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
	srvadmin "gitlab.dailyyoga.com.cn/server/children-admin-api/service/admin"
	srvutil "gitlab.dailyyoga.com.cn/server/children-admin-api/service/util"
)

type file struct {
}

var AdminFile file

type result struct {
	URLQiniu  string `json:"url_qiniu"`
	ImageSize string `json:"imageSize"`
}

const (
	DancefitQiuniuImageBucket = "dailyfitness"
	MaxUploadSize             = 1024 * 1024 * 200
)

func (f *file) QiYuUploadFile(t *http.Context) {
	if !srvadmin.CheckQiYuCs(t.GetRequestStringD("md5", ""), t.GetRequestInt64D("timestamp", 0)) {
		t.Result(errorcode.InvalidParams, "QiYu认证失败")
		return
	}
	f.UploadFile(t)
}

func (*file) UploadFile(t *http.Context) {
	fileType := t.GetRequestIntD("file_type", 0)
	if _, ok := client.FileTypeDir[client.FileType(fileType)]; !ok {
		t.Result(errorcode.SystemError, "文件类型错误")
		return
	}
	logger.Info("UploadFile-1", fileType)
	var err error
	r := t.Request
	err = r.ParseMultipartForm(MaxUploadSize)
	if err != nil {
		logger.Error(err)
		t.Result(errorcode.SystemError)
		return
	}
	logger.Info("UploadFile-2", fileType)
	files, original, err := r.FormFile("file")
	if err != nil {
		logger.Error(err)
		t.Result(errorcode.SystemError)
		return
	}
	logger.Info("UploadFile-3", fileType)
	address, err := uploadFileItem(original, files, client.FileType(fileType))
	if err != nil || address == "" {
		logger.Warn("上传文件失败", err, original.Filename)
		t.Result(errorcode.SystemError, err.Error())
		return
	}
	logger.Info("UploadFile-4", fileType)
	ret := result{
		URLQiniu: address,
	}
	logger.Info("UploadFile-5", fileType)
	if fileType == int(client.FileTypeEnum.Image) {
		ret.URLQiniu = strings.Replace(ret.URLQiniu, client.QiniuCDNResourceDomain, client.QiniuResourceDomain, 1)
		size := srvutil.GetImageSizeByQiNiuURL(ret.URLQiniu)
		ret.ImageSize = fmt.Sprintf("%dx%d", size.Width, size.Height)
	}
	logger.Info("UploadFile-6", fileType)
	t.Result(ret)
}

func makeFileName(originalName string) string {
	data := []byte(fmt.Sprintf("%d%s", time.Now().UnixNano(), originalName))
	ext := path.Ext(originalName)
	return fmt.Sprintf("%s%s", srvutil.Byte2Md5(data)+"xayl", ext)
}

// putToQiniu 上传至七牛云存储
func putToQiniu(fileName, localPath string) *storage.PutRet {
	putPolicy := storage.PutPolicy{
		Scope:   client.QiniuResourceBucket,
		Expires: client.QiniuTokenDefaultExpire,
	}
	cfg := config.Get().QiniuCDN
	mac := qbox.NewMac(cfg.AccessKey, cfg.AccessSecret)
	uploadToken := putPolicy.UploadToken(mac)
	storageCfg := storage.Config{
		Zone:          &storage.ZoneHuadong,
		UseHTTPS:      true,
		UseCdnDomains: false,
	}

	formUploader := storage.NewFormUploader(&storageCfg)
	ret := storage.PutRet{}
	putExtra := storage.PutExtra{}

	var err error
	defer func() {
		if err != nil {
			logger.Error(err)
		}
	}()
	err = formUploader.PutFile(context.Background(), &ret, uploadToken, fileName, localPath, &putExtra)
	if err != nil {
		return nil
	}
	return &ret
}

type UploadFileRsp struct {
	List []string `json:"url_list"`
}

func (*file) BatchUploadFile(t *http.Context) {
	fileType := t.GetRequestIntD("file_type", 0)
	if _, ok := client.FileTypeDir[client.FileType(fileType)]; !ok {
		t.Result(errorcode.SystemError, "文件类型错误")
		return
	}
	fileHeaderList := parseFileList(t)
	if len(fileHeaderList) == 0 {
		t.Result(errorcode.InvalidParams, "无效文件")
		return
	}
	res := UploadFileRsp{
		List: make([]string, 0),
	}
	for _, v := range fileHeaderList {
		original := v
		logger.Info("file info", original.Filename, original.Header.Get("Content-Type"))
		file, err := v.Open()
		if err != nil {
			logger.Error(err)
			continue
		}
		address, err := uploadFileItem(original, file, client.FileType(fileType))
		if err != nil || address == "" {
			logger.Warn("上传文件失败", err, original.Filename)
			continue
		}
		res.List = append(res.List, address)
	}
	t.Result(res)
}

// uploadFileItem 上传单个文件
func uploadFileItem(original *multipart.FileHeader, file multipart.File,
	fileType client.FileType) (string, error) {
	var err error
	fileNameOriginal := original.Filename
	defer func() {
		err = file.Close()
	}()
	err = os.Mkdir("./upload/", os.ModePerm)
	if err != nil {
		if os.IsNotExist(err) {
			logger.Error(err)
			return "", err
		}
	}
	fileName := makeFileName(fileNameOriginal)
	saveKey := fmt.Sprintf("%s/%s", client.FileTypeDir[fileType], fileName)
	localPath := "./upload/" + fileName
	var dst *os.File
	dst, err = os.Create(localPath)
	defer func() {
		err = dst.Close()
	}()

	if err != nil {
		return "", err
	}

	_, err = io.Copy(dst, file)
	putRet := putToQiniu(saveKey, localPath)
	if putRet == nil || putRet.Key != saveKey {
		return "", errors.New("上传失败")
	}
	// 用完之后把文件删除
	err = os.RemoveAll(localPath)
	if err != nil {
		logger.Error(err)
	}
	return client.QiniuCDNResourceDomain + "/" + client.FileTypeDir[fileType] + "/" + fileName, nil
}

// parseFileList 解析多个文件
func parseFileList(t *http.Context) []*multipart.FileHeader {
	if t.Request.MultipartForm == nil {
		err := t.Request.ParseMultipartForm(MaxUploadSize)
		if err != nil {
			return nil
		}
	}
	if t.Request.MultipartForm != nil && t.Request.MultipartForm.File != nil {
		if fhs := t.Request.MultipartForm.File["file"]; len(fhs) > 0 {
			return fhs
		}
	}
	return nil
}

func FTUpdateObjectStatus(key string) {
	cfgQin := config.Get().QiniuCDN
	mac := auth.New(cfgQin.AccessKey, cfgQin.AccessSecret)
	cfg := &storage.Config{}
	// 空间对应的机房
	cfg.Zone = &storage.ZoneHuadong
	// 是否使用https域名
	cfg.UseHTTPS = false
	// 上传是否使用CDN上传加速
	cfg.UseCdnDomains = false

	// 构建代理client对象
	tsr := storage.NewBucketManager(mac, cfg)
	err := tsr.UpdateObjectStatus(client.QiniuResourceBucket, key, true)
	if err != nil {
		fmt.Println(err)
	}
}
