package admin

import (
	"encoding/json"
	"errors"
	"fmt"
	"io"
	nhttp "net/http"
	"strings"
	"time"

	"github.com/qiniu/go-sdk/v7/auth/qbox"
	"github.com/qiniu/go-sdk/v7/storage"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	conf "gitlab.dailyyoga.com.cn/server/children-admin-api/config"
	dbclient "gitlab.dailyyoga.com.cn/server/children-admin-api/databases/client"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/client"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
	libp "gitlab.dailyyoga.com.cn/server/children-admin-api/library/product"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/service/util"
)

type aclient struct {
}

var AdminClient aclient

type ObPageRop struct {
	ObType int       `json:"ob_type"`
	Conf   []*ObPage `json:"conf"`
}

type ObPage struct {
	TerminalType int `json:"terminal_type"`
	PageList     []struct {
		ChannelList []int `json:"channel_list,omitempty"`
		PageID      int   `json:"page_id"`
		Flow        int   `json:"flow"`
	} `json:"sku_list"`
}

type ObPageRopSaveReq struct {
	ObType       int `json:"ob_type"`
	TerminalType int `json:"terminal_type"`
	PageList     []struct {
		ChannelList []int `json:"channel_list,omitempty"`
		PageID      int   `json:"page_id"`
		Flow        int   `json:"flow"`
	} `json:"sku_list"`
}

func (*aclient) ObPageRopSave(t *http.Context) {
	obPageReq := &ObPageRopSaveReq{}
	t.ParseRequestStruct(obPageReq)
	if obPageReq.ObType == 0 {
		t.Result(errorcode.InvalidParams, "OB类型不能为空")
		return
	}
	if err := CheckObPageRopValid(obPageReq); err != nil {
		t.Result(errorcode.SystemError, err.Error())
		return
	}
	confKey := ""
	if obPageReq.ObType == client.ObPageRopObTypeNew {
		confKey = client.ObPageRopObTypeNewKey
	}
	if obPageReq.ObType == client.ObPageRopObTypeChannel {
		confKey = client.ObPageRopObTypeChannelKey
	}
	info := dbclient.TbConfig.GetItemByKey(confKey)
	if info == nil {
		t.Result(errorcode.DBError)
		return
	}
	var obPageRopDB ObPageRop
	obPageRopDB.Conf = make([]*ObPage, 0)
	if err := json.Unmarshal([]byte(info.Value), &obPageRopDB); err != nil {
		t.Result(errorcode.SystemError, err.Error())
		return
	}
	isChange := false
	for k := range obPageRopDB.Conf {
		if obPageRopDB.Conf[k].TerminalType == obPageReq.TerminalType {
			obPageRopDB.Conf[k] = &ObPage{
				TerminalType: obPageReq.TerminalType,
				PageList:     obPageReq.PageList,
			}
			isChange = true
		}
	}
	if !isChange {
		t.Result(errorcode.SystemError, "未找到对应设备类型配置")
		return
	}
	configByte, err := json.Marshal(obPageRopDB)
	if err == nil && len(configByte) != 0 {
		info.Value = string(configByte)
	}
	if err := info.Update(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

func CheckObPageRopValid(obPageRop *ObPageRopSaveReq) error {
	if obPageRop.TerminalType == 0 {
		return errors.New("客户端类型错误")
	}
	if len(obPageRop.PageList) == 0 {
		return errors.New("付费页不能为空")
	}
	flow := 0
	channelFlowMap := make(map[int]int)
	for _, page := range obPageRop.PageList {
		if page.PageID == 0 {
			return errors.New("付费页ID为空")
		}
		for _, v := range page.ChannelList {
			channelFlowMap[v] += page.Flow
		}
		flow += page.Flow
	}
	if obPageRop.ObType == client.ObPageRopObTypeChannel {
		for _, v := range channelFlowMap {
			if v > client.FlowAll {
				return errors.New("渠道用户付费页流量总和不能大于100%")
			}
		}
	}
	if obPageRop.ObType == client.ObPageRopObTypeNew && flow != client.FlowAll {
		return errors.New("新用户付费页流量总和需为100%")
	}
	return nil
}

func SaveConf(keyName, value string) error {
	info := dbclient.TbConfig.GetItemByKey(keyName)
	if info == nil {
		return errors.New("查询配置失败")
	}
	info.Value = value
	if err := info.Update(); err != nil {
		return err
	}
	return nil
}

type ObPageRopConfigRsp struct {
	ChannelList map[int]string `json:"channel_list"`
}

func (*aclient) ObPageRopConfig(t *http.Context) {
	t.Result(&ObPageRopConfigRsp{
		ChannelList: client.ChannelMap,
	})
}

func (*aclient) ObPageRopInfo(t *http.Context) {
	obType := t.GetRequestInt64D("ob_type", 0)
	if obType == 0 {
		t.Result(errorcode.InvalidParams, "OB类型不能为空")
		return
	}
	confKey := ""
	if obType == client.ObPageRopObTypeNew {
		confKey = client.ObPageRopObTypeNewKey
	}
	if obType == client.ObPageRopObTypeChannel {
		confKey = client.ObPageRopObTypeChannelKey
	}
	var obPageRop ObPageRop
	obPageRop.Conf = make([]*ObPage, 0)
	info := dbclient.TbConfig.GetItemByKey(confKey)
	if info != nil {
		if err := json.Unmarshal([]byte(info.Value), &obPageRop); err != nil {
			obPageRop.Conf = make([]*ObPage, 0)
			t.Result(obPageRop)
			return
		}
	}
	t.Result(obPageRop)
}

const MaxAppVersion = "99.99.99"

type AuditInfoConfig struct {
	IOSAuditVersion    string   `json:"ios_audit_version"`
	IsIOSChannelIAP    bool     `json:"is_ios_channel_iap"`
	VivoAuditVersion   string   `json:"vivo_audit_version"`
	HuaweiAuditVersion string   `json:"huawei_audit_version"`
	ChoiceVersion      []string `json:"choice_version"`
}

func (*aclient) AuditInfo(t *http.Context) {
	info := dbclient.TbConfig.GetItemByKey(client.AuditInfoConfig)
	if info == nil {
		t.Result(errorcode.SystemError)
		return
	}
	config := &AuditInfoConfig{}
	err := json.Unmarshal([]byte(info.Value), config)
	if err != nil {
		t.Result(err)
		return
	}
	config.ChoiceVersion = []string{MaxAppVersion}
	if version := getAppVersion(client.DeviceTypeEnum.IOS); version != "" {
		config.ChoiceVersion = append(config.ChoiceVersion, version)
	}
	config.IOSAuditVersion = strings.Split(config.IOSAuditVersion, ",")[1]
	t.Result(config)
}

func getAppVersion(osType client.DeviceTypeInt) string {
	url := fmt.Sprintf("%s?app_name=children", conf.Get().AppVersionURL)
	// 发送 HTTP GET 请求
	header := nhttp.Header{}
	header.Add("Content-Type", "application/x-www-form-urlencoded")
	resp, err := util.HTTPRequest(url, "GET", header, nil)
	if err != nil {
		return ""
	}
	// 解析 JSON 数据
	var result map[string]interface{}
	err = json.Unmarshal(resp, &result)
	if err != nil {
		return ""
	}
	// 提取 Version 字段
	if osType == client.DeviceTypeEnum.IOS {
		if data, ok := result["data"].(map[string]interface{}); ok {
			if version, ok := data["ios"].(string); ok {
				return version
			}
		}
	} else {
		if data, ok := result["data"].(map[string]interface{}); ok {
			if version, ok := data["android"].(string); ok {
				return version
			}
		}
	}
	return ""
}

func (*aclient) AuditUpdate(t *http.Context) {
	params := &AuditInfoConfig{}
	t.ParseRequestStruct(params)
	params.IOSAuditVersion = MaxAppVersion + "," + params.IOSAuditVersion
	info := dbclient.TbConfig.GetItemByKey(client.AuditInfoConfig)
	if info == nil {
		t.Result(errorcode.SystemError)
		return
	}
	config := &AuditInfoConfig{}
	err := json.Unmarshal([]byte(info.Value), config)
	if err != nil {
		t.Result(err)
		return
	}
	if params.IOSAuditVersion != "" {
		config.IOSAuditVersion = params.IOSAuditVersion
		config.IsIOSChannelIAP = params.IsIOSChannelIAP
	}
	if params.VivoAuditVersion != "" {
		config.VivoAuditVersion = params.VivoAuditVersion
	}
	if params.HuaweiAuditVersion != "" {
		config.HuaweiAuditVersion = params.HuaweiAuditVersion
	}
	configByte, err := json.Marshal(config)
	if err == nil && len(configByte) != 0 {
		info.Value = string(configByte)
	}
	if err := info.Update(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

type CustomerServiceConf struct {
	IsOpenQiyu bool `json:"is_open_qiyu"`
}

func (*aclient) CustomerServiceConfInfo(t *http.Context) {
	info := dbclient.TbConfig.GetItemByKey(client.CustomerServiceConfig)
	if info == nil {
		t.Result(errorcode.SystemError)
		return
	}
	config := &CustomerServiceConf{}
	err := json.Unmarshal([]byte(info.Value), config)
	if err != nil {
		t.Result(err)
		return
	}
	t.Result(config)
}

type IPIdentityConf struct {
	UserIPSwitch bool `json:"user_ip_switch"`
	CityList     []struct {
		Label    string `json:"label"`
		Value    int64  `json:"value"`
		Selected int64  `json:"selected"`
	} `json:"city_list"`
	WhiteIP string `json:"white_ip"`
}

func (*aclient) UpdateIPIdentityConf(t *http.Context) {
	key := client.ConfigIdentityIP
	body := library.GetBody(t)
	info := dbclient.TbConfig.GetItemByKey(key)
	if info != nil {
		info.Value = string(body)
		if err := info.Update(); err != nil {
			t.Result(errorcode.DBError)
			return
		}
		t.Result(library.EmptyResponse{})
		return
	}
	info = &dbclient.Config{}
	info.Key = key
	info.Value = string(body)
	info.KeyName = "IP甄别配置"
	if err := info.Save(); err != nil {
		t.Result(errorcode.DBError)
		return
	}
	t.Result(library.EmptyResponse{})
}

func (*aclient) GetIPIdentityConf(t *http.Context) {
	config := dbclient.TbConfig.GetItemByKey(client.ConfigIdentityIP)
	if config == nil {
		t.Result(library.EmptyResponse{})
		return
	}
	res := &IPIdentityConf{}
	err := json.Unmarshal([]byte(config.Value), &res)
	if err != nil {
		t.Result(errorcode.SystemError)
		return
	}
	t.Result(res)
}

type ComplianceConfigParams struct {
	DeviceType string
	Data       string
}

func (*aclient) ComplianceConfigUpdate(t *http.Context) {
	deviceType := t.GetRequestStringD("device_type", "1")
	// 将前端传来的设备类型转换为存储用的渠道号
	channelKey := library.ConvertDeviceTypeToChannel(deviceType)
	data := t.GetRequestStringD("data", "")
	if data == "" {
		t.Result(errorcode.InvalidParams)
		return
	}
	config := dbclient.TbConfig.GetItemByKey(client.ComplianceConfig)
	if config == nil {
		t.Result(errorcode.DBError)
		return
	}
	var complianceConfigMap = map[string]ComplianceItem{}
	err := json.Unmarshal([]byte(config.Value), &complianceConfigMap)
	if err != nil {
		t.Result(errorcode.DBError)
		return
	}

	updateInfo := &ComplianceItem{}
	err = json.Unmarshal([]byte(data), &updateInfo)
	if err != nil {
		t.Result(errorcode.DBError)
		return
	}
	complianceConfigMap[channelKey] = *updateInfo
	configBytes, err := json.Marshal(complianceConfigMap)
	if err != nil {
		t.Result(errorcode.DBError)
		return
	}
	config.Value = string(configBytes)
	if err = config.Update(); err != nil {
		t.Result(errorcode.DBError)
		return
	}

	t.Result(library.EmptyResponse{})
}

type AreaItem struct {
	Label    string           `json:"label"`
	AreaCode string           `json:"areacode"`
	Value    int              `json:"value"`
	Selected int              `json:"selected"`
	Invalid  *InvalidDuration `json:"invalid,omitempty"`
}

type InvalidDuration struct {
	IsContainHoliday bool   `json:"is_contain_holiday"`
	StartDate        int64  `json:"start_date"`
	EndDate          int64  `json:"end_date"`
	StartHour        string `json:"start_hour"`
	EndHour          string `json:"end_hour"`
}
type ComplianceItem struct {
	Natural []AreaItem   `json:"natural"`
	Channel []AreaItem   `json:"channel"`
	WhiteIP string       `json:"white_ip"`
	Switch  []SwitchItem `json:"switch,omitempty"`
}

type SwitchItem struct {
	Name  string `json:"name"`
	Key   string `json:"key"`
	Value bool   `json:"value"`
}

func (*aclient) ComplianceConfigInfo(t *http.Context) {
	deviceType := t.GetRequestStringD("device_type", "1")
	// 将前端传来的设备类型转换为存储用的渠道号
	channelKey := library.ConvertDeviceTypeToChannel(deviceType)

	bc := dbclient.TbConfig.GetItemByKey(client.ComplianceConfig)
	if bc == nil {
		t.Result(errorcode.DBError)
		return
	}
	var complianceConfigMap = map[string]ComplianceItem{}
	err := json.Unmarshal([]byte(bc.Value), &complianceConfigMap)
	if err != nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	t.Result(complianceConfigMap[channelKey])
}

const (
	DateTypeHoliday        = 1
	DateTypeInvalidWeekEnd = 2
)

func (*aclient) ComplianceHoliday(t *http.Context) {
	list := dbclient.TbYearHoliday.GetListByYear(time.Now().Format("2006"))
	holiday := ""
	invalidWeekEnd := ""
	for _, v := range list {
		// 解析字符串为时间对象
		date, err := time.Parse("20060102", v.DateIndex)
		if err != nil {
			continue
		}
		// 提取月份和日期
		month := date.Month()
		day := date.Day()
		// 格式化为 "4月1日" 格式的字符串
		formattedDate := fmt.Sprintf("%d月%d日", month, day)
		if v.DateType == DateTypeHoliday {
			holiday += formattedDate + ","
		} else {
			invalidWeekEnd += formattedDate + ","
		}
	}
	res := struct {
		Holiday        string `json:"holiday"`
		InvalidWeekEnd string `json:"invalid_week_end"`
	}{
		Holiday:        strings.Trim(holiday, ","),
		InvalidWeekEnd: strings.Trim(invalidWeekEnd, ","),
	}
	t.Result(res)
}

type AndroidAuditItem struct {
	ClientChannel       []string                     `json:"client_channel"`
	AndroidAuditVersion string                       `json:"android_audit_version"`
	AllChannel          []string                     `json:"all_channel"`
	ChoiceVersion       []string                     `json:"choice_version"`
	OnlineConf          map[string][]*OnlineConfItem `json:"online_conf"`
}

type OnlineConfItem struct {
	Name  string `json:"name"`
	Key   string `json:"key"`
	Value bool   `json:"value"`
}

func (*aclient) AndroidAuditConfigUpdate(t *http.Context) {
	bc := dbclient.TbConfig.GetItemByKey(client.AndroidAuditConfig)
	if bc == nil {
		t.Result(errorcode.DBError)
		return
	}
	androidAuditConfig := &AndroidAuditItem{}
	t.ParseRequestStruct(androidAuditConfig)
	androidAuditConfig.AndroidAuditVersion = MaxAppVersion + "," + androidAuditConfig.AndroidAuditVersion
	androidAuditConfig.AllChannel = []string{library.ChannelHuawei, library.ChannelOPPO,
		library.ChannelVIVO, library.ChannelXiaomi, library.ChannelOther}
	value, err := json.Marshal(androidAuditConfig)
	if err != nil {
		t.Result(errorcode.DBError)
		return
	}
	bc.Value = string(value)
	if err := bc.Update(); err != nil {
		t.Result(errorcode.DBError)
		return
	}
	t.Result(library.EmptyResponse{})
}

func (*aclient) AndroidAuditConfigInfo(t *http.Context) {
	bc := dbclient.TbConfig.GetItemByKey(client.AndroidAuditConfig)
	if bc == nil {
		t.Result(errorcode.DBError)
		return
	}
	androidAuditConfig := &AndroidAuditItem{}
	err := json.Unmarshal([]byte(bc.Value), &androidAuditConfig)
	if err != nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	androidAuditConfig.ChoiceVersion = []string{MaxAppVersion}
	if version := getAppVersion(client.DeviceTypeEnum.Android); version != "" {
		androidAuditConfig.ChoiceVersion = append(androidAuditConfig.ChoiceVersion, version)
	}
	androidAuditConfig.AndroidAuditVersion = strings.Split(androidAuditConfig.AndroidAuditVersion, ",")[1]
	res := struct {
		ClientChannel       []string                     `json:"client_channel"`
		AndroidAuditVersion string                       `json:"android_audit_version"`
		ChoiceVersion       []string                     `json:"choice_version"`
		OnlineConf          map[string][]*OnlineConfItem `json:"online_conf"`
	}{
		ClientChannel:       androidAuditConfig.ClientChannel,
		AndroidAuditVersion: androidAuditConfig.AndroidAuditVersion,
		ChoiceVersion:       androidAuditConfig.ChoiceVersion,
		OnlineConf:          androidAuditConfig.OnlineConf,
	}
	t.Result(res)
}

type CommonChannel struct {
	Name    string `json:"name"`
	Channel string `json:"channel"`
}

func (*aclient) CommonChannel(t *http.Context) {
	t.Result([]CommonChannel{
		{Name: "华为", Channel: library.ChannelHuawei},
		{Name: "OPPO", Channel: library.ChannelOPPO},
		{Name: "ViVo", Channel: library.ChannelVIVO},
		{Name: "小米", Channel: library.ChannelXiaomi},
		{Name: "其他小渠道", Channel: library.ChannelOther},
	})
}

type JuLiangSwitch struct {
	Android bool `json:"android"`
	Ios     bool `json:"ios"`
}

func (*aclient) JuLiangSwitchSave(t *http.Context) {
	params := library.GetBody(t)
	key := client.JuLiangSwitch
	err := SaveConfig(key, "巨量开关", string(params))
	if err != nil {
		t.Result(errorcode.DBError)
		return
	}
	config := &JuLiangSwitch{}
	_ = json.Unmarshal(params, config)
	t.Result(config)
}

func (*aclient) GetJuLiangSwitchInfo(t *http.Context) {
	info := dbclient.TbConfig.GetItemByKey(client.JuLiangSwitch)
	juLiangSwitch := &JuLiangSwitch{}
	if info != nil {
		err := json.Unmarshal([]byte(info.Value), juLiangSwitch)
		if err != nil {
			t.Result(errorcode.SystemError)
			return
		}
	}
	t.Result(juLiangSwitch)
}

func SaveConfig(key, keyName, value string) error {
	schedule := &dbclient.Config{
		Key:     key,
		KeyName: keyName,
		Value:   value,
	}
	info := dbclient.TbConfig.GetItemByKey(key)
	if info == nil {
		if err := schedule.Save(); err != nil {
			return err
		}
	} else {
		schedule.ID = info.ID
		if err := schedule.Update(); err != nil {
			return err
		}
	}
	return nil
}

type GetQiNiuTokenResp struct {
	UploadToken string `json:"upload_token"`
	Directory   string `json:"directory"`
}

func (*aclient) GetQiNiuToken(t *http.Context) {
	resp := &GetQiNiuTokenResp{}
	fileType := client.FileTypeEnum.Video
	resp.Directory = client.FileTypeDir[fileType]
	putPolicy := storage.PutPolicy{
		Scope:   client.QiniuResourceBucket,
		Expires: client.QiniuTokenDefaultExpire,
	}
	cfg := conf.Get().QiniuCDN
	mac := qbox.NewMac(cfg.AccessKey, cfg.AccessSecret)
	resp.UploadToken = putPolicy.UploadToken(mac)
	t.Result(resp)
}

type CopyWritingRop struct {
	CopyWriting      string `json:"copywriting"`
	CopyWritingTitle string `json:"copywriting_title"`
	Flow             int    `json:"flow"`
}

type AliPaySigningCopyWriting struct {
	OfferType             int              `json:"offer_type"`
	Conf                  []CopyWritingRop `json:"conf"`
	CheckCopyWriting      string           `json:"check_copywriting"`
	CheckCopyWritingTitle string           `json:"check_copywriting_title"`
}

// nolint
func (*aclient) AliPaySigningCopyWritingSave(t *http.Context) {
	params := &AliPaySigningCopyWriting{}
	body := library.GetBody(t)
	if err := json.Unmarshal(body, &params); err != nil {
		t.Result(errorcode.InvalidParams, err.Error())
		return
	}
	switch params.OfferType {
	case libp.ConstOfferTypeNo:
	case libp.ConstOfferTypeFirstBuy:
	case libp.ConstOfferTypeTrial:
	case libp.ConstOfferTypeTrialFirstBuy:
	default:
		t.Result(errorcode.InvalidParams)
		return
	}
	key := libp.ConstOfferTypeConfKey[params.OfferType]
	if len(params.Conf) == 0 {
		t.Result(errorcode.InvalidParams, "文案配置不能为空")
		return
	}
	flow := 0
	for _, cnf := range params.Conf {
		if cnf.CopyWriting == "" || cnf.CopyWritingTitle == "" {
			t.Result(errorcode.InvalidParams, "会员文案/标题不能为空")
			return
		}
		flow += cnf.Flow
	}
	if flow != client.FlowAll {
		t.Result(errorcode.InvalidParams, "流量总和必须为100%")
		return
	}
	paramsByte, _ := json.Marshal(params)
	if err := SaveConfig(key, libp.OfferTypeDesc[params.OfferType], string(paramsByte)); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

func (*aclient) AliPaySigningCopyWritingInfo(t *http.Context) {
	offerType := t.GetRequestIntD("offer_type", 0)
	switch offerType {
	case libp.ConstOfferTypeNo:
	case libp.ConstOfferTypeFirstBuy:
	case libp.ConstOfferTypeTrial:
	case libp.ConstOfferTypeTrialFirstBuy:
	default:
		t.Result(errorcode.InvalidParams)
		return
	}
	key := libp.ConstOfferTypeConfKey[offerType]
	var result AliPaySigningCopyWriting
	info := dbclient.TbConfig.GetItemByKey(key)
	result.Conf = make([]CopyWritingRop, 0)
	result.OfferType = offerType
	if info != nil {
		if err := json.Unmarshal([]byte(info.Value), &result); err != nil {
			t.Result(result)
			return
		}
	}
	t.Result(result)
}

type PrivacyProtocolInfo struct {
	Version   string `json:"version"`
	Timestamp int64  `json:"timestamp"`
}

func (*aclient) PrivacyProtocolSwitchSave(t *http.Context) {
	params, err := io.ReadAll(t.Request.Body)
	if err != nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	key := client.PrivacyProtocolSwitch
	err = SaveConfig(key, "隐私协议开关", string(params))
	if err != nil {
		t.Result(errorcode.DBError)
		return
	}
	configPrivacy := &PrivacyProtocolInfo{}
	_ = json.Unmarshal(params, configPrivacy)
	t.Result(configPrivacy)
}

func (*aclient) PrivacyProtocolSwitchInfo(t *http.Context) {
	info := dbclient.TbConfig.GetItemByKey(client.PrivacyProtocolSwitch)
	privacyProtocolSwitch := &PrivacyProtocolInfo{}
	if info != nil {
		err := json.Unmarshal([]byte(info.Value), privacyProtocolSwitch)
		if err != nil {
			t.Result(errorcode.SystemError)
			return
		}
	}
	t.Result(privacyProtocolSwitch)
}
