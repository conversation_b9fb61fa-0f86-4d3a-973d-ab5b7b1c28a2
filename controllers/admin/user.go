package admin

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"mime/multipart"
	"net/url"
	"strconv"
	"strings"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/gokit/middlewares/auth"
	safelygo "gitlab.dailyyoga.com.cn/gokit/safely-go"
	"gitlab.dailyyoga.com.cn/protogen/children-go/children"
	dbadmin "gitlab.dailyyoga.com.cn/server/children-admin-api/databases/admin"
	dbuser "gitlab.dailyyoga.com.cn/server/children-admin-api/databases/user"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/grpc"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
	libproduct "gitlab.dailyyoga.com.cn/server/children-admin-api/library/product"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/user"
	srvadmin "gitlab.dailyyoga.com.cn/server/children-admin-api/service/admin"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/service/cache"
)

type adminuser struct {
}

var AdminUser adminuser

type loginData struct {
	WelcomeMessage string    `json:"welcome"`
	User           loginUser `json:"user"`
	Token          string    `json:"token"`
	ExpireAt       int64     `json:"expireAt"`
}

type loginUser struct {
	Name   string `json:"name"`
	Avatar string `json:"avatar"`
}
type loginRequest struct {
	Name     string `json:"name"`
	Password string `json:"password"`
}

func (*adminuser) Login(t *http.Context) {
	req := &loginRequest{}
	t.ParseRequestStruct(req)
	if req.Name == "" || req.Password == "" {
		t.Result(-1, "账号密码输入不正确")
		return
	}
	data := &loginData{
		User: loginUser{},
	}

	account, err := dbadmin.TbAccount.CheckUser(req.Name, req.Password)
	if err != nil {
		t.Result(-1, "账号密码输入不正确")
		return
	}

	data.User = loginUser{Name: account.AdminName}

	// generate token
	tokenInfo := srvadmin.GenerateToken(account)
	if tokenInfo == nil {
		t.Result(-1, "账号密码输入不正确")
		return
	}
	data.Token = tokenInfo.Token
	data.ExpireAt = tokenInfo.ExpireAt
	t.Result(data)
}

type VipConfigEnum struct {
	OperateType  map[user.VipOperateType]string
	DurationType map[libproduct.DurationType]string
	ChangeReason map[user.VipChangeReason]string
	VipType      map[int]string
}

// VipConfig 会员加减枚举
func (*adminuser) VipConfig(t *http.Context) {
	res := &VipConfigEnum{
		DurationType: libproduct.DurationTypeDesAdminEnum,
		OperateType:  user.VipOperateTypeDesc,
		ChangeReason: user.VipChangeReasonDesc,
		VipType:      libproduct.ProductVipTypeDesc,
	}
	t.Result(res)
}

// VipChange 会员加减
func (*adminuser) VipChange(t *http.Context) {
	uidStr := t.GetRequestStringD("uid_str", "")
	operateType := t.GetRequestInt32D("operate_type", 0)
	durationType := t.GetRequestInt32D("duration_type", 0)
	durationValue := t.GetRequestInt32D("duration_value", 0)
	changeReason := t.GetRequestInt32D("change_reason", 0)
	desc := t.GetRequestStringD("desc", "")
	adminName := t.GetHeader(library.HeaderOperatorName)
	adminName, _ = url.QueryUnescape(adminName)
	uidStrList := strings.Split(uidStr, ",")
	fClient := grpc.GetchildrenClient()
	if durationValue > MaxDurationValue {
		t.Result(errorcode.SystemError, "请正确填写权益值（最大值10000）")
		return
	}
	viptypeArr := "1"
	for _, v := range strings.Split(viptypeArr, ",") {
		vipType, _ := strconv.ParseInt(v, 10, 32)
		if vipType == 0 {
			continue
		}
		_, err := fClient.ChangeVipEquity(context.Background(), &children.ChangeVipEquityReq{
			UIDStrList:       uidStr,
			OperateType:      operateType,
			DurationType:     durationType,
			DurationValue:    durationValue,
			ChangeReasonType: changeReason,
			Remark:           desc,
			AdminName:        adminName,
			VipType:          int32(vipType),
		})
		if err != nil {
			logger.Error(err.Error())
		}
	}
	res := &VipChangeRes{
		IsProcessed: 0,
	}
	if len(uidStrList) <= MaxSyncUserID {
		res.IsProcessed = 1
	}
	t.Result(res)
}

type VipChangeRes struct {
	IsProcessed int `json:"is_processed"`
}

// 同步处理最多100个 超过100异步处理
const MaxSyncUserID = 100

const MaxDurationValue = 10000

// VipAdminList 后台会员操作记录
func (*adminuser) VipAdminList(t *http.Context) {
	uid := t.GetRequestInt64D("uid", 0)
	page := t.GetRequestIntD("page", 1)
	pageSize := t.GetRequestIntD("page_size", 20) //nolint
	list := dbuser.TbAORecord.GetList(uid, page, pageSize)
	for i := range list {
		if list[i].VipType != 0 {
			continue
		}
		list[i].VipType = libproduct.ProductVipTypeVIP
	}
	if len(list) == 0 {
		t.Result(make([]*dbuser.AdminOperateVipRecord, 0))
		return
	}
	t.Result(list)
}

type UIDListRsp struct {
	UIDList string `json:"uid_list"`
}

// LoadUidList 加载文件中的uid列表
func (*adminuser) LoadUIDList(t *http.Context) {
	r := t.Request
	var file multipart.File
	file, _, err := r.FormFile("file")
	if err != nil {
		logger.Error(err)
		t.Result(errorcode.SystemError)
		return
	}
	if file == nil {
		logger.Error("文件为空")
		t.Result(errorcode.SystemError)
		return
	}
	defer func() {
		err = file.Close()
	}()
	info, err := io.ReadAll(file)
	if err != nil {
		t.Result(errorcode.SystemError)
		return
	}
	csvInfo := strings.ReplaceAll(string(info), "\ufeff", "")
	uidList := strings.Split(csvInfo, "\r\n")
	uidIntList := make([]string, 0)
	for _, v := range uidList {
		u, err := strconv.ParseInt(v, 10, 64)
		if err != nil || u <= 0 {
			continue
		}
		uidIntList = append(uidIntList, v)
	}
	logger.Info("info", uidList)
	t.Result(UIDListRsp{UIDList: strings.Join(uidIntList, ",")})
}

func (*adminuser) ModuleInfo(t *http.Context) {
	t.Result(map[string]interface{}{
		"module_list": srvadmin.AdminModuleTypeDesc,
		"operate_list": map[auth.AdminOperateType]string{
			srvadmin.OperateTypeEnum.View: "查看",
			srvadmin.OperateTypeEnum.Edit: "编辑",
		},
	})
}

type LogoffAccountReq struct {
	UID int64 `json:"uid"`
}

// LogoffAccount 注销账号
func (*adminuser) LogoffAccount(t *http.Context) {
	params := &LogoffAccountReq{}
	body := library.GetBody(t)
	if err := json.Unmarshal(body, params); err != nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	if params.UID == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	account := dbuser.TbAccount.GetUserByID(params.UID)
	if account == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	thirdList := dbuser.TbAccountThirdAuth.GetThirdBindInfoByUID(params.UID)
	account.IsLogoff = library.Yes
	err := account.Update()
	if err != nil {
		t.Result(errorcode.InvalidParams, err.Error())
		return
	}
	for _, v := range thirdList {
		v.IsBind = library.No
		err = v.Update()
		if err != nil {
			t.Result(errorcode.InvalidParams, err.Error())
			return
		}
	}
	safelygo.GoSafelyByTraceID(func() {
		UIDBindSid(account.ID)
	})
	t.Result(library.EmptyResponse{})
}

type LogoffMobileReq struct {
	Mobile string `json:"mobile"`
	UID    int64  `json:"id"`
}

func UIDBindSid(uid int64) {
	rdc := cache.GetCRedis().GetClient()
	dataKey := fmt.Sprintf("%s%d", user.UserOnlineCacheKeyPrefix, uid)
	ctx := context.Background()
	resultArr, err := rdc.LRange(ctx, dataKey, 0, user.UIDBindSidLen).Result()
	if err != nil {
		logger.Warn(err)
		return
	}
	for _, v := range resultArr {
		cacheKey := fmt.Sprintf("%s%s", user.LoginSIDCacheKeyPrefix, v)
		if _, err := rdc.Del(context.Background(), cacheKey).Result(); err == nil {
			logger.Warn(err)
		}
	}
}
