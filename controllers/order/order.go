package order

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
)

type adminorder struct {
}

var AdminOrder adminorder

const LogoffUserRefundDurationDesc = "已注销无法扣减"

type SearchOrderItem struct {
	ID                  int64   `json:"id"`
	UID                 int64   `json:"uid"`
	Mobile              string  `json:"mobile"`
	OrderID             string  `json:"order_id"`
	CreateTime          int64   `json:"create_time"`
	PayTime             int64   `json:"pay_time"`
	ProductName         string  `json:"product_name"`
	ProductID           int64   `json:"product_id"`
	PayType             int     `json:"pay_type"`
	Amount              string  `json:"amount"`
	IsSubscribe         int     `json:"is_subscribe"`
	RefundAmount        float64 `json:"refund_amount"`
	ReasonType          int     `json:"reason_type"`
	Reason              string  `json:"reason"`
	SuccessRefundAmount float64 `json:"success_refund_amount"`
	RefundTime          int64   `json:"refund_time"`
	RefundDurationDesc  string  `json:"refund_duration_desc"`
	OrderRefundStatus   int     `json:"order_refund_status"`
	AdminName           string  `json:"admin_name"`
	RefundID            int64   `json:"refund_id"`
	RefundCreateTime    int64   `json:"refund_create_time"`
	ThirdFailMsg        string  `json:"third_fail_msg"`
	RetryTimes          int     `json:"retry_times"`
	RefundSource        int     `json:"refund_source"`
}

type SearchOrderRsp struct {
	List []*SearchOrderItem `json:"list"`
}

func (*adminorder) Search(t *http.Context) {
	t.Result(SearchOrderRsp{List: []*SearchOrderItem{}})
}
