package top

import (
	"time"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	gdb "gitlab.dailyyoga.com.cn/server/children-admin-api/databases/group"
	db "gitlab.dailyyoga.com.cn/server/children-admin-api/databases/top"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
	liboperate "gitlab.dailyyoga.com.cn/server/children-admin-api/library/operate"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/service/util"
)

type operate struct {
}

var AOperate operate

type OperateItemAdmin struct {
	InfoImg        string `json:"info_img"`
	UserGroupType  int32  `json:"user_group_type"`
	UserGroupID    int64  `json:"user_group_id"`
	LinkType       int32  `json:"link_type"`
	LinkContent    string `json:"link_content"`
	LinkContentIOS string `json:"link_content_ios"`
	IsIndexOpen    int32  `json:"is_index_open"`
	StartTime      int64  `json:"start_time"`
	EndTime        int64  `json:"end_time"`
	Sort           int32  `json:"sort"`
	Title          string `json:"title"`
}

type operateConfigInfo struct {
	Link          map[library.LinkTypeInt]string      `json:"link"`
	IsOnline      map[int]string                      `json:"isOnline"`
	TimeStatus    map[int]string                      `json:"time_status"`
	UserGroupType map[library.UserGroupTypeInt]string `json:"user_group_type"`
}

// Config 获取配置
func (*operate) Config(t *http.Context) {
	t.Result(&operateConfigInfo{
		Link: map[library.LinkTypeInt]string{
			library.LinkTypeEnum.Payment:    library.LinkMap[library.LinkTypeEnum.Payment],
			library.LinkTypeEnum.Course:     library.LinkMap[library.LinkTypeEnum.Course],
			library.LinkTypeEnum.CoursePlay: library.LinkMap[library.LinkTypeEnum.CoursePlay],
		},
		TimeStatus:    liboperate.TimeStatus,
		UserGroupType: library.UserGroupTypeMap,
		IsOnline: map[int]string{
			library.Yes: "是",
			library.No:  "否",
		},
	})
}

func (*operate) NewInfo(t *http.Context) {
	params := &OperateItemAdmin{}
	t.ParseRequestStruct(params)
	if (params.StartTime == 0 || params.EndTime == 0) || (params.StartTime > params.EndTime) {
		t.Result(errorcode.InvalidParams)
		return
	}
	item := &db.Operate{
		UserGroupType:  params.UserGroupType,
		UserGroupID:    params.UserGroupID,
		LinkType:       params.LinkType,
		LinkContent:    params.LinkContent,
		LinkContentIOS: params.LinkContentIOS,
		Sort:           params.Sort,
		StartTime:      params.StartTime,
		EndTime:        params.EndTime,
		InfoImg:        util.FormatImageInfoStr(params.InfoImg),
		IsDel:          library.No,
		IsOnline:       library.No,
		IsIndexOpen:    params.IsIndexOpen,
		Title:          params.Title,
	}
	if params.IsIndexOpen == 0 {
		item.IsIndexOpen = library.No
	}
	if err := item.Save(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

func (*operate) UpdateInfo(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	if id < 1 {
		t.Result(errorcode.InvalidParams)
		return
	}
	item := db.TbTopOperate.GetItemByID(id)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	params := &OperateItemAdmin{}
	t.ParseRequestStruct(params)
	if (params.StartTime == 0 || params.EndTime == 0) || (params.StartTime > params.EndTime) {
		t.Result(errorcode.InvalidParams)
		return
	}
	item.UserGroupType = params.UserGroupType
	item.UserGroupID = params.UserGroupID
	item.LinkType = params.LinkType
	item.LinkContent = params.LinkContent
	item.LinkContentIOS = params.LinkContentIOS
	item.Sort = params.Sort
	item.StartTime = params.StartTime
	item.EndTime = params.EndTime
	item.IsIndexOpen = params.IsIndexOpen
	item.InfoImg = util.FormatImageInfoStr(params.InfoImg)
	item.Title = params.Title
	if params.IsIndexOpen == 0 {
		item.IsIndexOpen = library.No
	}
	if err := item.UpdateMustCols(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

type OperateList struct {
	List []*OperateItem `json:"list"`
}
type OperateItem struct {
	ID             int64  `json:"id"`
	Title          string `json:"title"`
	InfoImg        string `json:"info_img"`
	UserGroupType  int32  `json:"user_group_type"`
	UserGroupID    int64  `json:"user_group_id"`
	UserGroupName  string `json:"user_group_name"`
	LinkType       int32  `json:"link_type"`
	LinkContent    string `json:"link_content"`
	LinkContentIOS string `json:"link_content_ios"`
	IsIndexOpen    int32  `json:"is_index_open"`
	StartTime      int64  `json:"start_time"`
	EndTime        int64  `json:"end_time"`
	Sort           int32  `json:"sort"`
	IsOnline       int32  `json:"is_online"`
	TimeStatusDesc string `json:"time_status_desc"`
	TimeStatus     int    `json:"time_status"`
}

func (*operate) ListInfo(t *http.Context) {
	page := t.GetRequestIntD("page", 1)
	pageSize := t.GetRequestIntD("page_size", 20) //nolint
	timeStatus := t.GetRequestIntD("time_status", -1)
	userGroupID := t.GetRequestIntD("user_group_id", -1)
	list := db.TbTopOperate.GetListByPageTStatus(timeStatus, page, pageSize, userGroupID)
	res := &OperateList{}
	if len(list) == 0 {
		t.Result(res)
		return
	}
	for _, v := range list {
		res.List = append(res.List, formatItem(v))
	}
	t.Result(res)
}
func formatItem(item *db.Operate) *OperateItem {
	groupName := "全部用户"
	if item.UserGroupID > 0 {
		group := gdb.TbUserGroupRuleConfig.GetDetail(item.UserGroupID)
		if group != nil {
			groupName = group.GroupName
		}
	}
	now := time.Now().Unix()
	timeStatusDesc := liboperate.TimeStatus[liboperate.NotStarted]
	timeStatus := liboperate.NotStarted
	if item.EndTime > now && item.StartTime < now {
		timeStatusDesc = liboperate.TimeStatus[liboperate.HaveInHand]
		timeStatus = liboperate.HaveInHand
	} else if item.EndTime < now {
		timeStatusDesc = liboperate.TimeStatus[liboperate.Closed]
		timeStatus = liboperate.Closed
	}

	op := &OperateItem{
		ID:             item.ID,
		Title:          item.Title,
		InfoImg:        util.UnmarshalImageStr(item.InfoImg).URL,
		UserGroupType:  item.UserGroupType,
		UserGroupID:    item.UserGroupID,
		UserGroupName:  groupName,
		LinkType:       item.LinkType,
		LinkContent:    item.LinkContent,
		LinkContentIOS: item.LinkContentIOS,
		StartTime:      item.StartTime,
		EndTime:        item.EndTime,
		Sort:           item.Sort,
		IsOnline:       item.IsOnline,
		IsIndexOpen:    item.IsIndexOpen,
		TimeStatusDesc: timeStatusDesc,
		TimeStatus:     timeStatus,
	}
	return op
}
func (*operate) DeleteInfo(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	if id < 1 {
		t.Result(errorcode.InvalidParams)
		return
	}
	item := db.TbTopOperate.GetItemByID(id)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	item.IsDel = library.Yes
	if err := item.Update(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

func (*operate) OnlineInfo(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	isOnline := t.GetRequestInt32D("is_online", 1)
	if id < 1 {
		t.Result(errorcode.InvalidParams)
		return
	}
	item := db.TbTopOperate.GetItemByID(id)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	item.IsOnline = isOnline
	if err := item.Update(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

func (*operate) Info(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	if id < 1 {
		t.Result(errorcode.InvalidParams)
		return
	}
	item := db.TbTopOperate.GetItemByID(id)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	res := formatItem(item)
	t.Result(res)
}
