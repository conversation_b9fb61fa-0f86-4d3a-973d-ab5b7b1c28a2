package ob

import (
	"encoding/json"
	"errors"
	"sort"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/controllers/config"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/databases/conf"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/service/util"
)

type process struct {
}

var AdminProcess process

type ProcessResourceItem struct {
	ID               int64  `json:"id"`
	Name             string `json:"name"`
	PageList         string `json:"page_list"`
	TransitionVideos string `json:"transition_videos"`
	CreateTime       int64  `json:"create_time"`
	UpdateTime       int64  `json:"update_time"`
	PlanPageID       int64  `json:"plan_page_id"`
	PlanPageName     string `json:"plan_page_name"`
	ObType           int    `json:"ob_type"`
}

func (*process) FmtObProcessResource(resource *conf.ObProcessResource) *ProcessResourceItem {
	res := &ProcessResourceItem{
		ID:               resource.ID,
		Name:             resource.Name,
		PageList:         resource.PageList,
		TransitionVideos: resource.TransitionVideos,
		CreateTime:       resource.CreateTime,
		UpdateTime:       resource.UpdateTime,
		PlanPageID:       resource.PlanPageID,
		ObType:           resource.ObType,
	}
	if resource.PlanPageID != 0 {
		planPage := conf.TbPlanPage.GetItemByID(resource.PlanPageID)
		if planPage != nil {
			res.PlanPageName = planPage.PlanPageName
		}
	}
	return res
}

// nolint
func (*process) Config(t *http.Context) {
	obType := t.GetRequestInt64D("ob_type", 0)
	var (
		res              PageListConfigRsp
		obProcessList    []PageItem
		transitionVideos []PageItem
	)
	isCheck := true
	if obType == config.ObTypePlan || obType == config.ObTypekegel {
		isCheck = false
	}
	for _, v := range config.ImageMap {
		if obType == config.ObTypeStartApp && v.ID == config.BodyInfo {
			continue
		}
		if obType != config.ObTypePlan && obType != config.ObTypekegel && util.ContainsElement(config.ObkegelIDs, v.Key) {
			continue
		}
		if obType == config.ObTypekegel && util.ContainsElement(config.ObkegelIDs, v.Key) {
			isCheck = true
		}
		if obType == config.ObTypeStartApp && v.Key == config.PurposeKegel {
			continue
		}
		if v.Key == config.BodyCurrent || v.Key == config.BodyTarget {
			continue
		}
		if (obType == config.ObTypeStartApp || obType == config.ObTypePlan) && (v.Key == config.Purpose || v.Key == config.PurposeAll) {
			continue
		}
		item := PageItem{
			ID:      v.ID,
			Key:     v.Key,
			Img:     v.Img,
			IsCheck: isCheck,
		}
		obProcessList = append(obProcessList, item)
	}
	for _, v := range config.TransitionVideos {
		item := PageItem{
			ID:      v.ID,
			Key:     v.Key,
			Img:     v.Img,
			IsCheck: isCheck,
		}
		transitionVideos = append(transitionVideos, item)
	}
	// 按照ID排序
	sort.Slice(obProcessList, func(i, j int) bool {
		return obProcessList[i].ID < obProcessList[j].ID
	})
	res.ObProcessList = obProcessList
	res.TransitionVideos = transitionVideos
	t.Result(res)
}

func (*process) Update(t *http.Context) {
	params := &ProcessUpdateReq{}
	body := library.GetBody(t)
	if err := json.Unmarshal(body, params); err != nil {
		t.Result(errorcode.InvalidParams, err.Error())
		return
	}
	if err := AdminProcess.ValidPages(params.PageList, params.TransitionVideos); err != nil {
		t.Result(errorcode.InvalidParams, err.Error())
		return
	}
	pageListStr, err := json.Marshal(params.PageList)
	if err != nil {
		t.Result(errorcode.InvalidParams, err.Error())
		return
	}
	if params.TransitionVideos == nil {
		params.TransitionVideos = make([]PageItem, 0)
	}
	transitionVideoStr, err := json.Marshal(params.TransitionVideos)
	if err != nil {
		t.Result(errorcode.InvalidParams, err.Error())
		return
	}
	pageInfo := conf.TbObProcessResource.GetItemByID(int64(params.ID))
	if pageInfo == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	pageInfo.Name = params.Name
	pageInfo.PageList = string(pageListStr)
	pageInfo.TransitionVideos = string(transitionVideoStr)
	pageInfo.PlanPageID = params.PlanPageID
	if err = pageInfo.Update(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

func (*process) Create(t *http.Context) {
	params := &ProcessAddReq{}
	body := library.GetBody(t)
	if err := json.Unmarshal(body, params); err != nil {
		t.Result(errorcode.InvalidParams, err.Error())
		return
	}
	if err := AdminProcess.ValidPages(params.PageList, params.TransitionVideos); err != nil {
		t.Result(errorcode.InvalidParams, err.Error())
		return
	}
	pageListStr, err := json.Marshal(params.PageList)
	if err != nil {
		t.Result(errorcode.InvalidParams, err.Error())
		return
	}
	if params.TransitionVideos == nil {
		params.TransitionVideos = make([]PageItem, 0)
	}
	transitionVideoStr, err := json.Marshal(params.TransitionVideos)
	if err != nil {
		t.Result(errorcode.InvalidParams, err.Error())
		return
	}
	item := &conf.ObProcessResource{
		Name:             params.Name,
		PageList:         string(pageListStr),
		TransitionVideos: string(transitionVideoStr),
		IsDel:            library.No,
		PlanPageID:       params.PlanPageID,
		ObType:           params.ObType,
	}
	if err := item.Save(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

// nolint
func (p *process) Detail(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	info := conf.TbObProcessResource.GetItemByID(id)
	if info == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	var (
		res              PageDetailRsp
		pageListTmp      []PageItem
		pageList         []PageItem
		transitionVideos []PageItem
	)
	err := json.Unmarshal([]byte(info.PageList), &pageListTmp)
	if err != nil {
		t.Result(errorcode.SystemError)
		return
	}
	transitionVideos = make([]PageItem, 0)
	if info.TransitionVideos != "" {
		err = json.Unmarshal([]byte(info.TransitionVideos), &transitionVideos)
		if err != nil {
			t.Result(errorcode.SystemError)
			return
		}
	}
	for i := range pageListTmp {
		if info.ObType == config.ObTypeStartApp && pageListTmp[i].Key == config.PurposeKegel {
			continue
		}
		if info.ObType == config.ObTypeStartApp && pageListTmp[i].ID == config.BodyInfo {
			continue
		}
		if info.ObType != config.ObTypePlan && info.ObType != config.ObTypekegel && util.ContainsElement(config.ObkegelIDs, pageListTmp[i].Key) {
			continue
		}
		if pageListTmp[i].Key == "resultApparatus" {
			pageListTmp[i].Img = "https://fitnessimg.dailyworkout.cn/image/7eac045c7d6a60e70bd01026033e0193xayl.png"
		}
		pageList = append(pageList, pageListTmp[i])
	}
	// 兼容旧配置
	pageMap := make(map[string]PageItem)
	for i := range pageList {
		pageMap[pageList[i].Key] = pageList[i]
	}
	transVideoMap := make(map[string]PageItem)
	for i := range transitionVideos {
		transVideoMap[transitionVideos[i].Key] = transitionVideos[i]
	}
	for i := range config.ImageMap {
		if info.ObType == config.ObTypeStartApp && config.ImageMap[i].ID == config.BodyInfo {
			continue
		}
		if info.ObType == config.ObTypeStartApp && config.ImageMap[i].Key == config.PurposeKegel {
			continue
		}
		if info.ObType != config.ObTypePlan && info.ObType != config.ObTypekegel && util.ContainsElement(config.ObkegelIDs, config.ImageMap[i].Key) {
			continue
		}
		if _, ok := pageMap[config.ImageMap[i].Key]; !ok {
			item := PageItem{ID: config.ImageMap[i].ID, Img: config.ImageMap[i].Img, Key: config.ImageMap[i].Key}
			pageList = append(pageList, item)
		}
	}
	for i := range config.TransitionVideos {
		if _, ok := transVideoMap[config.TransitionVideos[i].Key]; !ok {
			item := PageItem{ID: config.TransitionVideos[i].ID, Img: config.TransitionVideos[i].Img,
				Key: config.TransitionVideos[i].Key}
			transitionVideos = append(transitionVideos, item)
		}
	}
	res.Name = info.Name
	res.PageList = pageList
	res.TransitionVideos = transitionVideos
	res.PlanPageID = info.PlanPageID
	res.ObType = info.ObType
	if res.PlanPageID != 0 {
		planPage := conf.TbPlanPage.GetItemByID(info.PlanPageID)
		if planPage != nil {
			res.PlanPageName = planPage.PlanPageName
		}
	}
	t.Result(res)
}

func (*process) List(t *http.Context) {
	page := t.GetRequestIntD("page", 1)
	pageSize := t.GetRequestIntD("page_size", config.PageDef)
	obType := t.GetRequestIntD("ob_type", 0)
	list := conf.TbObProcessResource.GetList(page, pageSize, obType)
	res := &ProcessListRsp{
		List: make([]*ProcessResourceItem, 0),
	}
	for i := range list {
		res.List = append(res.List, AdminProcess.FmtObProcessResource(list[i]))
	}
	if len(list) == 0 {
		t.Result(res)
		return
	}
	t.Result(res)
}

func (*process) Delete(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	pageInfo := conf.TbObProcessResource.GetItemByID(id)
	if pageInfo == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	pageInfo.IsDel = library.Yes
	if err := pageInfo.Update(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

func (*process) ValidPages(pages, transitionVideos []PageItem) error {
	countTransPages := 0
	for i := range pages {
		if util.ContainsElement(config.TransitionPageIDs, pages[i].ID) && pages[i].IsCheck {
			countTransPages++
		}
	}
	pagesMap := make(map[string]PageItem)
	for _, v := range pages {
		pagesMap[v.Key] = v
	}
	transitionVideo, ok := pagesMap["transitionVideo"]
	if !ok || (ok && !transitionVideo.IsCheck) {
		for i := range transitionVideos {
			transitionVideos[i].IsCheck = false
		}
	}
	if countTransPages > 1 {
		return errors.New("生成页面只能选择一个")
	}
	return nil
}

type PlanPageAdmin struct {
	ID           int64              `json:"id"`
	PlanPageName string             `json:"plan_page_name"`
	Content      []*PlanPageContent `json:"content"`
	BgColor      string             `json:"bg_color"`
	CreateTime   int64              `json:"create_time"`
}

type PlanPageContent struct {
	PlanPageType           int64              `json:"plan_page_type"`
	BannerMale             *library.ImageInfo `json:"banner_male"`
	BannerFemale           *library.ImageInfo `json:"banner_female"`
	ImageMale              *library.ImageInfo `json:"img_male"`
	ImageFemale            *library.ImageInfo `json:"img_female"`
	BottomImg              *library.ImageInfo `json:"bottom_img"`
	BottomImgPad           *library.ImageInfo `json:"bottom_img_pad"`
	SwiperImg              *SwiperInfo        `json:"swiper_img"`
	CuttingImageMale       library.ImageInfo  `json:"cutting_img_male"`
	CuttingImageFemale     library.ImageInfo  `json:"cutting_img_female"`
	MusclesImageMale       library.ImageInfo  `json:"muscles_img_male"`
	MusclesImageFemale     library.ImageInfo  `json:"muscles_img_female"`
	StayHealthyImageMale   library.ImageInfo  `json:"stay_healthy_img_male"`
	StayHealthyImageFemale library.ImageInfo  `json:"stay_healthy_img_female"`
	KegelLossImg           *library.ImageInfo `json:"kegel_loss_img"`
	KegelShapingImg        *library.ImageInfo `json:"kegel_shaping_img"`
	KegelHealthyImg        *library.ImageInfo `json:"kegel_healthy_img"`
	KegelDefaultImg        *library.ImageInfo `json:"kegel_default_img"`
	KegelGym               *library.ImageInfo `json:"kegel_gym"`
	Gym                    *library.ImageInfo `json:"gym"`
}

type SwiperInfo struct {
	TitleImg string               `json:"title_img"`
	Images   []*library.ImageInfo `json:"images"`
}

func marshalPlanPageContent(list []*PlanPageContent) string {
	dByte, err := json.Marshal(list)
	if err != nil {
		logger.Error(err)
		return ""
	}
	return string(dByte)
}

func formatPlanPageContent(item *conf.PlanPage) *PlanPageAdmin {
	res := &PlanPageAdmin{
		ID:           item.ID,
		PlanPageName: item.PlanPageName,
		BgColor:      item.BgColor,
		CreateTime:   item.CreateTime,
	}
	if item.Content != "" {
		_ = json.Unmarshal([]byte(item.Content), &res.Content)
	}
	return res
}

func (*process) CreatePlanPage(t *http.Context) {
	req := &PlanPageAdmin{}
	t.ParseRequestStruct(req)
	item := &conf.PlanPage{}
	if req.ID > 0 {
		item = conf.TbPlanPage.GetItemByID(req.ID)
	}
	// 验证首尾
	for i := range req.Content {
		if i == 0 && req.Content[0].PlanPageType != config.PlanPageTopBanner { // 顶部banner
			t.Result(errorcode.InvalidParams, "顶部banner必须在最上面")
			return
		}
		if i == len(req.Content)-1 && req.Content[len(req.Content)-1].PlanPageType != config.PlanPageSwiper { // 吸底
			t.Result(errorcode.InvalidParams, "吸底按钮必须在最下面")
			return
		}
		if req.Content[i].PlanPageType == config.PlanPageKegelPracticeSug &&
			(req.Content[i].KegelDefaultImg == nil || req.Content[i].KegelDefaultImg.URL == "") { // 吸底
			t.Result(errorcode.InvalidParams, "凯格尔练习建议图片不能为空")
			return
		}
	}

	item.PlanPageName = req.PlanPageName
	item.Content = marshalPlanPageContent(req.Content)
	item.BgColor = req.BgColor
	if item.ID > 0 {
		if err := item.UpdateMustCol(); err != nil {
			t.Result(errorcode.SystemError, err.Error())
			return
		}
	} else {
		item.Status = library.No
		if err := item.Save(); err != nil {
			t.Result(errorcode.SystemError, err.Error())
			return
		}
	}
	t.Result(library.EmptyResponse{})
}

func (*process) EditPlanPage(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	item := conf.TbPlanPage.GetItemByID(id)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	t.Result(formatPlanPageContent(item))
}

func (*process) DeletePlanPage(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	item := conf.TbPlanPage.GetItemByID(id)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	item.Status = library.Yes
	if err := item.UpdateMustCol(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}

func (*process) PlanPageList(t *http.Context) {
	page := t.GetRequestIntD("page", 0)
	pageSize := t.GetRequestIntD("page_size", 0)
	name := t.GetRequestStringD("plan_page_name", "")
	list := conf.TbPlanPage.GetList(page, pageSize, name)
	res := make([]*PlanPageAdmin, 0)
	for _, v := range list {
		res = append(res, formatPlanPageContent(v))
	}
	t.Result(res)
}

func (*process) CopyPlanPage(t *http.Context) {
	req := &PlanPageAdmin{}
	t.ParseRequestStruct(req)
	item := conf.TbPlanPage.GetItemByID(req.ID)
	if item == nil {
		t.Result(errorcode.InvalidParams)
		return
	}
	item.ID = 0
	item.PlanPageName = "复制_" + item.PlanPageName
	if err := item.Save(); err != nil {
		t.Result(errorcode.SystemError, err.Error())
		return
	}
	t.Result(library.EmptyResponse{})
}
