package ob

type PageItem struct {
	ID      int    `json:"id"`
	Img     string `json:"img"`
	Key     string `json:"key"`
	<PERSON><PERSON><PERSON><PERSON> bool   `json:"is_check"`
}

type PageListConfigRsp struct {
	ObProcessList    []PageItem `json:"ob_process_list"`
	TransitionVideos []PageItem `json:"transition_videos"`
}

type PageDetailRsp struct {
	Name             string     `json:"name"`
	PageList         []PageItem `json:"page_list"`
	TransitionVideos []PageItem `json:"transition_videos"`
	PlanPageID       int64      `json:"plan_page_id"`
	PlanPageName     string     `json:"plan_page_name"`
	ObType           int        `json:"ob_type"`
}

type ProcessListRsp struct {
	List []*ProcessResourceItem `json:"list"`
}

type ProcessAddReq struct {
	Name             string     `json:"name"`
	PageList         []PageItem `json:"page_list"`
	TransitionVideos []PageItem `json:"transition_videos"`
	PlanPageID       int64      `json:"plan_page_id"`
	ObType           int        `json:"ob_type"`
}

type ProcessUpdateReq struct {
	ID               int        `json:"id"`
	Name             string     `json:"name"`
	PageList         []PageItem `json:"page_list"`
	TransitionVideos []PageItem `json:"transition_videos"`
	PlanPageID       int64      `json:"plan_page_id"`
}
