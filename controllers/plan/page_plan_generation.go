package plan

import (
	"encoding/json"
	"fmt"
	"strconv"
	"strings"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
	dbplan "gitlab.dailyyoga.com.cn/server/children-admin-api/databases/plan"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/client"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/service/util"
)

type planGeneration struct {
}

var AdminPlanGeneration planGeneration

type GenReq struct {
	ID              int64  `json:"id"`
	ImageInfo       string `json:"image_info"`
	BackgroundColor string `json:"background_color"`
	Title           string `json:"title"`
}

type GenResp struct {
	ID              int64  `json:"id"`
	ImageInfo       []*Gen `json:"image_info"`
	BackgroundColor string `json:"background_color"`
	Title           string `json:"title"`
}

type Gen struct {
	Sort        int    `json:"sort"`
	ImageMale   string `json:"image_male"`
	ImageFemale string `json:"image_female"`
	MaleType    int    `json:"male_type"`
	FemaleType  int    `json:"female_type"`
	FemaleScope int    `json:"female_scope"`
	MaleScope   int    `json:"male_scope"`
}

func (*planGeneration) SaveInfo(t *http.Context) {
	req := &GenReq{}
	t.ParseRequestStruct(req)
	planGenList := make([]Gen, 0)
	if err := json.Unmarshal([]byte(t.PostForm("image_info")), &planGenList); err != nil {
		t.Result(errorcode.SystemError, err.Error())
		return
	}
	if len(planGenList) == 0 {
		t.Result(errorcode.InvalidParams)
		return
	}
	for k := range planGenList {
		if planGenList[k].FemaleType == library.FemaleTypeVideo {
			planGenList[k].ImageFemale = util.FormatVideoInfoStr(
				strings.ReplaceAll(planGenList[k].ImageFemale, client.QiniuCDNResourceDomain, client.QiniuResourceDomain))
			planGenList[k].ImageFemale = strings.ReplaceAll(planGenList[k].ImageFemale, client.QiniuResourceDomain, client.QiniuCDNResourceDomain)
		} else {
			planGenList[k].ImageFemale = util.FormatImageInfoStr(strings.ReplaceAll(planGenList[k].ImageFemale,
				client.QiniuCDNResourceDomain, client.QiniuResourceDomain))
			planGenList[k].ImageFemale = strings.ReplaceAll(planGenList[k].ImageFemale, client.QiniuResourceDomain, client.QiniuCDNResourceDomain)
		}
		if planGenList[k].MaleType == library.FemaleTypeVideo {
			planGenList[k].ImageMale = util.FormatVideoInfoStr(
				strings.ReplaceAll(planGenList[k].ImageMale, client.QiniuCDNResourceDomain, client.QiniuResourceDomain))
			planGenList[k].ImageMale = strings.ReplaceAll(planGenList[k].ImageMale, client.QiniuResourceDomain, client.QiniuCDNResourceDomain)
		} else {
			planGenList[k].ImageMale = util.FormatImageInfoStr(strings.ReplaceAll(planGenList[k].ImageMale,
				client.QiniuCDNResourceDomain, client.QiniuResourceDomain))
			planGenList[k].ImageMale = strings.ReplaceAll(planGenList[k].ImageMale, client.QiniuResourceDomain, client.QiniuCDNResourceDomain)
		}
	}
	generationByte, err := json.Marshal(planGenList)
	if err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	req.ID, _ = strconv.ParseInt(t.PostForm("id"), 10, 64)
	planGene := dbplan.PagePlanGeneration{
		ID:              req.ID,
		ImageInfo:       string(generationByte),
		BackgroundColor: t.PostForm("background_color"),
		IsDel:           library.No,
		Title:           t.PostForm("title"),
	}
	if req.ID == 0 {
		if err := planGene.Save(); err != nil {
			t.Result(errorcode.DBError, err.Error())
			return
		}
	} else {
		if err := planGene.Update(); err != nil {
			t.Result(errorcode.DBError, err.Error())
			return
		}
	}
	t.Result(errorcode.Success)
}

func (*planGeneration) List(t *http.Context) {
	planGenList := make([]*GenResp, 0)
	for _, v := range dbplan.TbPlanPageGeneration.GetList() {
		planGenList = append(planGenList, formatPlanGeneration(v))
	}
	t.Result(planGenList)
}

func (*planGeneration) Info(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	t.Result(formatPlanGeneration(dbplan.TbPlanPageGeneration.GetByID(id)))
}

func (*planGeneration) Del(t *http.Context) {
	idPrm := &library.IDPrm{}
	t.ParseRequestStruct(idPrm)
	if idPrm.ID == library.Yes {
		t.Result(errorcode.InvalidParams, "兜底数据不能删除")
		return
	}
	info := dbplan.TbPlanPageGeneration.GetByID(idPrm.ID)
	info.IsDel = library.Yes
	if err := info.Update(); err != nil {
		t.Result(errorcode.DBError, err.Error())
		return
	}
	t.Result(errorcode.Success)
}

func formatPlanGeneration(generation *dbplan.PagePlanGeneration) *GenResp {
	if generation == nil {
		return nil
	}
	planGenList := make([]*Gen, 0)
	if err := json.Unmarshal([]byte(generation.ImageInfo), &planGenList); err != nil {
		fmt.Println(err.Error())
		logger.Error(err)
		return nil
	}
	for k := range planGenList {
		planGenList[k].ImageMale = util.UnmarshalImageStr(planGenList[k].ImageMale).URL
		planGenList[k].ImageFemale = util.UnmarshalImageStr(planGenList[k].ImageFemale).URL
	}
	return &GenResp{
		ID:              generation.ID,
		ImageInfo:       planGenList,
		BackgroundColor: generation.BackgroundColor,
		Title:           generation.Title,
	}
}
