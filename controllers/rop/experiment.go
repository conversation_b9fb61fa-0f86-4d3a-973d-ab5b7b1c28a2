package rop

import (
	"encoding/json"
	"strconv"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"

	pb "gitlab.dailyyoga.com.cn/protogen/children-rop-go/children-rop"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/grpc"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
	srvrop "gitlab.dailyyoga.com.cn/server/children-admin-api/service/rop"
)

type experiment struct {
}

var RExperiment experiment

func (*experiment) Create(t *http.Context) {
	name := t.GetRequestStringD("name", "")
	description := t.GetRequestStringD("description", "")
	bizID := t.GetRequestStringD("biz_id", "")
	client := grpc.GetchildrenRopClient()
	resp, err := client.NewExperiment(t.Context, &pb.NewExperimentRequest{
		Name:        name,
		BizID:       bizID,
		Description: description,
	})
	if err != nil {
		logger.Error("请求失败", err)
		t.Result(errorcode.SystemError, "系统错误")
		return
	}
	if !resp.GetOK() {
		t.Result(errorcode.SystemError, "实验创建失败，请检查参数后重试!")
	}
	t.Result(library.EmptyResponse{})
}

// PercentUpdate 实验流量分配接口
func (*experiment) PercentUpdate(t *http.Context) {
	params := t.GetRequestStringD("experiment_percent", "")
	if params == "" {
		t.Result(errorcode.SystemError, "实验流量不能为空")
		return
	}

	var data = make(map[string]int32)
	if err := json.Unmarshal([]byte(params), &data); err != nil {
		t.Result(errorcode.SystemError, "数据解析失败")
		return
	}
	var sum int32
	for _, v := range data {
		sum += v
		if v == 0 {
			t.Result(errorcode.SystemError, "实验比例配置不能为0")
			return
		}
	}
	if sum > 100 || sum <= 0 {
		t.Result(errorcode.SystemError, "实验比例配置错误")
		return
	}
	client := grpc.GetchildrenRopClient()
	for key, val := range data {
		eID, _ := strconv.ParseInt(key, 10, 64)
		resp, err := client.ModifyExperiment(t.Context, &pb.ModifyExperimentRequest{
			ExperimentID: eID,
			Percent:      val,
		})
		if err != nil {
			logger.Error("修改版本失败", resp.GetError())
			return
		}
	}
	t.Result(library.EmptyResponse{})
}

// ChangeStatus 修改实验
func (*experiment) ChangeStatus(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	flag := t.GetRequestInt64D("flag", 0)
	if flag < 1 || id < 0 {
		t.Result(errorcode.SystemError, "实验配置错误")
		return
	}
	eFlag := pb.ModifyExperimentRequest_ExperimentOFF
	if flag == int64(pb.ModifyExperimentRequest_ExperimentON) {
		eFlag = pb.ModifyExperimentRequest_ExperimentON
	}
	client := grpc.GetchildrenRopClient()
	resp, err := client.ModifyExperiment(t.Context, &pb.ModifyExperimentRequest{
		ExperimentID: id,
		Switch:       eFlag,
	})
	if err != nil {
		logger.Error("请求失败", err)
		t.Result(errorcode.SystemError, "系统错误")
		return
	}
	if !resp.GetOK() {
		t.Result(errorcode.SystemError, "实验修改失败，请检查参数后重试!")
	}
	t.Result(library.EmptyResponse{})
}

func (*experiment) Del(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	if id < 0 {
		t.Result(errorcode.SystemError, "版本ID不能为空")
		return
	}
	client := grpc.GetchildrenRopClient()
	resp, err := client.ModifyExperiment(t.Context, &pb.ModifyExperimentRequest{
		ExperimentID: id,
		Removed:      true,
	})
	if err != nil {
		logger.Error("请求失败", err)
		t.Result(errorcode.SystemError, "系统错误")
		return
	}
	if !resp.GetOK() {
		t.Result(errorcode.SystemError, "实验修改失败，请检查参数后重试!")
	}
	t.Result(library.EmptyResponse{})
}

func (*experiment) Info(t *http.Context) {
	bizID := t.GetRequestInt64D("biz_id", 0)
	id := t.GetRequestInt64D("id", 0)
	if id < 0 || bizID == 0 {
		t.Result(errorcode.SystemError, "数据错误")
		return
	}
	experiments := srvrop.GetExperimentDetail(bizID, id)
	t.Result(experiments)
}
