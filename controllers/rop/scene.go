package rop

import (
	"encoding/json"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"

	pb "gitlab.dailyyoga.com.cn/protogen/children-rop-go/children-rop"

	"gitlab.dailyyoga.com.cn/server/children-admin-api/grpc"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
	srvrop "gitlab.dailyyoga.com.cn/server/children-admin-api/service/rop"
)

type scene struct {
}

var RScene scene

type FetchScenesResponse struct {
	List []*FetchScenes `json:"list"`
}

type FetchScenes struct {
	ID           int64    `json:"id"`
	Name         string   `json:"name"`
	Description  string   `json:"description"`
	OwningModule string   `json:"owning_module"`
	Params       []Params `json:"params"`
	SceneID      int64    `json:"scene_id"`
}
type Params struct {
	GoalID int    `json:"goal_id"`
	Title  string `json:"title"`
}

// GetConfigList 创建场景需要的配置
func (*scene) GetConfigList(t *http.Context) {
	client := grpc.GetchildrenRopClient()
	resp, err := client.FetchScenes(t.Context, &pb.FetchScenesRequest{
		Active: true,
	})
	if err != nil || !resp.GetResult().GetOK() {
		logger.Error("请求失败", err)
		t.Result(errorcode.SystemError, "系统错误")
		return
	}
	res := &FetchScenesResponse{
		List: make([]*FetchScenes, 0),
	}
	for _, v := range resp.GetScenes() {
		fc := &FetchScenes{
			ID:           v.GetID(),
			Name:         v.GetName(),
			Description:  v.GetDescription(),
			OwningModule: v.GetOwningModule(),
			SceneID:      v.GetID(),
		}

		param := srvrop.GetSourceList(v.GetOwningModule())
		if len(param) > 0 {
			for _, p := range param {
				fc.Params = append(fc.Params, Params{
					GoalID: p.GoalID,
					Title:  p.Title,
				})
			}
		}
		res.List = append(res.List, fc)
	}
	t.Result(res)
}

type CreateSceneParams struct {
}

// CreateScene 创建场景
func (*scene) CreateScene(t *http.Context) {
	name := t.GetRequestStringD("name", "")
	description := t.GetRequestStringD("description", "")
	sceneID := t.GetRequestInt64D("scene_id", 0)
	shuntType := t.GetRequestInt32D("shunt_type", 0)
	userGroupID := t.GetRequestInt64D("user_group_id", 0)
	startTime := t.GetRequestInt64D("start_time", 0)
	endTime := t.GetRequestInt64D("end_time", 0)
	params := t.GetRequestStringD("params", "")
	if name == "" {
		t.Result(errorcode.InvalidParams, "无效产品ID")
		return
	}
	if sceneID == 0 {
		t.Result(errorcode.InvalidParams, "业务ID不能为空")
		return
	}
	if endTime != 0 && endTime <= startTime {
		t.Result(errorcode.InvalidParams, "结束时间不能小于等于开始时间")
		return
	}
	var m = make(map[string]string)
	if params != "" {
		if err := json.Unmarshal([]byte(params), &m); err != nil {
			t.Result(errorcode.InvalidParams, "params 参数解析错误")
			return
		}
	} else {
		m["common_target"] = "0"
	}
	pbShuntType := pb.SceneShuntType_All
	if shuntType == int32(pb.SceneShuntType_UserGroup) {
		pbShuntType = pb.SceneShuntType_UserGroup
	}

	client := grpc.GetchildrenRopClient()
	resp, err := client.NewSceneBiz(t.Context, &pb.NewSceneBizRequest{
		SceneID:     sceneID,
		BizParams:   m,
		ShuntType:   pbShuntType,
		UserGroupID: userGroupID,
		Name:        name,
		Description: description,
		StartTime:   startTime,
		EndTime:     endTime,
	})
	if err != nil {
		logger.Error("请求失败", err)
		t.Result(errorcode.SystemError, "系统错误")
		return
	}
	if !resp.GetOK() {
		t.Result(errorcode.SystemError, resp.GetError())
		return
	}
	t.Result(library.EmptyResponse{})
}

// DeleteSceneBiz 场景删除
func (*scene) DeleteSceneBiz(t *http.Context) {
	BizID := t.GetRequestStringD("biz_id", "")
	if BizID == "" {
		t.Result(errorcode.InvalidParams, "无效的biz_id")
		return
	}
	client := grpc.GetchildrenRopClient()
	resp, err := client.ModifySceneBiz(t.Context, &pb.ModifySceneBizRequest{
		BizID:   BizID,
		Removed: true,
	})
	if err != nil {
		logger.Error("请求失败", err)
		t.Result(errorcode.SystemError, "系统错误")
		return
	}
	if !resp.GetOK() {
		t.Result(errorcode.SystemError, "场景删除失败，请检查参数后重试!")
	}
	t.Result(library.EmptyResponse{})
}

// GetBizList 场景列表
func (*scene) GetBizList(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	sceneID := t.GetRequestInt64D("scene_id", 0)
	pageNum := t.GetRequestInt32D("page", 1)
	pageSize := t.GetRequestInt32D("page_size", 50) //nolint
	name := t.GetRequestStringD("name", "")
	data := srvrop.GetSceneList(&pb.FetchSceneBizsRequest{
		ID:       id,
		SceneID:  sceneID,
		PageSize: pageSize,
		PageNum:  pageNum,
		Name:     name,
	})
	t.Result(data)
}

// GetBizDetail 场景详情
func (*scene) GetBizDetail(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	client := grpc.GetchildrenRopClient()
	resp, err := client.FetchSceneBizs(t.Context, &pb.FetchSceneBizsRequest{ID: id})
	if err != nil {
		logger.Error("请求失败", err)
		t.Result(errorcode.SystemError, "系统错误")
		return
	}
	if !resp.GetResult().GetOK() || len(resp.GetScenesBizs()) < 1 {
		t.Result(library.EmptyResponse{})
		return
	}
	data := srvrop.GetBizDetail(&pb.FetchSceneBizsRequest{
		ID: id,
	})
	t.Result(data)
}

func (*scene) UpdateBizTime(t *http.Context) {
	id := t.GetRequestInt64D("id", 0)
	startTime := t.GetRequestInt64D("start_time", 0)
	endTime := t.GetRequestInt64D("end_time", 0)
	client := grpc.GetchildrenRopClient()
	resp, err := client.ModifySceneTime(t.Context, &pb.ModifySceneTimeRequest{
		ID:        id,
		StartTime: startTime,
		EndTime:   endTime,
	})
	if err != nil {
		logger.Error("请求失败", err)
		t.Result(errorcode.SystemError, "系统错误")
		return
	}
	if !resp.GetOK() {
		t.Result(errorcode.SystemError, "修改时间失败，请检查参数后重试!")
		return
	}
	t.Result(library.EmptyResponse{})
}
