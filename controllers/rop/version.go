package rop

import (
	"encoding/json"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"

	pb "gitlab.dailyyoga.com.cn/protogen/children-rop-go/children-rop"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/grpc"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library/errorcode"
)

type version struct {
}

var RVersion version

func (*version) Create(t *http.Context) {
	bizID := t.GetRequestInt64D("biz_id", 0)
	experimentID := t.GetRequestInt64D("experiment_id", 0)
	name := t.GetRequestStringD("name", "")
	description := t.GetRequestStringD("description", "")
	config := t.GetRequestStringD("config", "")

	configMap := make(map[string]string)
	if config != "" {
		err := json.Unmarshal([]byte(config), &configMap)
		if err != nil {
			logger.Error("请求失败", err)
			t.Result(errorcode.SystemError, "系统错误")
			return
		}
	}
	client := grpc.GetchildrenRopClient()
	resp, err := client.NewVersion(t.Context, &pb.NewVersionRequest{
		Name:         name,
		Description:  description,
		ExperimentID: experimentID,
		Config:       configMap,
		BizID:        bizID,
	})
	if err != nil {
		logger.Error("请求失败", err)
		t.Result(errorcode.SystemError, "系统错误")
		return
	}
	if !resp.GetOK() {
		t.Result(errorcode.SystemError, resp.GetError())
		return
	}
	t.Result(library.EmptyResponse{})
}

// PercentUpdate 实验流量分配接口
func (*version) PercentUpdate(t *http.Context) {
	params := t.GetRequestStringD("version_percent", "")
	if params == "" {
		t.Result(errorcode.SystemError, "版本流量不能为空")
		return
	}

	var data = make(map[string]int32)
	if err := json.Unmarshal([]byte(params), &data); err != nil {
		t.Result(errorcode.SystemError, "数据解析失败")
		return
	}
	var sum int32
	for _, v := range data {
		if v == 0 {
			t.Result(errorcode.SystemError, "比例不能为0")
			return
		}
		sum += v
	}
	if sum > 100 || sum <= 0 {
		t.Result(errorcode.SystemError, "实验比例配置错误")
		return
	}
	client := grpc.GetchildrenRopClient()
	for key, val := range data {
		_, err := client.ModifyVersion(t.Context, &pb.ModifyVersionRequest{
			VersionID: key,
			Percent:   val,
		})
		if err != nil {
			logger.Error("修改版本失败", err)
			return
		}
	}
	t.Result(library.EmptyResponse{})
}

func (*version) Del(t *http.Context) {
	id := t.GetRequestStringD("id", "")
	if id == "" {
		t.Result(errorcode.SystemError, "版本ID不能为空")
		return
	}
	client := grpc.GetchildrenRopClient()
	resp, err := client.ModifyVersion(t.Context, &pb.ModifyVersionRequest{
		VersionID: id,
		Removed:   true,
	})
	if err != nil {
		logger.Error("请求失败", err)
		t.Result(errorcode.SystemError, "系统错误")
		return
	}
	if !resp.GetOK() {
		t.Result(errorcode.SystemError, "版本修改失败，请检查参数后重试!")
		return
	}
	t.Result(library.EmptyResponse{})
}

func (*version) Update(t *http.Context) {
	id := t.GetRequestStringD("id", "")
	name := t.GetRequestStringD("name", "")
	description := t.GetRequestStringD("description", "")
	config := t.GetRequestStringD("config", "")
	if id == "" {
		t.Result(errorcode.SystemError, "版本ID不能为空")
		return
	}
	configMap := make(map[string]string)
	if config != "" {
		err := json.Unmarshal([]byte(config), &configMap)
		if err != nil {
			logger.Error("请求失败", err)
			t.Result(errorcode.SystemError, "系统错误")
			return
		}
	}
	client := grpc.GetchildrenRopClient()
	resp, err := client.ModifyVersion(t.Context, &pb.ModifyVersionRequest{
		VersionID:   id,
		Name:        name,
		Description: description,
		Config:      configMap,
	})
	if err != nil {
		logger.Error("请求失败", err)
		t.Result(errorcode.SystemError, "系统错误")
		return
	}
	if !resp.GetOK() {
		t.Result(errorcode.SystemError, "版本修改失败，请检查参数后重试!")
		return
	}
	t.Result(library.EmptyResponse{})
}
