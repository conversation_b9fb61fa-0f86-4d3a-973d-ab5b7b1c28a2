package grpc

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/grpc"
	message "gitlab.dailyyoga.com.cn/protogen/srv-message-go"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/config"
)

var messageClient message.MessageClient

func GetMessageClient() message.MessageClient {
	return messageClient
}

func initMessage(ms *microservice.Microservice) error {
	serviceName := "srv-message"
	cfgEnv := config.Get().Service.Env
	if cfgEnv == microservice.Mirror {
		serviceName = "srv-message-mirro"
	}

	cc, err := grpc.NewClient(ms, serviceName)
	if err != nil {
		return err
	}
	messageClient = message.NewMessageClient(cc)
	return nil
}
