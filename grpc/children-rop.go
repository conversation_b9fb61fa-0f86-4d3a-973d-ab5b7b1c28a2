package grpc

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/grpc"
	rop "gitlab.dailyyoga.com.cn/protogen/children-rop-go/children-rop"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/config"
)

var ropchildrenClient rop.ChildrenRopClient

func GetchildrenRopClient() rop.ChildrenRopClient {
	return ropchildrenClient
}

func initchildrenRop(ms *microservice.Microservice) error {
	cfgEnv := config.Get().Service.Env
	serviceName := "children-rop"
	if cfgEnv == microservice.Mirror {
		serviceName = "children-rop-mirro"
	}

	cc, err := grpc.NewClient(ms, serviceName)
	if err != nil {
		return err
	}
	ropchildrenClient = rop.NewChildrenRopClient(cc)
	return nil
}
