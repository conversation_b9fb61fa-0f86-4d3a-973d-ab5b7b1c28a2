package grpc

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/grpc"
	"gitlab.dailyyoga.com.cn/protogen/children-go/children"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/config"
)

var childrenClient children.ChildrenClient

func GetchildrenClient() children.ChildrenClient {
	return childrenClient
}

func initchildren(ms *microservice.Microservice) error {
	cfgEnv := config.Get().Service.Env
	serviceName := "children"
	if cfgEnv == microservice.Mirror {
		serviceName = "children-mirror"
	}

	cc, err := grpc.NewClient(ms, serviceName)
	if err != nil {
		return err
	}
	childrenClient = children.NewChildrenClient(cc)
	return nil
}
