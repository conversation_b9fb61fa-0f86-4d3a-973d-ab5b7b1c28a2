package grpc

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/gokit/microservice/logger"
)

func Starter(ms *microservice.Microservice) {
	if err := initchildrenRop(ms); err != nil {
		logger.Error("initchildrenRop error", err)
	}
	if err := initchildrenGroup(ms); err != nil {
		logger.Error("initchildrenGroup error", err)
	}
	if err := initchildren(ms); err != nil {
		logger.Error("initchildren error", err)
	}
	if err := initMessage(ms); err != nil {
		logger.Error("initMessage error", err)
	}
}
