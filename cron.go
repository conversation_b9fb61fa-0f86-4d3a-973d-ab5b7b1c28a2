package main

import (
	"gitlab.dailyyoga.com.cn/gokit/microservice"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/config"
	"gitlab.dailyyoga.com.cn/server/children-admin-api/service/util"
)

// nolint
func cronHandle(s *util.Server) {
	if config.Get().Service.Env == microservice.Mirror {
		// Mirror 规定不允许跑定时任务
		return
	}
	// 同步课程索引
}

func CronStarter(ms *microservice.Microservice) {
	cronServer := util.NewCronServer()
	cronHandle(cronServer)
	cronServer.Start()
}
