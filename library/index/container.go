package index

import "gitlab.dailyyoga.com.cn/server/children-admin-api/library/course"

type ContainerTypeInt int

var ContainerTypeEnum = struct {
	HorizontalCourse     ContainerTypeInt
	VerticalCourse       ContainerTypeInt
	BillboardCourse      ContainerTypeInt
	CustomCard           ContainerTypeInt
	GuessLike            ContainerTypeInt
	RecentPractice       ContainerTypeInt
	CourseCollect        ContainerTypeInt
	VideoStreaming       ContainerTypeInt
	CourseClassification ContainerTypeInt
	SessionFilter        ContainerTypeInt
}{
	HorizontalCourse:     1,
	VerticalCourse:       2,
	BillboardCourse:      3,
	CustomCard:           4,
	GuessLike:            5,
	RecentPractice:       6,
	CourseCollect:        7,
	VideoStreaming:       8,
	CourseClassification: 9,
	SessionFilter:        10,
}

var ContainerTypeDesc = map[ContainerTypeInt]string{
	ContainerTypeEnum.HorizontalCourse:     "横滑课程组",
	ContainerTypeEnum.VerticalCourse:       "纵向课程组",
	ContainerTypeEnum.BillboardCourse:      "榜单课程组",
	ContainerTypeEnum.CustomCard:           "自定义卡片",
	ContainerTypeEnum.GuessLike:            "猜你喜欢",
	ContainerTypeEnum.RecentPractice:       "最近练习",
	ContainerTypeEnum.CourseCollect:        "我的课程收藏",
	ContainerTypeEnum.VideoStreaming:       "视频流",
	ContainerTypeEnum.CourseClassification: "课程分类",
	ContainerTypeEnum.SessionFilter:        "二级分类筛选",
}

const DefaultIndexGroup = 1
const DefaultIndexGroupHome = 2

const DefaultCourseCollect = 9
const DefaultGuessLike = 7
const DefaultRecentPractice = 8

const (
	ContainerCourse = 1
	ContainerPlan   = 2
)

var ContainerLabelList = []int{
	course.LabelCourseEffectPID, course.LabelCourseLevelPID, course.LabelCourseDurationPID, course.LabelCourseAgePID,
}
