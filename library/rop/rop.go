package rop

const (
	StatusDefault = iota // 默认
	StatusNormal         // 正常
	StatusDelete         // 停止
)

const (
	ShuntTypeAll       = iota // 全部
	ShuntTypeUserGroup        // 用户分群
)

var ShuntTypeMap = map[string]int{
	"All":       ShuntTypeAll,
	"UserGroup": ShuntTypeUserGroup,
}

const (
	OnBoardingPayment    = "payment_page"
	PlanGenerationPage   = "plan_generation_page"
	TallerOb             = "taller_ob"
	IndexFullScreenPopup = "index_full_screen_popup"
)

// version key
const (
	PaymentPageID        = "payment_page_id"
	PlanGenerationPageID = "plan_generation_page_id"
	TallerObSwitch       = "taller_ob_switch"
	ScreenPopupID        = "screen_popup_id"
)

const (
	SceneCateTypeGoal   = iota + 1 // 目标
	SceneCateTypeConfig            // 配置
)

type SourceItem struct {
	GoalID int    `json:"goal_id"`
	Title  string `json:"title"`
}

const (
	DeviceTypeAll = iota + 1
	DeviceTypeAndroid
	DeviceTypeIos
	DeviceTypeAndroidNOHUAWEI
	DeviceTypeAndroidHUAWEI
)

var SourceDevice = []*SourceItem{
	{
		GoalID: DeviceTypeAll,
		Title:  "全部",
	},
	{
		GoalID: DeviceTypeAndroid,
		Title:  "安卓全量",
	},
	{
		GoalID: DeviceTypeIos,
		Title:  "IOS全量",
	},
	{
		GoalID: DeviceTypeAndroidNOHUAWEI,
		Title:  "安卓非华为",
	},
	{
		GoalID: DeviceTypeAndroidHUAWEI,
		Title:  "安卓华为",
	},
}

const VipOrder = 3

const (
	ChannelPaymentPageScenesID        = 1 // ob付费页面
	ChannelPlanGenerationPageScenesID = 2 // 计划生成页面
	TallerObScenesID                  = 3 // 长高ob
	ScreenPopupScenesID               = 4 // 全屏弹窗
)
