package course

import "gitlab.dailyyoga.com.cn/server/children-admin-api/library"

const (
	// 身体部位标签父ID
	LabelBodyPartPID = 1
	// 老师标签父ID
	LabelCoachPID = 13
	// 课程功效标签父ID
	LabelCourseEffectPID = 9
	// 课程类型
	LabelCourseTypePID = 15
	// 课程难度
	LabelCourseLevelPID = 20
	// 课程时长
	LabelCourseDurationPID = 24
	// 课程年龄
	LabelCourseAgePID = 29
)

type ResourceTypeInt int

var ResourceTypeEnum = struct {
	Action ResourceTypeInt
	Course ResourceTypeInt
}{
	Action: 1,
	Course: 2,
}

const (
	SearchResourceCourse = iota + 1
	SearchResourcePlan
)

type LevelInt int

var LevelEnum = struct {
	Primary LevelInt
	Middle  LevelInt
	Senior  LevelInt
}{
	Primary: 1,
	Middle:  2,
	Senior:  3,
}

var LevelEnumDesc = map[string]LevelInt{
	"L1": LevelEnum.Primary,
	"L2": LevelEnum.Middle,
	"L3": LevelEnum.Senior,
}

var EnumByDBIDLevel = map[LevelInt]int{
	LevelEnum.Primary: 21,
	LevelEnum.Middle:  22,
	LevelEnum.Senior:  23,
}
var EnumByLevelDBID = map[int]LevelInt{
	21: LevelEnum.Primary,
	22: LevelEnum.Middle,
	23: LevelEnum.Senior,
}

var LevelToCalorieRatio = map[LevelInt]float64{
	LevelEnum.Primary: 0.075,
	LevelEnum.Middle:  0.09,
	LevelEnum.Senior:  0.105,
}

const CourseID = 430
const CourseLabel = 10

var HorizontalEnumDesc = map[int]string{
	library.Yes: "横屏",
	library.No:  "竖屏",
}

var HorizontalEnum = map[string]int{
	"横屏": library.Yes,
	"竖屏": library.No,
}

const (
	ValAddTypeNormal = iota + 1
	ValAddTypeKegel
)

var YesNoEnumDesc = map[int32]string{
	library.Yes: "是",
	library.No:  "否",
}

const SessionListNum = 28

type TypeInt int

// TypeEnum 课程类型枚举
var TypeEnum = struct {
	JumpRope         TypeInt // 跳绳
	Aerobics         TypeInt // 有氧操
	PhysicalTraining TypeInt
	Basketball       TypeInt
}{
	JumpRope:         1,
	Aerobics:         2,
	PhysicalTraining: 3,
	Basketball:       4,
}

// TypeEnumDesc 课程类型描述
var TypeEnumDesc = map[TypeInt]string{
	TypeEnum.JumpRope:         "跳绳",
	TypeEnum.Aerobics:         "有氧操",
	TypeEnum.PhysicalTraining: "体能训练",
	TypeEnum.Basketball:       "篮球",
}

var CourseTypeEnumDesc = map[string]TypeInt{
	"跳绳":   TypeEnum.JumpRope,
	"有氧操":  TypeEnum.Aerobics,
	"体能训练": TypeEnum.PhysicalTraining,
	"篮球":   TypeEnum.Basketball,
}
var TypeEnumDBID = map[TypeInt]int{
	TypeEnum.JumpRope:         16,
	TypeEnum.Aerobics:         17,
	TypeEnum.PhysicalTraining: 18,
	TypeEnum.Basketball:       19,
}
