package client

type GrayStatusInt int

var GrayStatusEnum = struct {
	Get      GrayStatusInt
	Refuse   GrayStatusInt
	Download GrayStatusInt
	Display  GrayStatusInt
	Use      GrayStatusInt
}{
	Get:      1,
	Refuse:   2,
	Download: 3,
	Display:  4,
	Use:      5,
}

var GrayStatusColumn = map[GrayStatusInt]string{
	GrayStatusEnum.Get:      "get_count",
	GrayStatusEnum.Refuse:   "refuse_count",
	GrayStatusEnum.Download: "download_count",
	GrayStatusEnum.Display:  "display_count",
	GrayStatusEnum.Use:      "use_count",
}
