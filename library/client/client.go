package client

const (
	ObPageRopObTypeNewKey     = "ob_page_rop_ob_type_new"
	ObPageRopObTypeChannelKey = "ob_page_rop_ob_type_channel"
	AuditInfoConfig           = "audit_info_config"
	CustomerServiceConfig     = "customer_service_config" // 客服配置在线开关
	ObSkipNumConfig           = "ob_skip_num_config"
	ComplianceConfig          = "compliance_config"
	AndroidAuditConfig        = "android_audit_config"
	// 巨量开关
	JuLiangSwitch = "ju_liang_switch"
	// 阶梯扣款配置
	ChargeStepSwitch = "ali_step_charge_config"
	// 应用商店好评配置
	ConfigKeyStoreParise = "store_praise_config"
	// 隐私协议
	PrivacyProtocolSwitch = "privacy_protocol_switch"
)

type DeviceTypeInt int

var DeviceTypeEnum = struct {
	Unknown DeviceTypeInt
	Android DeviceTypeInt
	IOS     DeviceTypeInt
	All     DeviceTypeInt
}{
	Unknown: 0,
	Android: 1,
	IOS:     2,
	All:     99,
}
var TerminalTypeDesc = map[DeviceTypeInt]string{
	DeviceTypeEnum.All:     "全部",
	DeviceTypeEnum.Android: "安卓",
	DeviceTypeEnum.IOS:     "IOS",
}

const (
	FlowAll                = 100
	ObPageRopConfLen       = 2
	ObPageRopObTypeNew     = 1
	ObPageRopObTypeChannel = 2
)

type FileType int

var FileTypeEnum = struct {
	Image FileType
	Video FileType
	Audio FileType
	Apk   FileType
}{
	Image: 1,
	Video: 2,
	Audio: 3,
	Apk:   4,
}

var FileTypeDir = map[FileType]string{
	FileTypeEnum.Image: "image",
	FileTypeEnum.Video: "video",
	FileTypeEnum.Audio: "audio",
	FileTypeEnum.Apk:   "apk",
}

const (
	QiniuCDNResourceDomain  = "https://cimage.childrenworkout.com.cn"
	QiniuResourceDomain     = "https://qnchildren.childrenworkout.com.cn"
	QiniuResourceBucket     = "dailychildren"
	QiniuTokenDefaultExpire = 1200
)

const (
	UserChanneDef = iota
	UserChannelJuLiang
	UserChannelHuawei
)

var ChannelMap = map[int]string{
	UserChanneDef:      "自然量",
	UserChannelJuLiang: "巨量",
	UserChannelHuawei:  "huawei",
}

var ChannelADIdenMap = map[int]string{
	UserChanneDef:      "【CS_小树苗运动】",
	UserChannelJuLiang: "【CS_巨量】",
	UserChannelHuawei:  "【CS_huawei】",
}

var ChannelMapping = map[string]string{
	"【CS_小树苗运动】":  "小树苗运动",
	"【CS_巨量】":     "巨量",
	"【CS_huawei】": "huawei",
}

const ConfigIdentityIP = "config_identity_ip"

const (
	UID = iota + 1
	DeviceID
)

var RangeUserClient = map[int]string{
	UID:      "UID",
	DeviceID: "设备ID",
}
