package group

type RuleRelationType int

var RuleRelationTypeEnum = struct {
	OR  RuleRelationType // 或
	AND RuleRelationType // 且
}{
	OR:  1,
	AND: 2,
}
var RuleRelationTypeDesc = map[RuleRelationType]string{
	RuleRelationTypeEnum.OR:  "或",
	RuleRelationTypeEnum.AND: "且",
}

type StatusRadioType int

var StatusRadioTypeEnum = struct {
	YES StatusRadioType // YES
	NO  StatusRadioType // NO
}{
	YES: 1,
	NO:  0,
}
var StatusRadioTypeDesc = map[StatusRadioType]string{
	StatusRadioTypeEnum.YES: "YES",
	StatusRadioTypeEnum.NO:  "NO",
}

var ConditionEnum = struct {
	GreaterOrEqual int32 // 大于等于
	Greater        int32 // 大于
	Equal          int32 // 等于
	Less           int32 // 小于
	LessOrEqual    int32 // 小于等于
	NotEqual       int32 // 不等于
}{
	GreaterOrEqual: 1,
	Greater:        2,
	Equal:          3,
	Less:           4,
	LessOrEqual:    5,
	NotEqual:       6,
}

var ConditionDesc = map[int32]string{
	ConditionEnum.GreaterOrEqual: "大于等于",
	ConditionEnum.Greater:        "大于",
	ConditionEnum.Equal:          "等于",
	ConditionEnum.Less:           "小于",
	ConditionEnum.LessOrEqual:    "小于等于",
	ConditionEnum.NotEqual:       "不等于",
}

const (
	LabelCategoryTypeBase = iota + 1
	LabelCategoryTypePlatform
	LabelCategoryTypeInterest
	LabelCategoryTypeEvaluation
	LabelCategoryTypeExclusive
	LabelCategoryTypeBehavior
	LabelCategoryTypeObAttributes
	LabelCategoryTypePersonalInfo
)

var LabelCategoryType = map[int]string{
	LabelCategoryTypeBase:         "基本属性",
	LabelCategoryTypePlatform:     "平台属性",
	LabelCategoryTypeInterest:     "兴趣属性",
	LabelCategoryTypeEvaluation:   "测评属性",
	LabelCategoryTypeExclusive:    "专属分群",
	LabelCategoryTypeBehavior:     "行为分群",
	LabelCategoryTypeObAttributes: "OB属性",
	LabelCategoryTypePersonalInfo: "个人信息",
}

var ConditionType = struct {
	Sedentary int
	Night     int
	Insomnia  int
	Other     int
}{
	Sedentary: 1,
	Night:     2,
	Insomnia:  3,
	Other:     4,
}

var GenderType = struct {
	Man   int
	Woman int
}{
	Woman: 1,
	Man:   2,
}

var GenderTypeDesc = map[int]string{
	GenderType.Man:   "男",
	GenderType.Woman: "女",
}

var CommonStatusRadioType = map[int]string{
	1: "YES",
	2: "NO",
}
