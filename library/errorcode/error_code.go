package errorcode

type ErrorCode int

const (
	Success           ErrorCode = 0
	InvalidParams     ErrorCode = 2 // 无效参数
	FailGetVercode    ErrorCode = 3 // 获取验证码失败
	FailSendVercode   ErrorCode = 4 // 发送验证码失败
	SystemError       ErrorCode = 5 // 系统错误
	DBError           ErrorCode = 6 // DB错误
	IsUseDeleteForbid ErrorCode = 100014
)

// ErrorCodeMessage 错误码对应提示表
var ErrorCodeMessage = map[interface{}]string{
	InvalidParams:     "无效参数",
	FailGetVercode:    "获取验证码失败",
	FailSendVercode:   "发送验证码失败",
	SystemError:       "系统错误",
	DBError:           "DB错误",
	IsUseDeleteForbid: "已被使用中，请勿删除",
}

const (
	ServiceResultSuccess = 1001
)
