package library

import (
	"io"

	"gitlab.dailyyoga.com.cn/gokit/microservice/http"
)

// 全局数据库布尔值公用是/否状态码
const (
	Yes = iota + 1
	No
)

type EmptyResponse struct {
}

// TimeZoneBeijing 北京时间时区
var TimeZoneBeijing = "Asia/Shanghai"

// AppClient app客户端公共参数
type AppClient struct {
	Version  string
	OsType   int
	Channel  int
	DeviceID string
}

type ImageInfo struct {
	URL    string `json:"url"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
}

type VideoInfo struct {
	URL    string `json:"url"`
	Width  int    `json:"width"`
	Height int    `json:"height"`
}

type LinkTypeInt int

var LinkTypeEnum = struct {
	Course     LinkTypeInt // 课程详情
	Payment    LinkTypeInt // 付费方案页
	InnerLink  LinkTypeInt // 内部链接
	Praise     LinkTypeInt // 好评弹窗
	CoursePlay LinkTypeInt // 课程播放
}{
	Course:     1,
	Payment:    2,
	InnerLink:  3,
	Praise:     4,
	CoursePlay: 5,
}

type Link struct {
	LinkType    LinkTypeInt `json:"link_type"`
	LinkTitle   string      `json:"link_title,omitempty"`
	LinkContent string      `json:"link_content,omitempty"`
}

var LinkMap = map[LinkTypeInt]string{
	LinkTypeEnum.Course:     "课程详情",
	LinkTypeEnum.Payment:    "付费方案页",
	LinkTypeEnum.InnerLink:  "内部链接",
	LinkTypeEnum.Praise:     "好评活动",
	LinkTypeEnum.CoursePlay: "课程播放",
}

const FenToYuan = 100

type UserGroupTypeInt int

var UserGroupTypeEnum = struct {
	All       UserGroupTypeInt
	GroupRule UserGroupTypeInt
}{
	All:       1,
	GroupRule: 2,
}

var UserGroupTypeMap = map[UserGroupTypeInt]string{
	UserGroupTypeEnum.All:       "全部用户",
	UserGroupTypeEnum.GroupRule: "用户分群",
}

const (
	ChannelHuawei        = "1600001"
	ChannelOPPO          = "1600002"
	ChannelVIVO          = "1600003"
	ChannelXiaomi        = "1600004"
	ChannelTencent       = "1600005"
	ChannelBaidu         = "1600006"
	Channel360           = "1600007"
	ChannelMeizu         = "1600008"
	ChannelLianxiang     = "1600009"
	ChannelPP            = "1600010"
	ChannnelSamsung      = "1600011"
	ChannelLittle        = "1600012"
	ChannelHonor         = "1600013"
	ChannelApple         = "1700001"
	ChannelWechatMiNiApp = "100-1"
	ChannelTiktok        = "100-2"
	ChannelHarmonyHuawei = "100-3"
	ChannelOther         = "-1"
)

const HeaderOperatorName = "Operatorname"

const MaxPageSize = 1000

const FlowAll = 100

func GetBody(t *http.Context) []byte {
	body, _ := io.ReadAll(t.Request.Body)
	return body
}

type ExportRsp struct {
	FileName string `json:"file_name"`
	FileData string `json:"file_data"`
}

var TimeTemplate = "2006-01-02 15:04:05"

type IDPrm struct {
	ID int64 `json:"id"`
}

const Const999 = 999

const (
	FemaleTypeVideo = 2
)

var YesNoEnumDesc = map[string]int{
	"是": Yes,
	"否": No,
}

// DeviceTypeToChannelMap 将前端传来的设备类型转换为渠道号
// 1 -> 安卓, 2 -> iOS, 3 -> 华为渠道号, 4 -> OPPO渠道号, 5 -> VIVO渠道号, 6 -> 小米渠道号, 7 -> 其他小渠道安卓渠道号
var DeviceTypeToChannelMap = map[string]string{
	"1": "1",           // 安卓 (保持原有)
	"2": "2",           // iOS (保持原有)
	"3": ChannelHuawei, // 华为
	"4": ChannelOPPO,   // OPPO
	"5": ChannelVIVO,   // VIVO
	"6": ChannelXiaomi, // 小米
	"7": ChannelOther,  // 其他小渠道 - 安卓
}

// ConvertDeviceTypeToChannel 将前端传来的设备类型转换为存储用的渠道号
func ConvertDeviceTypeToChannel(deviceType string) string {
	if channel, exists := DeviceTypeToChannelMap[deviceType]; exists {
		return channel
	}
	return deviceType // 如果没有映射，返回原值
}

// NameToChannelMap 好评资源池配置信息
var NameToChannelMap = map[string]string{
	ChannelApple:  "IOS",
	ChannelHuawei: "华为",
	ChannelOPPO:   "oppo",
	ChannelVIVO:   "vivo",
	ChannelXiaomi: "xiaomi",
	ChannelOther:  "其他小渠道",
}
