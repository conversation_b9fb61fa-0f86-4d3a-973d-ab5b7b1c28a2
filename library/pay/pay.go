package pay

const (
	PayTypeWechat = iota + 1
	PayTypeAlipay
	PayTypeApple
)

var PayTypeDesc = map[int]string{
	PayTypeWechat: "微信",
	PayTypeAlipay: "支付宝",
	PayTypeApple:  "IOS支付",
}

// PayTypeToSensor 因为神策和每日瑜伽共用，支付方式需要转换 避免和每日瑜伽冲突
var PayTypeToSensor = map[int]int{
	PayTypeAlipay: 101,
	PayTypeWechat: 100,
}

type OrderStatusInt int

var OrderStatus = struct {
	UnPay OrderStatusInt
	Paid  OrderStatusInt
}{
	UnPay: 0,
	Paid:  1,
}

type ContractChangeType int

// ContractChangeTypeEnum 签约状态 0等待签约 1 签约 2 解约
var ContractChangeTypeEnum = struct {
	ContractChangeTypeWait   ContractChangeType
	ContractChangeTypeAdd    ContractChangeType
	ContractChangeTypeDelete ContractChangeType
}{
	ContractChangeTypeWait:   0,
	ContractChangeTypeAdd:    1,
	ContractChangeTypeDelete: 2,
}

// AliContractToChangeType 支付宝支付签约状态和change_type互转
var AliContractToChangeType = map[string]ContractChangeType{
	"NORMAL": ContractChangeTypeEnum.ContractChangeTypeAdd,
	"UNSIGN": ContractChangeTypeEnum.ContractChangeTypeDelete,
}

var SubscribeStatusDesc = map[ContractChangeType]string{
	ContractChangeTypeEnum.ContractChangeTypeAdd:    "生效中",
	ContractChangeTypeEnum.ContractChangeTypeDelete: "已取消",
}

type SubscribeModeInt int

var SubscribeModeEnum = struct {
	PayAndSubscribe SubscribeModeInt // 支付中签约
	OnlySubscribe   SubscribeModeInt // 先签约后付款
}{
	PayAndSubscribe: 1,
	OnlySubscribe:   2,
}

type OrderSubscribeStatus int

var OrderSubscribeStatusEnum = struct {
	Wait    OrderSubscribeStatus
	Success OrderSubscribeStatus
	Not     OrderSubscribeStatus
}{
	Wait:    1,
	Success: 2,
	Not:     3,
}

// 当前线上使用的签约模式
const CurrentSubscribeMode = 2

// 支付相关appID和secret
const (
	// 微信支付相关信息
	WechatPayAppID     = "wx01b7b9eaf1e392b3"
	WechatPayMchID     = "1574912371"
	WechatPayAppSecret = "dancefitme1654141312rehanwudao22"
)

const AlipayComplaintNotify = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?" +
	"key=04347bc9-8d2f-4cfc-831d-8218ddc09eed"

type RefundStatusInt int

var RefundStatusEnum = struct {
	All      RefundStatusInt
	Paid     RefundStatusInt
	Wait     RefundStatusInt
	Refuse   RefundStatusInt
	Ing      RefundStatusInt
	Success  RefundStatusInt
	Fail     RefundStatusInt
	Retrieve RefundStatusInt
}{
	All:      -1,
	Paid:     0,
	Wait:     1,
	Refuse:   2,
	Ing:      3,
	Success:  4,
	Fail:     5,
	Retrieve: 8,
}

var RefundStatusDesc = map[RefundStatusInt]string{
	RefundStatusEnum.Paid:     "支付成功",
	RefundStatusEnum.Wait:     "待审核",
	RefundStatusEnum.Refuse:   "已拒绝",
	RefundStatusEnum.Ing:      "退费中",
	RefundStatusEnum.Success:  "退费成功",
	RefundStatusEnum.Fail:     "退费失败",
	RefundStatusEnum.Retrieve: "挽回成功",
}

type ReasonTypeInt int

var ReasonTypeEnum = struct {
	OB              ReasonTypeInt
	ActivityNo      ReasonTypeInt
	RepeatPay       ReasonTypeInt
	SystemSubscribe ReasonTypeInt
	Personal        ReasonTypeInt
	ChangeEquity    ReasonTypeInt
	Test            ReasonTypeInt
	Others          ReasonTypeInt
}{
	OB:              1,
	ActivityNo:      2,
	RepeatPay:       3,
	SystemSubscribe: 4,
	Personal:        5,
	ChangeEquity:    6,
	Test:            7,
	Others:          8,
}

var ReasonTypeDesc = map[ReasonTypeInt]string{
	ReasonTypeEnum.OB:              "OB流程",
	ReasonTypeEnum.ActivityNo:      "活动不认可",
	ReasonTypeEnum.RepeatPay:       "重复支付",
	ReasonTypeEnum.SystemSubscribe: "系统续订",
	ReasonTypeEnum.Personal:        "个人原因",
	ReasonTypeEnum.ChangeEquity:    "更换权益",
	ReasonTypeEnum.Test:            "测试",
	ReasonTypeEnum.Others:          "其他",
}

type RefundTypeInt int

// RefundTypeEnum 1 是全部退款 2 部分退款
var RefundTypeEnum = struct {
	Whole RefundTypeInt
	Part  RefundTypeInt
}{
	Whole: 1,
	Part:  2,
}

type CommunicateStatusInt int

var CommunicateStatusEnum = struct {
	Wait         CommunicateStatusInt
	SubmitRefund CommunicateStatusInt
	Ignore       CommunicateStatusInt
}{
	Wait:         1,
	SubmitRefund: 2,
	Ignore:       3,
}

var CommunicateStatusDesc = map[CommunicateStatusInt]string{
	CommunicateStatusEnum.Wait:         "未沟通",
	CommunicateStatusEnum.SubmitRefund: "已提交退款",
	CommunicateStatusEnum.Ignore:       "已忽略",
}

type DebitTypeInt int

var DebitTypeEnum = struct {
	System DebitTypeInt
	Manual DebitTypeInt
}{
	System: 1,
	Manual: 2,
}

var DebitTypeDesc = map[DebitTypeInt]string{
	DebitTypeEnum.System: "微信/支付宝订阅自动扣款",
	DebitTypeEnum.Manual: "微信/支付宝手动支付",
}

type CommonRefundsItem struct {
	RefundAmount        float64 `json:"refund_amount"`
	RefundDurationType  int     `json:"refund_duration_type"`
	RefundDurationValue int     `json:"refund_duration_value"`
}

type CancelTypeInt int

var CancelTypeEnum = struct {
	Normal CancelTypeInt
	Cancel CancelTypeInt
}{
	Normal: 1,
	Cancel: 2,
}

type RefundSource int

var RefundSourceEnum = struct {
	Unknown RefundSource
	Alipay  RefundSource
	Wechat  RefundSource
	Other   RefundSource
}{
	Unknown: 0,
	Alipay:  1,
	Wechat:  2,
	Other:   3,
}

var RefundSourceDesc = map[RefundSource]string{
	RefundSourceEnum.Alipay: "支付宝",
	RefundSourceEnum.Wechat: "微信",
	RefundSourceEnum.Other:  "其他",
}

const (
	VipCenterComplianceUserGroup = 999
	VipCenterUserGroupDef        = 0
)
const (
	SkuProductTypeVIP       = 1
	SkuProductTypeChallenge = 2
)

const (
	ObTypeOb = 1 + iota
	ObTypePlan
	ObTypeKegel
)
