package user

// 登录类型
const (
	LoginTypeVistor        = iota // 游客
	LoginTypePassword             // 账号密码登录
	LoginTypeMobileVercode        // 手机号验证码登录
	LoginTypeWechat               // 微信登录
	LoginTypeOneKey               // 极光一键登录
	LoginTypeApple                // 苹果登陆
	LoginTypeShenCe               // 神策distinctID登陆
)

var LoginTypeEnDesc = map[int]string{
	LoginTypePassword:      "telephone",
	LoginTypeOneKey:        "telephone_onekey",
	LoginTypeMobileVercode: "telephone_code",
	LoginTypeWechat:        "weixin",
	LoginTypeApple:         "ios",
	LoginTypeShenCe:        "sensorsdata",
}

func LoginTypeToEnDesc(loginType int) string {
	ed, ok := LoginTypeEnDesc[loginType]
	if !ok {
		return ""
	}
	return ed
}

const LoginSIDCacheKeyPrefix = "Cs:sid:"

const UIDBindSidLen = 20

// 用户在线数
const UserOnlineCacheKeyPrefix = "Cs:deviceOline:"
