package user

type VipSourceType int

// VipSourceTypeEnum 会员发放来源
var VipSourceTypeEnum = struct {
	System VipSourceType // 系统发放
	Admin  VipSourceType // 后台发放
	Gift   VipSourceType // 大礼包
}{
	System: 1,
	Admin:  2,
	Gift:   3,
}

type VipChangeReason int

// VipChangeReasonEnum 会员权益变更原因
var VipChangeReasonEnum = struct {
	Purchase       VipChangeReason
	Refund         VipChangeReason
	Activity       VipChangeReason
	TransferEquity VipChangeReason
	StorePraise    VipChangeReason
	Other          VipChangeReason
}{
	Purchase:       1,
	Refund:         2,
	Activity:       3,
	TransferEquity: 4,
	StorePraise:    5,
	Other:          999,
}
var VipChangeReasonDesc = map[VipChangeReason]string{
	VipChangeReasonEnum.Purchase: "购买",
	VipChangeReasonEnum.Refund:   "退款",
	VipChangeReasonEnum.Activity: "活动发放",
	VipChangeReasonEnum.Other:    "其他",
}

type VipOperateType int

var VipOperateTypeEnum = struct {
	Add VipOperateType
	Sub VipOperateType
}{
	Add: 1,
	Sub: 2,
}

var VipOperateTypeDesc = map[VipOperateType]string{
	VipOperateTypeEnum.Add: "加",
	VipOperateTypeEnum.Sub: "减",
}

type StorePraiseAuditInt int

var StorePraiseAuditEnum = struct {
	Wait   StorePraiseAuditInt
	Pass   StorePraiseAuditInt
	Refuse StorePraiseAuditInt
}{
	Wait:   1,
	Pass:   2,
	Refuse: 3,
}

const UpMobileNum = 3

var QiYUAccount = map[string]string{
	"dailyyoga1":                 "杨巧婵",
	"dailyyoga6":                 "秦婕",
	"dailyyoga17":                "康乐",
	"dailyyoga22":                "庞亚慧",
	"zhanghaifeng":               "张海风",
	"chenyu":                     "陈雨",
	"dailyyoga13":                "张晓妮",
	"dailyyoga16":                "杨晓雨",
	"dailyyoga11":                "王琳",
	"dailyyoga10":                "宋婉婷",
	"dailyyoga20":                "康雯",
	"chenwan":                    "陈婉",
	"chenbaolan":                 "陈宝兰",
	"songbingyan":                "宋秉艳",
	"gaopu":                      "高普",
	"zhangbei":                   "张贝",
	"renhan021":                  "杨巧婵",
	"qinjie":                     "秦婕",
	"rehan017":                   "康乐",
	"rehan022":                   "庞亚慧",
	"rehan013":                   "张晓妮",
	"rehan016":                   "杨晓雨",
	"rehan011":                   "王琳",
	"rehan010":                   "宋婉婷",
	"houyuyao":                   "侯雨瑶",
	"chenhui":                    "陈慧",
	"hankeru":                    "韩可如",
	"rehan020":                   "康雯",
	"jingtian03":                 "景甜",
	"lirongrong":                 "李蓉蓉",
	"shengliqin":                 "盛丽琴",
	"yangqiaochan":               "杨巧婵",
	"yinghan04":                  "康乐",
	"pangyahui":                  "庞亚慧",
	"yinghan07":                  "张晓妮",
	"yinghan08":                  "杨晓雨",
	"yinghan06":                  "王琳",
	"yinghan01":                  "宋婉婷",
	"yinghan03":                  "康雯",
	"ceshi":                      "王奕娇",
	"ceshi2":                     "测试2",
	"<EMAIL>":    "超级管理员",
	"<EMAIL>": "超级管理员",
}

type BigGiftSourceType int

// BigGiftSourceTypeEnum 大礼包发放来源
var BigGiftSourceTypeEnum = struct {
	Order BigGiftSourceType // 订单来源
}{
	Order: 1,
}
