package product

import (
	"gitlab.dailyyoga.com.cn/server/children-admin-api/library"
)

// 会员产品类型
const (
	ProductType1Month = iota + 1
	ProductType3Month
	ProcuctType12Month
)

var ProductTypeDesc = map[int]string{
	ProductType1Month:  "月度会员",
	ProductType3Month:  "季度会员",
	ProcuctType12Month: "年度会员",
}

var ProductTypeOnly = map[int]string{
	ProductType1Month:  "月度会员",
	ProductType3Month:  "季度会员",
	ProcuctType12Month: "年度会员",
}

var ProductVoiceType = map[int]string{
	ProductType1Month:  "月度会员",
	ProductType3Month:  "季度会员",
	ProcuctType12Month: "年度会员",
}

var ProductTypeAll = map[int]string{
	ProductType1Month:  "月度会员",
	ProductType3Month:  "季度会员",
	ProcuctType12Month: "年度会员",
}

var ProductPlanEqType = map[int]string{
	ProductType1Month:  "月度会员",
	ProductType3Month:  "季度会员",
	ProcuctType12Month: "年度会员",
}

var VipType = map[int]string{
	ProductType1Month:  "月度会员",
	ProductType3Month:  "季度会员",
	ProcuctType12Month: "年度会员",
}

type DurationType int

// DurationTypeEnum 会员产品的会员时间类型
var DurationTypeEnum = struct {
	Day   DurationType
	Month DurationType
	Year  DurationType
}{
	Day:   1,
	Month: 2,
	Year:  3,
}

var DurationTypeDesEnum = map[DurationType]string{
	DurationTypeEnum.Day:   "天",
	DurationTypeEnum.Month: "月",
	DurationTypeEnum.Year:  "年",
}

var DurationTypeDesAdminEnum = map[DurationType]string{
	DurationTypeEnum.Day:   "天",
	DurationTypeEnum.Month: "月",
}

var DurationTypeToDays = map[DurationType]int{
	DurationTypeEnum.Day:   1,
	DurationTypeEnum.Month: 30,
	DurationTypeEnum.Year:  365,
}

const (
	ConstOfferTypeNo = iota + 1
	ConstOfferTypeFirstBuy
	ConstOfferTypeTrial
	ConstOfferTypeTrialFirstBuy
)

var OfferTypeDesc = map[int]string{
	ConstOfferTypeNo:            "无",
	ConstOfferTypeFirstBuy:      "首购",
	ConstOfferTypeTrial:         "试用",
	ConstOfferTypeTrialFirstBuy: "试用+首购",
}

var ConstOfferTypeConfKey = map[int]string{
	ConstOfferTypeNo:            "offer_type_none",
	ConstOfferTypeFirstBuy:      "offer_type_first_offer",
	ConstOfferTypeTrial:         "offer_type_trial_offer",
	ConstOfferTypeTrialFirstBuy: "offer_type_first_trial_offer",
}

type FTProductTypeInt int

var FTProductTypeEnum = struct {
	MemberProduct FTProductTypeInt
}{
	MemberProduct: 1,
}

const (
	ProductVipTypeVIP = iota + 1
)

var SubscribeTypeDesc = map[int]string{
	library.Yes: "订阅",
	library.No:  "非订阅",
}

var ProductVipTypeDesc = map[int]string{
	ProductVipTypeVIP: "VIP",
}

var SubscribeProductTypeDesc = map[int]string{
	ProductVipTypeVIP: "会员",
}

var GiftProductTypeDesc = map[int]string{
	ProductVipTypeVIP: "会员",
}

type FitnessPurposeInt int

// FitnessPurposeEnum 健身目的
var FitnessPurposeEnum = struct {
	ReduceWeight FitnessPurposeInt
	Confident    FitnessPurposeInt
	Elegant      FitnessPurposeInt
	Health       FitnessPurposeInt
}{
	ReduceWeight: 1, // 强身健体
	Confident:    2, // 促进长高
	Elegant:      3, // 体态改善
	Health:       4, // 性格培养
}

var FitnessPurposeDesc = map[FitnessPurposeInt]string{
	FitnessPurposeEnum.ReduceWeight: "强身健体",
	FitnessPurposeEnum.Confident:    "促进长高",
	FitnessPurposeEnum.Elegant:      "体态改善",
	FitnessPurposeEnum.Health:       "性格培养",
}

type PracticePartInt int

// PracticePartEnum 练习部位
var PracticePartEnum = struct {
	Shoulder PracticePartInt
	Arm      PracticePartInt
	Chest    PracticePartInt
	Belly    PracticePartInt
	Pygal    PracticePartInt
	Legs     PracticePartInt
	All      PracticePartInt
}{
	All:      1, // 全身
	Shoulder: 2, // 肩背
	Chest:    3, // 胸部
	Arm:      4, // 手臂
	Belly:    5, // 腹部
	Pygal:    6, // 臀部
	Legs:     7, // 腿部
}

var PracticePartDesc = map[PracticePartInt]string{
	PracticePartEnum.Shoulder: "肩背",
	PracticePartEnum.Arm:      "手臂",
	PracticePartEnum.Chest:    "胸部",
	PracticePartEnum.Belly:    "腰腹",
	PracticePartEnum.Pygal:    "臀部",
	PracticePartEnum.Legs:     "腿部",
	PracticePartEnum.All:      "全身",
}

const (
	IsHaveApparatusYes = 1
	IsHaveApparatusNo  = 2
)

var AwardTypeDesc = map[int]string{
	ProductVipTypeVIP: ProductVipTypeDesc[ProductVipTypeVIP],
}
